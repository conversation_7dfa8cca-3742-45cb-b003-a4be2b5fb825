import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Check, Crown, Zap } from 'lucide-react';

interface Plan {
    id?: number;
    name: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_popular?: boolean;
    formatted_price?: string;
    metadata?: Record<string, unknown>;
}

interface Props {
    plans: Record<string, Plan>;
    currentPlan: string;
    remainingSearches: number;
}

export default function Plans({ plans, currentPlan, remainingSearches }: Props) {
    const handleUpgrade = (planKey: string) => {
        // Navigate to checkout page with plan selection
        router.get(route('subscription.checkout', { plan: planKey }));
    };

    return (
        <AppLayout>
            <Head title="Subscription Plans" />

            <div className="py-16 bg-gradient-to-br from-gray-50 to-white min-h-screen">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                            Choose Your Plan
                        </h1>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                            Get access to our comprehensive mobile parts database with the plan that fits your needs
                        </p>
                        {currentPlan === 'free' && (
                            <div className="mt-6">
                                <Badge variant="outline" className="text-sm px-4 py-2">
                                    {remainingSearches === -1
                                        ? 'Unlimited searches remaining'
                                        : `${remainingSearches} searches remaining today`
                                    }
                                </Badge>
                            </div>
                        )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-12 lg:gap-x-12 lg:gap-y-16 max-w-7xl mx-auto">
                        {Object.entries(plans).map(([key, plan]) => (
                            <Card
                                key={key}
                                className={`relative h-full flex flex-col transition-all duration-300 hover:shadow-xl mb-8 !py-4 ${
                                    plan.is_popular
                                        ? 'border-blue-500 shadow-xl scale-105 bg-white'
                                        : plan.metadata?.color === 'purple'
                                        ? 'border-purple-300 shadow-lg bg-gradient-to-br from-purple-50 to-white'
                                        : 'border-gray-200 shadow-md bg-white hover:border-gray-300'
                                } ${currentPlan === key ? 'ring-2 ring-green-500 ring-offset-2' : ''}`}
                            >
                                {plan.is_popular && (
                                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                                        <Badge className="bg-blue-500 text-white px-4 py-2 shadow-lg">
                                            <Crown className="w-4 h-4 mr-1" />
                                            Most Popular
                                        </Badge>
                                    </div>
                                )}

                                {plan.metadata?.color === 'purple' && !plan.is_popular && (
                                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                                        <Badge className="bg-purple-600 text-white px-4 py-2 shadow-lg">
                                            Enterprise
                                        </Badge>
                                    </div>
                                )}

                                {currentPlan === key && (
                                    <div className="absolute -top-3 right-4 z-10">
                                        <Badge className="bg-green-500 text-white px-3 py-2 shadow-lg">
                                            Current Plan
                                        </Badge>
                                    </div>
                                )}

                                <CardHeader className="text-center pb-4 pt-6">
                                    <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2 mb-4">
                                        {plan.is_popular && <Zap className="w-6 h-6 text-blue-500" />}
                                        {plan.name}
                                    </CardTitle>
                                    <div className="mb-4">
                                        <span className="text-4xl md:text-5xl font-bold text-gray-900">
                                            {plan.formatted_price || `$${plan.price}`}
                                        </span>
                                        {!plan.formatted_price && (
                                            <span className="text-lg text-gray-600 ml-1">/{plan.interval}</span>
                                        )}
                                    </div>
                                    <CardDescription className="text-base text-gray-600 leading-relaxed px-4">
                                        {plan.metadata?.color === 'purple'
                                            ? 'Custom solutions for large organizations'
                                            : plan.search_limit === -1
                                            ? 'Unlimited access for professionals'
                                            : `Perfect for occasional searches (${plan.search_limit} per day)`
                                        }
                                    </CardDescription>
                                </CardHeader>

                                <CardContent className="flex-grow px-6 py-4">
                                    <ul className="space-y-3">
                                        {plan.features.map((feature, index) => (
                                            <li key={index} className="flex items-start gap-3">
                                                <Check className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                                <span className="text-gray-700 text-base leading-relaxed">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </CardContent>

                                <CardFooter className="pt-4 px-6 pb-6">
                                    {currentPlan === key ? (
                                        <Button
                                            className="w-full h-12 text-base font-semibold"
                                            variant="outline"
                                            disabled
                                        >
                                            Current Plan
                                        </Button>
                                    ) : plan.metadata?.contact_sales ? (
                                        <Button
                                            className="w-full h-12 text-base font-semibold bg-purple-600 hover:bg-purple-700 transition-colors"
                                            onClick={() => window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank')}
                                        >
                                            Contact Sales
                                        </Button>
                                    ) : plan.price > 0 ? (
                                        <Button
                                            className="w-full h-12 text-base font-semibold bg-blue-600 hover:bg-blue-700 transition-colors"
                                            onClick={() => handleUpgrade(key)}
                                        >
                                            Get Started
                                        </Button>
                                    ) : (
                                        <Button
                                            className="w-full h-12 text-base font-semibold"
                                            variant="outline"
                                            disabled
                                        >
                                            Free Plan
                                        </Button>
                                    )}
                                </CardFooter>
                            </Card>
                        ))}
                    </div>

                    <div className="text-center mt-16">
                        <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
                            <h3 className="text-xl font-semibold text-gray-900 mb-4">
                                Need help choosing the right plan?
                            </h3>
                            <p className="text-gray-600 mb-6 leading-relaxed">
                                Our team is here to help you find the perfect solution for your needs.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Link
                                    href="#"
                                    className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                                >
                                    Contact Support
                                </Link>
                                <Link
                                    href={route('subscription.dashboard')}
                                    className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                                >
                                    Manage Subscription
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
