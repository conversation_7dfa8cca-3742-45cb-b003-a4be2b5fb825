import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import PublicLayout from '@/layouts/public-layout';
import { Head, Link, router } from '@inertiajs/react';
import {
    Calendar,
    Filter,
    Grid,
    List,
    Lock,
    Package,
    Search,
    Smartphone,
    Zap
} from 'lucide-react';
import { useState } from 'react';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country?: string;
}

interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number?: string;
    release_year?: number;
    brand: Brand;
    parts_count: number;
}

interface Filters {
    search?: string;
    release_year?: string;
}

interface Props {
    brand: Brand;
    models: MobileModel[];
    totalModelsCount: number;
    hiddenModelsCount: number;
    releaseYears: number[];
    filters: Filters;
    isSubscribed: boolean;
    hasUnlimitedAccess: boolean;
}

export default function BrandSearch({
    brand,
    models,
    totalModelsCount,
    hiddenModelsCount,
    releaseYears,
    filters,
    isSubscribed,
    hasUnlimitedAccess
}: Props) {
    const { shouldShowAds, canShowZone } = useAds();
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedYear, setSelectedYear] = useState(filters.release_year || '');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

    const handleSearch = () => {
        const params: Record<string, string> = {};
        
        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }
        
        if (selectedYear) {
            params.release_year = selectedYear;
        }

        router.get(`/brands/${brand.slug || brand.id}/search`, params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedYear('');
        router.get(`/brands/${brand.slug || brand.id}/search`, {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const renderAccessLimitedMessage = () => {
        if (hasUnlimitedAccess || hiddenModelsCount === 0) return null;

        return (
            <Card className="border-orange-200 bg-orange-50 mb-6">
                <CardContent className="pt-6">
                    <div className="flex items-center space-x-3">
                        <Lock className="h-5 w-5 text-orange-600" />
                        <div className="flex-1">
                            <h3 className="font-semibold text-orange-900">
                                {hiddenModelsCount} more models available
                            </h3>
                            <p className="text-sm text-orange-700">
                                Subscribe to view all models and get unlimited access to our database.
                            </p>
                        </div>
                        <Link href="/pricing">
                            <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                                <Zap className="h-4 w-4 mr-2" />
                                Subscribe
                            </Button>
                        </Link>
                    </div>
                </CardContent>
            </Card>
        );
    };

    const renderModelCard = (model: MobileModel) => (
        <Card key={model.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <h3 className="text-lg font-semibold mb-2">{model.name}</h3>
                        
                        <div className="space-y-2 mb-4">
                            {model.model_number && (
                                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                    <Smartphone className="h-4 w-4" />
                                    <span>Model: {model.model_number}</span>
                                </div>
                            )}
                            
                            {model.release_year && (
                                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                    <Calendar className="h-4 w-4" />
                                    <span>Released: {model.release_year}</span>
                                </div>
                            )}
                            
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Package className="h-4 w-4" />
                                <span>{model.parts_count} compatible parts</span>
                            </div>
                        </div>

                        <Link href={`/models/${model.slug || model.id}`}>
                            <Button variant="outline" size="sm">
                                View Details
                            </Button>
                        </Link>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const renderModelList = (model: MobileModel) => (
        <Card key={model.id} className="hover:shadow-sm transition-shadow">
            <CardContent className="p-4">
                <div className="flex items-center justify-between">
                    <div className="flex-1">
                        <div className="flex items-center space-x-4">
                            <div>
                                <h3 className="font-semibold">{model.name}</h3>
                                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                    {model.model_number && (
                                        <span>Model: {model.model_number}</span>
                                    )}
                                    {model.release_year && (
                                        <span>Released: {model.release_year}</span>
                                    )}
                                    <span>{model.parts_count} parts</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <Link href={`/models/${model.slug || model.id}`}>
                        <Button variant="outline" size="sm">
                            View Details
                        </Button>
                    </Link>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <PublicLayout>
            <Head title={`${brand.name} Models - Brand Search | Mobile Parts Database`} />

            {/* Header Ad Zone */}
            {shouldShowAds && canShowZone('header') && (
                <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-4 py-2">
                        <HeaderAdZone page={`/brands/${brand.slug || brand.id}`} className="w-full flex justify-center" />
                    </div>
                </div>
            )}

            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2">
                        <Link href="/" className="hover:text-primary">Home</Link>
                        <span>/</span>
                        <Link href="/brands" className="hover:text-primary">Brands</Link>
                        <span>/</span>
                        <span>{brand.name}</span>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                        {brand.logo_url && (
                            <img 
                                src={brand.logo_url} 
                                alt={brand.name}
                                className="w-12 h-12 object-contain"
                            />
                        )}
                        <div>
                            <h1 className="text-3xl font-bold">{brand.name} Models</h1>
                            <p className="text-lg text-muted-foreground">
                                Browse {totalModelsCount} models from {brand.name}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Search and Filters */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Filter className="h-5 w-5" />
                            <span>Search & Filter</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="md:col-span-2">
                                <Label htmlFor="search">Search Models</Label>
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        id="search"
                                        placeholder="Search by model name or number..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                    />
                                </div>
                            </div>
                            
                            <div>
                                <Label htmlFor="release_year">Release Year</Label>
                                <Select value={selectedYear} onValueChange={setSelectedYear}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All years" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">All years</SelectItem>
                                        {releaseYears.map((year) => (
                                            <SelectItem key={year} value={year.toString()}>
                                                {year}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div className="flex items-end space-x-2">
                                <Button onClick={handleSearch} className="flex-1">
                                    <Search className="h-4 w-4 mr-2" />
                                    Search
                                </Button>
                                <Button variant="outline" onClick={clearFilters}>
                                    Clear
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* View Mode Toggle */}
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">
                            Showing {models.length} of {totalModelsCount} models
                        </span>
                        {!hasUnlimitedAccess && hiddenModelsCount > 0 && (
                            <Badge variant="outline" className="text-orange-600 border-orange-200">
                                {hiddenModelsCount} hidden
                            </Badge>
                        )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                        <Button
                            variant={viewMode === 'grid' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('grid')}
                        >
                            <Grid className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === 'list' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('list')}
                        >
                            <List className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Access Limited Message */}
                {renderAccessLimitedMessage()}

                {/* Models Grid/List */}
                {models.length > 0 ? (
                    <div className={
                        viewMode === 'grid' 
                            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                            : "space-y-4"
                    }>
                        {models.map((model) => 
                            viewMode === 'grid' ? renderModelCard(model) : renderModelList(model)
                        )}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Smartphone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No models found</h3>
                            <p className="text-muted-foreground mb-4">
                                Try adjusting your search criteria or clear the filters.
                            </p>
                            <Button variant="outline" onClick={clearFilters}>
                                Clear Filters
                            </Button>
                        </CardContent>
                    </Card>
                )}

                {/* Content Ad Separator */}
                {shouldShowAds && canShowZone('content') && (
                    <ContentAdSeparator page={`/brands/${brand.slug || brand.id}`} spacing="my-8" />
                )}

                {/* Subscription Prompt */}
                {!hasUnlimitedAccess && (
                    <Card className="border-blue-200 bg-blue-50 mt-8">
                        <CardContent className="pt-6">
                            <div className="text-center space-y-4">
                                <Zap className="h-12 w-12 text-blue-600 mx-auto" />
                                <div>
                                    <h3 className="font-semibold text-blue-900">
                                        Get Full Access to All Models
                                    </h3>
                                    <p className="text-sm text-blue-700">
                                        Subscribe to view all {totalModelsCount} models, get unlimited search results, and access our complete parts database.
                                    </p>
                                </div>
                                <Link href="/pricing">
                                    <Button className="bg-blue-600 hover:bg-blue-700">
                                        View Pricing Plans
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Footer Ad Zone */}
                {shouldShowAds && canShowZone('footer') && (
                    <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-8">
                        <div className="max-w-7xl mx-auto px-4 py-4">
                            <FooterAdZone page={`/brands/${brand.slug || brand.id}`} className="w-full flex justify-center" />
                        </div>
                    </div>
                )}
            </div>
        </PublicLayout>
    );
}
