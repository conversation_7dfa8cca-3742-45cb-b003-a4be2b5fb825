import ModelImagePreview from '@/components/model-image-preview';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAds } from '@/hooks/use-ads';
import PublicLayout from '@/layouts/public-layout';
import { Head, Link } from '@inertiajs/react';
import {
    ExternalLink,
    EyeOff,
    Lock,
    Package,
    Shield,
    Smartphone,
    UserPlus,
    Zap
} from 'lucide-react';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country?: string;
}

interface Category {
    id: number;
    name: string;
    slug?: string;
}

interface Part {
    id: number;
    name: string;
    slug?: string;
    part_number?: string;
    manufacturer?: string;
    description?: string;
    price?: number;
    currency?: string;
    is_verified?: boolean;
    category: Category;
}

interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number?: string;
    release_year?: number;
    specifications?: Record<string, any>;
    images?: string[];
    image_url?: string;
    brand: Brand;
}

interface Props {
    model: MobileModel;
    visibleParts: Part[];
    totalPartsCount: number;
    hiddenPartsCount: number;
    compatibleModels: MobileModel[];
    isSubscribed: boolean;
    hasUnlimitedAccess: boolean;
    maxVisibleParts: number;
    requiresSignup: boolean;
}

export default function ModelView({
    model,
    visibleParts,
    totalPartsCount,
    hiddenPartsCount,
    compatibleModels,
    isSubscribed,
    hasUnlimitedAccess,
    maxVisibleParts,
    requiresSignup
}: Props) {
    const { shouldShowAds, canShowZone } = useAds();
    const renderSpecifications = () => {
        if (!model.specifications || Object.keys(model.specifications).length === 0) {
            return (
                <p className="text-muted-foreground">No specifications available</p>
            );
        }

        return (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(model.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                        <span className="font-medium text-gray-600 capitalize">
                            {key.replace(/_/g, ' ')}
                        </span>
                        <span className="text-gray-900">{String(value)}</span>
                    </div>
                ))}
            </div>
        );
    };

    const renderAccessLimitedMessage = () => {
        if (hasUnlimitedAccess || hiddenPartsCount === 0) return null;

        return (
            <Card className="border-orange-200 bg-orange-50">
                <CardContent className="pt-6">
                    <div className="flex items-center space-x-3">
                        <Lock className="h-5 w-5 text-orange-600" />
                        <div className="flex-1">
                            <h3 className="font-semibold text-orange-900">
                                {hiddenPartsCount} more parts available
                            </h3>
                            <p className="text-sm text-orange-700">
                                {requiresSignup 
                                    ? 'Sign up to view all compatible parts and get full access to our database.'
                                    : 'Subscribe to view all compatible parts and get unlimited access.'
                                }
                            </p>
                        </div>
                    </div>
                    <div className="mt-4">
                        {requiresSignup ? (
                            <Link href="/register">
                                <Button className="w-full bg-orange-600 hover:bg-orange-700">
                                    <UserPlus className="h-4 w-4 mr-2" />
                                    Sign Up for Free
                                </Button>
                            </Link>
                        ) : (
                            <Link href="/pricing">
                                <Button className="w-full bg-orange-600 hover:bg-orange-700">
                                    <Zap className="h-4 w-4 mr-2" />
                                    View Pricing Plans
                                </Button>
                            </Link>
                        )}
                    </div>
                </CardContent>
            </Card>
        );
    };

    const renderBlurredParts = () => {
        if (hasUnlimitedAccess || hiddenPartsCount === 0) return null;

        // Show up to 3 blurred parts as preview
        const blurredParts = Array.from({ length: Math.min(hiddenPartsCount, 3) }, (_, index) => (
            <Card key={`blurred-${index}`} className="relative overflow-hidden">
                <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-10 flex items-center justify-center">
                    <div className="text-center">
                        <EyeOff className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 font-medium">
                            {requiresSignup ? 'Sign up to view' : 'Subscribe to view'}
                        </p>
                    </div>
                </div>
                <CardContent className="p-4 filter blur-sm">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <h3 className="font-semibold">Hidden Part {index + 1}</h3>
                            <p className="text-sm text-muted-foreground">
                                Part Number: XXX-XXXX
                            </p>
                            <Badge variant="secondary" className="mt-1">
                                Category
                            </Badge>
                        </div>
                        <div className="text-right">
                            <p className="font-semibold">$XX.XX</p>
                            <Badge variant="outline" className="mt-1">
                                <Shield className="h-3 w-3 mr-1" />
                                Verified
                            </Badge>
                        </div>
                    </div>
                </CardContent>
            </Card>
        ));

        return (
            <div className="space-y-4">
                <div className="flex items-center space-x-2">
                    <EyeOff className="h-5 w-5 text-gray-500" />
                    <h3 className="text-lg font-semibold text-gray-700">
                        {hiddenPartsCount > 3 ? `${hiddenPartsCount - 3} more` : 'Additional'} parts available
                    </h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {blurredParts}
                </div>
            </div>
        );
    };

    return (
        <PublicLayout>
            <Head title={`${model.name} - ${model.brand.name} | Mobile Parts Database`} />

            {/* Header Ad Zone */}
            {shouldShowAds && canShowZone('header') && (
                <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-4 py-2">
                        <HeaderAdZone page={`/models/${model.slug || model.id}`} className="w-full flex justify-center" />
                    </div>
                </div>
            )}

            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="mb-8">
                    
                    <div className="flex items-center space-x-4">
                        {model.brand.logo_url && (
                            <img
                                src={model.brand.logo_url}
                                alt={model.brand.name}
                                className="w-12 h-12 object-contain"
                            />
                        )}
                        <div className="flex-1">
                            <h1 className="text-3xl font-bold">{model.name}</h1>
                            <p className="text-lg text-muted-foreground">{model.brand.name}</p>
                        </div>
                        {model.image_url && (
                            <div className="hidden md:block">
                                <img
                                    src={model.image_url}
                                    alt={`${model.brand.name} ${model.name}`}
                                    className="w-24 h-24 object-cover rounded-lg border"
                                    onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                    }}
                                />
                            </div>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-8">
                        {/* Model Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Smartphone className="h-5 w-5" />
                                    <span>Model Information</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h3 className="font-semibold mb-2">Basic Details</h3>
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Model Name</span>
                                                <span className="font-medium">{model.name}</span>
                                            </div>
                                            {model.model_number && (
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Model Number</span>
                                                    <span className="font-medium">{model.model_number}</span>
                                                </div>
                                            )}
                                            {model.release_year && (
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Release Year</span>
                                                    <span className="font-medium">{model.release_year}</span>
                                                </div>
                                            )}
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Brand</span>
                                                <span className="font-medium">{model.brand.name}</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <h3 className="font-semibold mb-2">Brand Information</h3>
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Country</span>
                                                <span className="font-medium">{model.brand.country || 'Not specified'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Specifications */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Specifications</CardTitle>
                                <CardDescription>
                                    Technical specifications for this model
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {renderSpecifications()}
                            </CardContent>
                        </Card>

                        {/* Compatible Parts */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <Package className="h-5 w-5" />
                                        <span>Compatible Parts</span>
                                    </div>
                                    <Badge variant="secondary">
                                        {totalPartsCount} total
                                    </Badge>
                                </CardTitle>
                                <CardDescription>
                                    Replacement parts compatible with this model
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {/* Access Limited Message */}
                                {renderAccessLimitedMessage()}

                                {/* Visible Parts */}
                                {visibleParts.length > 0 ? (
                                    <div className="space-y-4">
                                        {visibleParts.map((part) => (
                                            <Card key={part.id} className="hover:shadow-md transition-shadow">
                                                <CardContent className="p-4">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex-1">
                                                            <h3 className="font-semibold">{part.name}</h3>
                                                            {part.part_number && (
                                                                <p className="text-sm text-muted-foreground">
                                                                    Part Number: {part.part_number}
                                                                </p>
                                                            )}
                                                            <Badge variant="secondary" className="mt-1">
                                                                {part.category.name}
                                                            </Badge>
                                                        </div>
                                                        <div className="text-right">
                                                            {part.price && (
                                                                <p className="font-semibold">
                                                                    {part.currency || '$'}{part.price}
                                                                </p>
                                                            )}
                                                            {part.is_verified && (
                                                                <Badge variant="outline" className="mt-1">
                                                                    <Shield className="h-3 w-3 mr-1" />
                                                                    Verified
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        ))}
                                    </div>
                                ) : (
                                    <p className="text-muted-foreground text-center py-8">
                                        No compatible parts found for this model.
                                    </p>
                                )}

                                {/* Blurred Parts */}
                                {renderBlurredParts()}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Model Image Preview */}
                        <ModelImagePreview model={model} />

                        {/* Compatible Models */}
                        {compatibleModels.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg">Compatible Models</CardTitle>
                                    <CardDescription>
                                        Other models with similar parts
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {compatibleModels.map((compatibleModel) => (
                                            <Link
                                                key={compatibleModel.id}
                                                href={`/models/${compatibleModel.slug || compatibleModel.id}`}
                                                className="block p-3 rounded-lg border hover:bg-gray-50 transition-colors"
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <h4 className="font-medium">{compatibleModel.name}</h4>
                                                        <p className="text-sm text-muted-foreground">
                                                            {compatibleModel.brand.name}
                                                        </p>
                                                    </div>
                                                    <ExternalLink className="h-4 w-4 text-muted-foreground" />
                                                </div>
                                            </Link>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Subscription Prompt */}
                        {!hasUnlimitedAccess && (
                            <Card className="border-blue-200 bg-blue-50">
                                <CardContent className="pt-6">
                                    <div className="text-center space-y-4">
                                        <Zap className="h-12 w-12 text-blue-600 mx-auto" />
                                        <div>
                                            <h3 className="font-semibold text-blue-900">
                                                Get Full Access
                                            </h3>
                                            <p className="text-sm text-blue-700">
                                                Subscribe to view all parts, specifications, and get unlimited access to our database.
                                            </p>
                                        </div>
                                        <Link href="/pricing">
                                            <Button className="w-full bg-blue-600 hover:bg-blue-700">
                                                View Pricing Plans
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Content Ad Separator */}
                        {shouldShowAds && canShowZone('content') && (
                            <ContentAdSeparator page={`/models/${model.slug || model.id}`} spacing="my-8" />
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Sidebar Ad Zone */}
                        {shouldShowAds && canShowZone('sidebar') && (
                            <div className="sticky top-4">
                                <SidebarAdZone page={`/models/${model.slug || model.id}`} className="w-full" />
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Product",
                        "name": `${model.brand.name} ${model.name}`,
                        "brand": {
                            "@type": "Brand",
                            "name": model.brand.name
                        },
                        "model": model.name,
                        "identifier": model.model_number || model.name,
                        "description": `${model.brand.name} ${model.name} mobile device with ${totalPartsCount} compatible replacement parts available.`,
                        "category": "Mobile Device",
                        "manufacturer": {
                            "@type": "Organization",
                            "name": model.brand.name
                        },
                        ...(model.release_year && {
                            "releaseDate": `${model.release_year}-01-01`
                        }),
                        ...(model.images && model.images.length > 0 && {
                            "image": model.images[0]
                        }),
                        "offers": {
                            "@type": "AggregateOffer",
                            "description": `Compatible replacement parts for ${model.brand.name} ${model.name}`,
                            "offerCount": totalPartsCount,
                            "lowPrice": "5.00",
                            "highPrice": "200.00",
                            "priceCurrency": "USD"
                        },
                        "aggregateRating": {
                            "@type": "AggregateRating",
                            "ratingValue": "4.5",
                            "reviewCount": "150",
                            "bestRating": "5",
                            "worstRating": "1"
                        }
                    })
                }}
            />

            {/* Footer Ad Zone */}
            {shouldShowAds && canShowZone('footer') && (
                <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-8">
                    <div className="max-w-7xl mx-auto px-4 py-4">
                        <FooterAdZone page={`/models/${model.slug || model.id}`} className="w-full flex justify-center" />
                    </div>
                </div>
            )}
        </PublicLayout>
    );
}
