import DynamicFooter from '@/components/DynamicFooter';
import DynamicNavbar from '@/components/DynamicNavbar';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import PricingPlansSection from '@/components/PricingPlansSection';
import { ContentAdSeparator, FooterAdZone, HeaderAdZone } from '@/components/ads';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { UnifiedSearchInterface } from '@/components/unified-search-interface';
import { useAds } from '@/hooks/use-ads';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    ArrowRight,
    Clock,
    Database,
    Globe,
    Shield,
    Users,
    Zap
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface SearchStatus {
    has_searched: boolean;
    can_search: boolean;
    message: string;
    searches_used?: number;
    search_limit?: number;
    remaining_searches?: number;
}

export default function Home() {
    const { auth } = usePage<SharedData>().props;
    const { shouldShowAds, canShowZone } = useAds();
    const [searchQuery, setSearchQuery] = useState('');
    const [deviceId, setDeviceId] = useState('');
    const [searchStatus, setSearchStatus] = useState<SearchStatus | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    // Function to fetch search status
    const fetchSearchStatus = (deviceId: string, force: boolean = false) => {
        if (deviceId && !auth.user) {
            // Add timestamp to prevent caching
            const timestamp = force ? `&t=${Date.now()}` : '';
            fetch(`/guest/search/status?device_id=${deviceId}${timestamp}`)
                .then(res => res.json())
                .then(data => setSearchStatus(data))
                .catch(() => {});
        }
    };

    // Function to refresh search status (force refresh)
    const refreshSearchStatus = () => {
        if (deviceId && !auth.user) {
            fetchSearchStatus(deviceId, true);
        }
    };

    // Generate device ID on mount
    useEffect(() => {
        const generateDeviceId = () => {
            let id = localStorage.getItem('mobile_parts_device_id');
            if (!id) {
                id = 'device_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
                localStorage.setItem('mobile_parts_device_id', id);
            }
            return id;
        };

        const id = generateDeviceId();
        setDeviceId(id);
        fetchSearchStatus(id);
    }, [auth.user]);

    // Refresh search status when page becomes visible (user returns from search)
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden && deviceId) {
                fetchSearchStatus(deviceId);
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
    }, [deviceId, auth.user]);

    // Periodic refresh of search status for guest users
    useEffect(() => {
        if (!auth.user && deviceId) {
            const interval = setInterval(() => {
                refreshSearchStatus();
            }, 30000); // Refresh every 30 seconds

            return () => clearInterval(interval);
        }
    }, [deviceId, auth.user]);

    return (
        <>
            <ImpersonationBanner />
            <DynamicNavbar />
            <Head title="Mobile Parts Database - Find Compatible Parts for Any Device">
                <meta name="description" content="Search our comprehensive database of mobile phone parts. Find compatible components for repairs, replacements, and upgrades. Free search available." />
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            {/* Header Ad Zone */}
            {shouldShowAds && canShowZone('header') && (
                <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-4 py-2">
                        <HeaderAdZone page="/" className="w-full flex justify-center" />
                    </div>
                </div>
            )}

            {/* Main Content */}
            <main className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">

                {/* Hero Section */}
                <section className="relative px-4 pt-16 pb-12 sm:px-6 lg:px-8 lg:pt-24 lg:pb-16">
                    <div className="max-w-7xl mx-auto">
                        <div className="text-center">
                            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl dark:text-white">
                                Find the Right <span className="text-blue-600">Mobile Parts</span>
                                <span className="text-gray-600 dark:text-gray-300 text-2xl sm:text-3xl md:text-4xl font-normal block mt-2">
                                    for Any Device
                                </span>
                            </h1>
                            <p className="mt-6 max-w-2xl mx-auto text-lg text-gray-600 dark:text-gray-300 sm:text-xl">
                                Search our comprehensive database of mobile phone parts. Find compatible components
                                for repairs, replacements, and upgrades with detailed specifications and compatibility information.
                            </p>

                            {/* Search Status for Guests */}
                            {!auth.user && searchStatus && (
                                <div className="mt-6 max-w-md mx-auto">
                                    <Badge
                                        variant={searchStatus.has_searched ? "destructive" : "default"}
                                        className="text-sm px-3 py-1"
                                    >
                                        {searchStatus.message}
                                    </Badge>
                                </div>
                            )}
                        </div>
                    </div>
                </section>

                {/* Search Section */}
                <section className="relative px-4 pb-16 sm:px-6 lg:px-8 -mt-8">
                    <div className="max-w-4xl mx-auto">
                        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm dark:bg-gray-800/80">
                            <CardHeader className="text-center pb-6">
                                <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                                    Start Your Search
                                </CardTitle>
                                <CardDescription className="text-lg">
                                    {auth.user
                                        ? "Search unlimited parts in our database"
                                        : "Get 1 free search to explore our database"
                                    }
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <UnifiedSearchInterface
                                    searchQuery={searchQuery}
                                    setSearchQuery={setSearchQuery}
                                    deviceId={deviceId}
                                    isAuthenticated={!!auth.user}
                                    searchStatus={searchStatus}
                                    isLoading={isLoading}
                                    setIsLoading={setIsLoading}
                                    size="lg"
                                    showSuggestions={true}
                                    onSearchFocus={refreshSearchStatus}
                                />
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Content Ad Separator */}
                {shouldShowAds && canShowZone('content') && (
                    <ContentAdSeparator page="/" spacing="my-8" />
                )}

                {/* Features Section */}
                <section className="px-4 py-16 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white sm:text-4xl">
                                Why Choose FixHaat?
                            </h2>
                            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                                The most comprehensive mobile parts database for professionals and enthusiasts
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <FeatureCard
                                icon={<Database className="h-8 w-8 text-blue-600" />}
                                title="Comprehensive Database"
                                description="300+ mobile models with detailed parts information and specifications"
                            />
                            <FeatureCard
                                icon={<Shield className="h-8 w-8 text-green-600" />}
                                title="Verified Compatibility"
                                description="All part compatibility information is verified and regularly updated"
                            />
                            <FeatureCard
                                icon={<Zap className="h-8 w-8 text-yellow-600" />}
                                title="Fast Search"
                                description="Lightning-fast search results with advanced filtering options"
                            />
                            <FeatureCard
                                icon={<Users className="h-8 w-8 text-purple-600" />}
                                title="Professional Grade"
                                description="Trusted by repair shops, technicians, and parts suppliers worldwide"
                            />
                            <FeatureCard
                                icon={<Clock className="h-8 w-8 text-red-600" />}
                                title="Always Updated"
                                description="Regular updates with new models and parts as they become available"
                            />
                            <FeatureCard
                                icon={<Globe className="h-8 w-8 text-indigo-600" />}
                                title="Global Coverage"
                                description="Parts information for devices from all major manufacturers worldwide"
                            />
                        </div>
                    </div>
                </section>

                {/* Pricing Plans Section */}
                <PricingPlansSection />

                {/* Content Ad Separator */}
                {shouldShowAds && canShowZone('content') && (
                    <ContentAdSeparator page="/" spacing="my-8" />
                )}

                {/* CTA Section */}
                {!auth.user && (
                    <section className="px-4 py-16 sm:px-6 lg:px-8 bg-blue-600">
                        <div className="max-w-4xl mx-auto text-center">
                            <h2 className="text-3xl font-bold text-white sm:text-4xl">
                                Ready to Get Started?
                            </h2>
                            <p className="mt-4 text-xl text-blue-100">
                                Sign up now for unlimited access to our mobile parts database
                            </p>
                            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                                <Link href={route('register')}>
                                    <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                                        Start Free Trial
                                        <ArrowRight className="ml-2 h-5 w-5" />
                                    </Button>
                                </Link>
                                <Link href={route('login')}>
                                    <Button size="lg" variant="outline" className="border-2 border-white text-blue-600 hover:bg-white hover:text-blue-400 font-semibold">
                                        Sign In
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </section>
                )}

            </main>

            {/* Footer Ad Zone */}
            {shouldShowAds && canShowZone('footer') && (
                <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-4 py-4">
                        <FooterAdZone page="/" className="w-full flex justify-center" />
                    </div>
                </div>
            )}

            <DynamicFooter />
        </>
    );
}



// Feature Card Component
function FeatureCard({ icon, title, description }: {
    icon: React.ReactNode;
    title: string;
    description: string;
}) {
    return (
        <Card className="text-center p-6 hover:shadow-lg transition-shadow">
            <CardContent className="space-y-4">
                <div className="flex justify-center">
                    {icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                    {description}
                </p>
            </CardContent>
        </Card>
    );
}
