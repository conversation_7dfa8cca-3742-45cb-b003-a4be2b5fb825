import { Pagination } from '@/components/pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import {
    ArrowRight,
    Building2,
    Calendar,
    Eye,
    EyeOff,
    Grid,
    List,
    Lock,
    Package,
    Search,
    Smartphone,
    Star,
    Zap
} from 'lucide-react';
import React, { useState } from 'react';
import { route } from 'ziggy-js';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country?: string;
}

interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number: string | null;
    release_year: number | null;
    specifications: Record<string, any> | null;
    images: string[] | null;
    is_active: boolean;
    brand: Brand;
    parts_count?: number;
}

interface PaginatedModels {
    data: MobileModel[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    models?: PaginatedModels;
    totalModelsCount?: number;
    hiddenModelsCount?: number;
    brands: Brand[];
    releaseYears: number[];
    isSubscribed: boolean;
    hasUnlimitedAccess: boolean;
    maxVisibleResults: number;
    searchQuery?: string;
    filters?: {
        brand_id?: string;
        release_year?: string;
    };
}


export default function ModelSearch({
    models = { data: [], current_page: 1, last_page: 1, per_page: 16, total: 0, from: 0, to: 0 },
    totalModelsCount = 0,
    hiddenModelsCount = 0,
    brands,
    releaseYears,
    isSubscribed,
    hasUnlimitedAccess,
    maxVisibleResults,
    searchQuery = '',
    filters = {}
}: Props) {
    const [searchTerm, setSearchTerm] = useState(searchQuery);
    const [selectedBrand, setSelectedBrand] = useState(filters.brand_id || 'all');
    const [selectedYear, setSelectedYear] = useState(filters.release_year || 'all');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

    const handleSearch = () => {
        const params: Record<string, string> = {};

        if (searchTerm.trim()) {
            params.q = searchTerm.trim();
        }

        if (selectedBrand && selectedBrand !== 'all') {
            params.brand_id = selectedBrand;
        }

        if (selectedYear && selectedYear !== 'all') {
            params.release_year = selectedYear;
        }

        router.get('/search/model', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedBrand('all');
        setSelectedYear('all');
        router.get('/search/model');
    };

    const handlePageChange = (page: number) => {
        const params: Record<string, string> = { page: page.toString() };

        if (searchTerm.trim()) {
            params.q = searchTerm.trim();
        }

        if (selectedBrand && selectedBrand !== 'all') {
            params.brand_id = selectedBrand;
        }

        if (selectedYear && selectedYear !== 'all') {
            params.release_year = selectedYear;
        }

        router.get('/search/model', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const renderModelCard = (model: MobileModel) => (
        <Card key={model.id} className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                        {model.brand.logo_url ? (
                            <img 
                                src={model.brand.logo_url} 
                                alt={model.brand.name}
                                className="w-10 h-10 object-contain rounded"
                            />
                        ) : (
                            <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                                <Building2 className="w-5 h-5 text-gray-400" />
                            </div>
                        )}
                        <div>
                            <h3 className="font-semibold text-lg text-gray-900">
                                {model.brand.name} {model.name}
                            </h3>
                            {model.model_number && (
                                <p className="text-sm text-gray-600">
                                    Model: {model.model_number}
                                </p>
                            )}
                        </div>
                    </div>
                    {model.release_year && (
                        <Badge variant="outline" className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {model.release_year}
                        </Badge>
                    )}
                </div>

                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Badge variant="secondary" className="flex items-center gap-1">
                            <Package className="w-3 h-3" />
                            {model.parts_count || 0} parts
                        </Badge>
                    </div>
                    
                    <div className="flex items-center gap-2">
                        <Link href={route('models.show', model.slug || model.id)}>
                            <Button variant="outline" size="sm">
                                <Eye className="w-4 h-4 mr-2" />
                                View Details
                            </Button>
                        </Link>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const renderModelListItem = (model: MobileModel) => (
        <Card key={model.id} className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        {model.brand.logo_url ? (
                            <img 
                                src={model.brand.logo_url} 
                                alt={model.brand.name}
                                className="w-8 h-8 object-contain rounded"
                            />
                        ) : (
                            <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                                <Building2 className="w-4 h-4 text-gray-400" />
                            </div>
                        )}
                        <div>
                            <h3 className="font-medium text-gray-900">
                                {model.brand.name} {model.name}
                            </h3>
                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                                {model.model_number && (
                                    <span>Model: {model.model_number}</span>
                                )}
                                {model.release_year && (
                                    <span className="flex items-center gap-1">
                                        <Calendar className="w-3 h-3" />
                                        {model.release_year}
                                    </span>
                                )}
                                <span className="flex items-center gap-1">
                                    <Package className="w-3 h-3" />
                                    {model.parts_count || 0} parts
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <Link href={route('models.show', model.slug || model.id)}>
                        <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                        </Button>
                    </Link>
                </div>
            </CardContent>
        </Card>
    );

    const renderBlurredResults = () => {
        if (hiddenModelsCount === 0) return null;

        const blurredModels = Array.from({ length: Math.min(hiddenModelsCount, 3) }, (_, index) => (
            <Card key={`blurred-${index}`} className="relative overflow-hidden">
                <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-10 flex items-center justify-center">
                    <div className="text-center p-4">
                        <Lock className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm font-medium text-gray-600">
                            {!isSubscribed ? 'Sign up to view' : 'Upgrade to view'}
                        </p>
                    </div>
                </div>
                <CardContent className="p-6 filter blur-sm">
                    <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gray-200 rounded"></div>
                            <div>
                                <div className="h-5 bg-gray-200 rounded w-32 mb-1"></div>
                                <div className="h-4 bg-gray-100 rounded w-24"></div>
                            </div>
                        </div>
                        <div className="h-6 bg-gray-100 rounded w-16"></div>
                    </div>
                    <div className="flex items-center justify-between">
                        <div className="h-6 bg-gray-100 rounded w-20"></div>
                        <div className="h-8 bg-gray-200 rounded w-24"></div>
                    </div>
                </CardContent>
            </Card>
        ));

        return (
            <div className="space-y-4">
                <div className="flex items-center space-x-2">
                    <EyeOff className="h-5 w-5 text-gray-500" />
                    <h3 className="text-lg font-semibold text-gray-700">
                        {hiddenModelsCount > 3 ? `${hiddenModelsCount - 3} more` : 'Additional'} models available
                    </h3>
                </div>
                <div className={
                    viewMode === 'grid'
                        ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                        : 'space-y-4'
                }>
                    {blurredModels}
                </div>
                <Card className="border-orange-200 bg-orange-50">
                    <CardContent className="p-6 text-center">
                        <Zap className="w-12 h-12 mx-auto mb-4 text-orange-500" />
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            {!isSubscribed ? 'Sign up to view all models' : 'Upgrade for unlimited access'}
                        </h3>
                        <p className="text-gray-600 mb-6">
                            {!isSubscribed 
                                ? 'Create a free account to see more mobile device models and their specifications.'
                                : 'Upgrade to Premium to access our complete database of mobile device models.'
                            }
                        </p>
                        <div className="flex justify-center gap-3">
                            {!isSubscribed ? (
                                <>
                                    <Link href={route('register')}>
                                        <Button className="bg-orange-600 hover:bg-orange-700">
                                            Sign Up Free
                                            <ArrowRight className="w-4 h-4 ml-2" />
                                        </Button>
                                    </Link>
                                    <Link href={route('login')}>
                                        <Button variant="outline">
                                            Sign In
                                        </Button>
                                    </Link>
                                </>
                            ) : (
                                <Link href={route('subscription.plans')}>
                                    <Button className="bg-blue-600 hover:bg-blue-700">
                                        <Star className="w-4 h-4 mr-2" />
                                        Upgrade to Premium
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    };

    return (
        <AppLayout>
            <Head title="Search Models - Mobile Parts Database" />
            
            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold mb-2">Search Models</h1>
                    <p className="text-gray-600">
                        Find mobile device models and explore their compatible parts
                    </p>
                </div>

                {/* Search and Filters */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Search className="w-5 h-5" />
                            Search Models
                        </CardTitle>
                        <CardDescription>
                            Search by model name, brand, or model number
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="md:col-span-2">
                                <Label htmlFor="search">Search Term</Label>
                                <Input
                                    id="search"
                                    placeholder="Enter model name, brand, or model number..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyDown={handleKeyDown}
                                />
                            </div>
                            <div>
                                <Label htmlFor="brand">Brand</Label>
                                <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All brands" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All brands</SelectItem>
                                        {brands.map((brand) => (
                                            <SelectItem key={brand.id} value={brand.id.toString()}>
                                                {brand.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="year">Release Year</Label>
                                <Select value={selectedYear} onValueChange={setSelectedYear}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All years" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All years</SelectItem>
                                        {releaseYears.map((year) => (
                                            <SelectItem key={year} value={year.toString()}>
                                                {year}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                            <div className="flex gap-2">
                                <Button onClick={handleSearch}>
                                    <Search className="w-4 h-4 mr-2" />
                                    Search
                                </Button>
                                <Button variant="outline" onClick={clearFilters}>
                                    Clear Filters
                                </Button>
                            </div>
                            
                            {models.data.length > 0 && (
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant={viewMode === 'grid' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setViewMode('grid')}
                                    >
                                        <Grid className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'list' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setViewMode('list')}
                                    >
                                        <List className="w-4 h-4" />
                                    </Button>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Results */}
                {models.data.length > 0 ? (
                    <div className="space-y-6">
                        {/* Results Summary */}
                        <div className="flex items-center justify-between">
                            <p className="text-gray-600">
                                Showing {models.from} to {models.to} of {models.total} models
                                {hiddenModelsCount > 0 && (
                                    <span className="text-orange-600 ml-1">
                                        ({hiddenModelsCount} hidden - {!isSubscribed ? 'sign up' : 'upgrade'} to view all)
                                    </span>
                                )}
                            </p>
                        </div>

                        {/* Models Grid/List */}
                        <div className={
                            viewMode === 'grid'
                                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                                : 'space-y-4'
                        }>
                            {models.data.map(model =>
                                viewMode === 'grid'
                                    ? renderModelCard(model)
                                    : renderModelListItem(model)
                            )}
                        </div>

                        {/* Pagination */}
                        {models.last_page > 1 && (
                            <div className="mt-8">
                                <Pagination
                                    currentPage={models.current_page}
                                    lastPage={models.last_page}
                                    from={models.from}
                                    to={models.to}
                                    total={models.total}
                                    onPageChange={handlePageChange}
                                />
                            </div>
                        )}

                        {/* Blurred Results for Non-subscribers */}
                        {renderBlurredResults()}
                    </div>
                ) : searchQuery ? (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Smartphone className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                No models found
                            </h3>
                            <p className="text-gray-600 mb-6">
                                Try adjusting your search terms or filters
                            </p>
                            <Button onClick={clearFilters}>
                                <Search className="w-4 h-4 mr-2" />
                                Clear Search
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Search className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                Search Mobile Models
                            </h3>
                            <p className="text-gray-600 mb-6">
                                Enter a search term above to find mobile device models
                            </p>
                            <div className="flex justify-center gap-4">
                                <Badge variant="outline" className="text-sm">
                                    {hasUnlimitedAccess ? 'Unlimited' : `Up to ${maxVisibleResults}`} results
                                </Badge>
                                {!hasUnlimitedAccess && (
                                    <Badge variant="secondary" className="text-sm">
                                        {isSubscribed ? 'Upgrade for unlimited' : 'Sign up for more'}
                                    </Badge>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
