import { ContentAdSeparator, FooterAdZone, HeaderAdZone, SidebarAdZone } from '@/components/ads';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAds } from '@/hooks/use-ads';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import {
    ArrowLeft,
    Grid3X3,
    Search
} from 'lucide-react';

interface Category {
    id: number;
    name: string;
    slug?: string;
    description: string | null;
    parent_id: number | null;
    sort_order: number;
    is_active: boolean;
    children: Array<{
        id: number;
        name: string;
        slug?: string;
        description: string | null;
    }>;
    parts: Array<{
        id: number;
        name: string;
        slug?: string;
        part_number: string | null;
        manufacturer: string | null;
        description: string | null;
        models: Array<{
            id: number;
            name: string;
            brand: {
                id: number;
                name: string;
            };
        }>;
    }>;
}

interface Props {
    category: Category;
}

export default function CategoryDetails({ category }: Props) {
    const { shouldShowAds, canShowZone } = useAds();

    return (
        <AppLayout>
            <Head title={category.name} />

            {/* Header Ad Zone */}
            {shouldShowAds && canShowZone('header') && (
                <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-4 py-2">
                        <HeaderAdZone page={`/categories/${category.slug || category.id}`} className="w-full flex justify-center" />
                    </div>
                </div>
            )}

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Breadcrumb */}
                    <div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
                        <Link href={route('search.index')} className="hover:text-gray-900">
                            Search
                        </Link>
                        <span>/</span>
                        <span className="text-gray-900">{category.name}</span>
                    </div>

                    <div className="grid lg:grid-cols-3 gap-8">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Header */}
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-start justify-between mb-4">
                                        <div className="flex-1">
                                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                                {category.name}
                                            </h1>
                                            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                                                <Badge variant={category.is_active ? "default" : "secondary"}>
                                                    {category.is_active ? "Active" : "Inactive"}
                                                </Badge>
                                                <span>{category.parts.length} parts</span>
                                            </div>
                                        </div>
                                    </div>

                                    {category.description && (
                                        <p className="text-gray-700 leading-relaxed">
                                            {category.description}
                                        </p>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Content Ad Separator */}
                            {shouldShowAds && canShowZone('content') && (
                                <ContentAdSeparator page={`/categories/${category.slug || category.id}`} spacing="my-6" />
                            )}

                            {/* Subcategories */}
                            {category.children.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Grid3X3 className="w-5 h-5" />
                                            Subcategories ({category.children.length})
                                        </CardTitle>
                                        <CardDescription>
                                            Browse specific types within {category.name}
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {category.children.map((child) => (
                                                <Link
                                                    key={child.id}
                                                    href={route('categories.show', child.slug || child.id)}
                                                    className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                                                >
                                                    <h3 className="font-medium text-gray-900 mb-1">
                                                        {child.name}
                                                    </h3>
                                                    {child.description && (
                                                        <p className="text-sm text-gray-600">
                                                            {child.description}
                                                        </p>
                                                    )}
                                                </Link>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}


                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Category Info */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Grid3X3 className="w-5 h-5" />
                                        Category Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div>
                                            <span className="text-sm font-medium text-gray-600">Subcategories:</span>
                                            <span className="ml-2 text-gray-900">{category.children.length}</span>
                                        </div>
                                        <div>
                                            <span className="text-sm font-medium text-gray-600">Status:</span>
                                            <span className="ml-2 text-gray-900">
                                                {category.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Actions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Actions</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <Link href={route('search.category', category.slug || category.id)}>
                                            <Button className="w-full">
                                                <Search className="w-4 h-4 mr-2" />
                                                Search in {category.name}
                                            </Button>
                                        </Link>
                                        <Link href={route('search.index')}>
                                            <Button className="w-full" variant="outline">
                                                <ArrowLeft className="w-4 h-4 mr-2" />
                                                Back to Search
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Sidebar Ad Zone */}
                            {shouldShowAds && canShowZone('sidebar') && (
                                <div className="sticky top-4">
                                    <SidebarAdZone page={`/categories/${category.slug || category.id}`} className="w-full" />
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Footer Ad Zone */}
                    {shouldShowAds && canShowZone('footer') && (
                        <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-8">
                            <div className="max-w-7xl mx-auto px-4 py-4">
                                <FooterAdZone page={`/categories/${category.slug || category.id}`} className="w-full flex justify-center" />
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
