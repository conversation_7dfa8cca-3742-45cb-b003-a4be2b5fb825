import { ContentAdSeparator, FooterAdZone, HeaderAdZone, SidebarAdZone } from '@/components/ads';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAds } from '@/hooks/use-ads';
import AppLayout from '@/layouts/app-layout';
import type { SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import {
    ArrowLeft,
    Building,
    Calendar,
    Hash,
    Heart,
    Search,
    Smartphone
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface ModelSpecifications {
    [key: string]: string | number | boolean | null;
}

interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number: string | null;
    release_year: number | null;
    specifications: ModelSpecifications | null;
    images: string[] | null;
    is_active: boolean;
    brand: {
        id: number;
        name: string;
        slug?: string;
        logo_url: string | null;
    };
    parts: Array<{
        id: number;
        name: string;
        slug?: string;
        part_number: string | null;
        manufacturer: string | null;
        description: string | null;
        category: {
            id: number;
            name: string;
        };
    }>;
}

interface Props {
    model: MobileModel;
}

export default function ModelDetails({ model }: Props) {
    const { auth } = usePage<SharedData>().props;
    const { shouldShowAds, canShowZone } = useAds();
    const [isAddingToFavorites, setIsAddingToFavorites] = useState(false);
    const [isSaved, setIsSaved] = useState(false);

    const handleAddToFavorites = () => {
        // Check if user is authenticated
        if (!auth.user) {
            toast.error('Please log in to add items to favorites', {
                description: 'You need to be logged in to save favorites.',
            });
            return;
        }

        // Check if already favoriting
        if (isAddingToFavorites) {
            return;
        }

        // Check if already favorited
        if (isSaved) {
            // Backend will handle this case and show appropriate message
            return;
        }

        setIsAddingToFavorites(true);

        router.post(route('dashboard.add-favorite'), {
            type: 'model',
            id: model.id,
        }, {
            onSuccess: () => {
                setIsSaved(true);
                setIsAddingToFavorites(false);
                // Success message will be shown via backend flash message
            },
            onError: (errors) => {
                setIsAddingToFavorites(false);
                // Error messages will be shown via backend flash message
            }
        });
    };

    return (
        <AppLayout>
            <Head title={`${model.brand.name} ${model.name}`} />

            {/* Header Ad Zone */}
            {shouldShowAds && canShowZone('header') && (
                <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-4 py-2">
                        <HeaderAdZone page="/search/model-details" className="w-full flex justify-center" />
                    </div>
                </div>
            )}

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 grid lg:grid-cols-4 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-3">
                    {/* Breadcrumb */}
                    <div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
                        <Link href={route('search.index')} className="hover:text-gray-900">
                            Search
                        </Link>
                        <span>/</span>
                        <Link href={route('brands.show', model.brand.slug || model.brand.id)} className="hover:text-gray-900">
                            {model.brand.name}
                        </Link>
                        <span>/</span>
                        <span className="text-gray-900">{model.name}</span>
                    </div>

                        {/* Content Ad Separator */}
                        {shouldShowAds && canShowZone('content') && (
                            <ContentAdSeparator page="/search/model-details" spacing="my-6" />
                        )}

                        <div className="space-y-6">
                            {/* Header */}
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4 mb-4">
                                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                            {model.brand.logo_url ? (
                                                <img 
                                                    src={model.brand.logo_url} 
                                                    alt={`${model.brand.name} logo`}
                                                    className="w-full h-full object-contain"
                                                />
                                            ) : (
                                                <Smartphone className="w-8 h-8 text-gray-400" />
                                            )}
                                        </div>
                                        <div className="flex-1">
                                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                                {model.brand.name} {model.name}
                                            </h1>
                                            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                                                {model.model_number && (
                                                    <span className="flex items-center gap-1">
                                                        <Hash className="w-4 h-4" />
                                                        {model.model_number}
                                                    </span>
                                                )}
                                                {model.release_year && (
                                                    <span className="flex items-center gap-1">
                                                        <Calendar className="w-4 h-4" />
                                                        {model.release_year}
                                                    </span>
                                                )}
                                                <Badge variant={model.is_active ? "default" : "secondary"}>
                                                    {model.is_active ? "Active" : "Inactive"}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Specifications */}
                            {model.specifications && Object.keys(model.specifications).length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Specifications</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {Object.entries(model.specifications).map(([key, value]) => (
                                                <div key={key} className="flex justify-between py-2 border-b border-gray-100 last:border-0">
                                                    <span className="font-medium text-gray-600 capitalize">
                                                        {key.replace(/_/g, ' ')}:
                                                    </span>
                                                    <span className="text-gray-900">{value}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}




                        </div>

                        {/* Content Ad Separator */}
                        {shouldShowAds && canShowZone('content') && (
                            <ContentAdSeparator page="/search/model-details" spacing="my-6" />
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                            {/* Model Info */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Smartphone className="w-5 h-5" />
                                        Model Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div>
                                            <span className="text-sm font-medium text-gray-600">Brand:</span>
                                            <Link 
                                                href={route('brands.show', model.brand.slug || model.brand.id)}
                                                className="ml-2 text-blue-600 hover:text-blue-800"
                                            >
                                                {model.brand.name}
                                            </Link>
                                        </div>

                                        {model.model_number && (
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Model Number:</span>
                                                <span className="ml-2 text-gray-900">{model.model_number}</span>
                                            </div>
                                        )}
                                        {model.release_year && (
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Release Year:</span>
                                                <span className="ml-2 text-gray-900">{model.release_year}</span>
                                            </div>
                                        )}
                                        <div>
                                            <span className="text-sm font-medium text-gray-600">Categories:</span>
                                            <span className="ml-2 text-gray-900">
                                                {new Set(model.parts.map(part => part.category.name)).size}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Actions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Actions</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <Button
                                            className="w-full bg-green-600 hover:bg-green-700 text-white"
                                            onClick={handleAddToFavorites}
                                            disabled={isSaved || isAddingToFavorites}
                                        >
                                            <Heart className={`w-4 h-4 mr-2 ${isSaved ? 'fill-current' : ''}`} />
                                            {isSaved ? 'Added to Favorites' : isAddingToFavorites ? 'Adding...' : 'Add to Favorites'}
                                        </Button>

                                        <Link href={route('search.brand', model.brand.slug || model.brand.id) + `?q=${encodeURIComponent(model.name)}`}>
                                            <Button className="w-full">
                                                <Search className="w-4 h-4 mr-2" />
                                                Search {model.name} Parts
                                            </Button>
                                        </Link>

                                        <Link href={route('brands.show', model.brand.slug || model.brand.id)}>
                                            <Button className="w-full" variant="outline">
                                                <Building className="w-4 h-4 mr-2" />
                                                View Brand
                                            </Button>
                                        </Link>
                                        <Link href={route('search.index')}>
                                            <Button className="w-full" variant="outline">
                                                <ArrowLeft className="w-4 h-4 mr-2" />
                                                Back to Search
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Sidebar Ad Zone */}
                            {shouldShowAds && canShowZone('sidebar') && (
                                <div className="sticky top-4">
                                    <SidebarAdZone page="/search/model-details" className="w-full" />
                                </div>
                            )}
                        </div>
                    </div>

                {/* Footer Ad Zone */}
                {shouldShowAds && canShowZone('footer') && (
                    <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-8">
                        <div className="max-w-7xl mx-auto px-4 py-4">
                            <FooterAdZone page="/search/model-details" className="w-full flex justify-center" />
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
