import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { Head, router } from '@inertiajs/react';
import {
    Award,
    BarChart3,
    Calendar,
    Download,
    Heart,
    Package,
    Search,
    TrendingUp,
    Users
} from 'lucide-react';
import { useState } from 'react';

interface OverviewStats {
    total_favorited_parts: number;
    total_favorites: number;
    recent_favorites: number;
    unique_users: number;
    recent_unique_users: number;
    avg_favorites_per_part: number;
}

interface TopPart {
    id: number;
    name: string;
    slug?: string;
    part_number?: string;
    manufacturer?: string;
    category_name: string;
    total_favorites: number;
    unique_users: number;
    recent_favorites: number;
}

interface CategoryBreakdown {
    id: number;
    name: string;
    total_favorites: number;
    unique_parts: number;
    unique_users: number;
    recent_favorites: number;
}

interface TrendingPart {
    id: number;
    name: string;
    slug?: string;
    manufacturer?: string;
    category_name: string;
    recent_favorites: number;
    recent_unique_users: number;
}

interface SearchCorrelation {
    part_name: string;
    search_count: number;
}

interface DailyFavorite {
    date: string;
    favorites_count: number;
    unique_users: number;
    unique_parts: number;
}

interface AnalyticsData {
    overview: OverviewStats;
    top_parts: TopPart[];
    category_breakdown: CategoryBreakdown[];
    trending_parts: TrendingPart[];
    search_correlation: SearchCorrelation[];
    daily_favorites: DailyFavorite[];
}

interface Props {
    analytics: AnalyticsData;
    days: number;
}

export default function PopularPartsIndex({ analytics, days }: Props) {
    const [selectedDays, setSelectedDays] = useState(days.toString());

    const handleDaysChange = (value: string) => {
        setSelectedDays(value);
        router.get(route('admin.popular-parts.index'), { days: value });
    };

    const handleExport = () => {
        window.open(`${route('admin.popular-parts.export')}?days=${selectedDays}`, '_blank');
    };

    const formatNumber = (num: number) => {
        return new Intl.NumberFormat().format(num);
    };

    return (
        <AppLayout>
            <Head title="Popular Parts Analytics" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Popular Parts Analytics</h1>
                            <p className="text-muted-foreground mt-2">
                                Insights into which parts are most in demand based on user favorites and search activity
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Select value={selectedDays} onValueChange={handleDaysChange}>
                                <SelectTrigger className="w-32">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="7">Last 7 days</SelectItem>
                                    <SelectItem value="30">Last 30 days</SelectItem>
                                    <SelectItem value="90">Last 90 days</SelectItem>
                                    <SelectItem value="365">Last year</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button onClick={handleExport} variant="outline">
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                        </div>
                    </div>

                    {/* Overview Stats */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Favorited Parts</CardTitle>
                                <Package className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatNumber(analytics.overview.total_favorited_parts)}</div>
                                <p className="text-xs text-muted-foreground">
                                    Parts that have been favorited at least once
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Favorites</CardTitle>
                                <Heart className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatNumber(analytics.overview.total_favorites)}</div>
                                <p className="text-xs text-muted-foreground">
                                    {formatNumber(analytics.overview.recent_favorites)} in last {days} days
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                                <Users className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatNumber(analytics.overview.unique_users)}</div>
                                <p className="text-xs text-muted-foreground">
                                    {formatNumber(analytics.overview.recent_unique_users)} active in last {days} days
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Avg Favorites per Part</CardTitle>
                                <BarChart3 className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{analytics.overview.avg_favorites_per_part}</div>
                                <p className="text-xs text-muted-foreground">
                                    Average across all favorited parts
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Trending Parts</CardTitle>
                                <TrendingUp className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatNumber(analytics.trending_parts.length)}</div>
                                <p className="text-xs text-muted-foreground">
                                    Parts gaining popularity recently
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Categories</CardTitle>
                                <Calendar className="h-4 w-4 text-indigo-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatNumber(analytics.category_breakdown.length)}</div>
                                <p className="text-xs text-muted-foreground">
                                    Categories with favorited parts
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Detailed Analytics Tabs */}
                    <Tabs defaultValue="top-parts" className="space-y-4">
                        <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="top-parts">Top Parts</TabsTrigger>
                            <TabsTrigger value="categories">Categories</TabsTrigger>
                            <TabsTrigger value="trending">Trending</TabsTrigger>
                            <TabsTrigger value="search">Search Correlation</TabsTrigger>
                        </TabsList>

                        <TabsContent value="top-parts" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Award className="h-5 w-5" />
                                        Most Popular Parts
                                    </CardTitle>
                                    <CardDescription>
                                        Parts ranked by total number of favorites from users
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {analytics.top_parts.slice(0, 15).map((part, index) => (
                                            <div key={part.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex items-center justify-center w-8 h-8 bg-primary/10 text-primary rounded-full text-sm font-bold">
                                                        {index + 1}
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{part.name}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {part.category_name}
                                                            {part.manufacturer && ` • ${part.manufacturer}`}
                                                            {part.part_number && ` • ${part.part_number}`}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant="secondary" className="flex items-center gap-1">
                                                            <Heart className="h-3 w-3" />
                                                            {formatNumber(part.total_favorites)}
                                                        </Badge>
                                                        <Badge variant="outline" className="flex items-center gap-1">
                                                            <Users className="h-3 w-3" />
                                                            {formatNumber(part.unique_users)}
                                                        </Badge>
                                                    </div>
                                                    <div className="text-xs text-muted-foreground mt-1">
                                                        {formatNumber(part.recent_favorites)} recent
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="categories" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Package className="h-5 w-5" />
                                        Category Breakdown
                                    </CardTitle>
                                    <CardDescription>
                                        Favorite parts distribution across categories
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {analytics.category_breakdown.slice(0, 10).map((category, index) => (
                                            <div key={category.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-bold">
                                                        {index + 1}
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{category.name}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {formatNumber(category.unique_parts)} unique parts
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant="secondary" className="flex items-center gap-1">
                                                            <Heart className="h-3 w-3" />
                                                            {formatNumber(category.total_favorites)}
                                                        </Badge>
                                                        <Badge variant="outline" className="flex items-center gap-1">
                                                            <Users className="h-3 w-3" />
                                                            {formatNumber(category.unique_users)}
                                                        </Badge>
                                                    </div>
                                                    <div className="text-xs text-muted-foreground mt-1">
                                                        {formatNumber(category.recent_favorites)} recent
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="trending" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <TrendingUp className="h-5 w-5" />
                                        Trending Parts
                                    </CardTitle>
                                    <CardDescription>
                                        Parts gaining popularity in the last {days} days
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {analytics.trending_parts.length > 0 ? (
                                        <div className="space-y-3">
                                            {analytics.trending_parts.map((part, index) => (
                                                <div key={part.id} className="flex items-center justify-between p-3 border rounded-lg bg-gradient-to-r from-orange-50 to-red-50">
                                                    <div className="flex items-center gap-3">
                                                        <div className="flex items-center justify-center w-8 h-8 bg-orange-100 text-orange-600 rounded-full text-sm font-bold">
                                                            {index + 1}
                                                        </div>
                                                        <div>
                                                            <div className="font-medium">{part.name}</div>
                                                            <div className="text-sm text-muted-foreground">
                                                                {part.category_name}
                                                                {part.manufacturer && ` • ${part.manufacturer}`}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        <div className="flex items-center gap-2">
                                                            <Badge variant="secondary" className="flex items-center gap-1 bg-orange-100 text-orange-700">
                                                                <TrendingUp className="h-3 w-3" />
                                                                {formatNumber(part.recent_favorites)}
                                                            </Badge>
                                                            <Badge variant="outline" className="flex items-center gap-1">
                                                                <Users className="h-3 w-3" />
                                                                {formatNumber(part.recent_unique_users)}
                                                            </Badge>
                                                        </div>
                                                        <div className="text-xs text-muted-foreground mt-1">
                                                            Recent activity
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 text-muted-foreground">
                                            <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                            <p>No trending parts found in the selected time period.</p>
                                            <p className="text-sm">Try selecting a longer time period to see trending data.</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="search" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Search className="h-5 w-5" />
                                        Search Correlation
                                    </CardTitle>
                                    <CardDescription>
                                        How popular parts correlate with search activity
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {analytics.search_correlation.length > 0 ? (
                                        <div className="space-y-3">
                                            {analytics.search_correlation.map((item, index) => (
                                                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                                                    <div className="flex items-center gap-3">
                                                        <div className="flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-full text-sm font-bold">
                                                            {index + 1}
                                                        </div>
                                                        <div>
                                                            <div className="font-medium">{item.part_name}</div>
                                                            <div className="text-sm text-muted-foreground">
                                                                Popular part with search activity
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        <Badge variant="secondary" className="flex items-center gap-1">
                                                            <Search className="h-3 w-3" />
                                                            {formatNumber(item.search_count)} searches
                                                        </Badge>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 text-muted-foreground">
                                            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                            <p>No search correlation data available.</p>
                                            <p className="text-sm">Search data may not be available for the selected time period.</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
