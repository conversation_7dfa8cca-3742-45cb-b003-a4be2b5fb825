import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft } from 'lucide-react';
import React from 'react';

interface AdConfiguration {
    id: number;
    zone: string;
    enabled: boolean;
    page_types: string[];
    user_types: string[];
    ad_slot_id: string;
    ad_format: string;
    priority: number;
    frequency_rules: any;
    targeting_rules: any;
    ad_sizes: any;
}

interface Props {
    configuration: AdConfiguration;
    zones: string[];
    pageTypes: string[];
    userTypes: string[];
    adFormats: string[];
}

export default function EditAdConfiguration({ 
    configuration, 
    zones, 
    pageTypes, 
    userTypes, 
    adFormats 
}: Props) {
    const { data, setData, put, processing, errors } = useForm({
        zone: configuration.zone,
        enabled: configuration.enabled,
        page_types: configuration.page_types || [],
        user_types: configuration.user_types || [],
        ad_slot_id: configuration.ad_slot_id || '',
        ad_format: configuration.ad_format,
        priority: configuration.priority,
        frequency_rules: configuration.frequency_rules || {
            max_ads_per_page: 4,
            delay_seconds: 3,
            grace_period_minutes: 0,
        },
        targeting_rules: configuration.targeting_rules || {},
        ad_sizes: configuration.ad_sizes || {},
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.ads.update', configuration.id));
    };

    const handlePageTypesChange = (value: string) => {
        const currentTypes = data.page_types || [];
        if (currentTypes.includes(value)) {
            setData('page_types', currentTypes.filter(type => type !== value));
        } else {
            setData('page_types', [...currentTypes, value]);
        }
    };

    const handleUserTypesChange = (value: string) => {
        const currentTypes = data.user_types || [];
        if (currentTypes.includes(value)) {
            setData('user_types', currentTypes.filter(type => type !== value));
        } else {
            setData('user_types', [...currentTypes, value]);
        }
    };

    return (
        <AppLayout>
            <Head title={`Edit ${configuration.zone} Ad Configuration`} />

            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" asChild>
                        <Link href={route('admin.ads.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Ad Management
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">
                            Edit {configuration.zone.charAt(0).toUpperCase() + configuration.zone.slice(1)} Ad Configuration
                        </h1>
                        <p className="text-muted-foreground">
                            Update the configuration for this ad zone
                        </p>
                    </div>
                </div>

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle>Ad Configuration Details</CardTitle>
                        <CardDescription>
                            Update the configuration for the {configuration.zone} ad zone
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Basic Settings */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="zone">Ad Zone</Label>
                                    <Select value={data.zone} onValueChange={(value) => setData('zone', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select ad zone" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {zones.map((zone) => (
                                                <SelectItem key={zone} value={zone}>
                                                    {zone.charAt(0).toUpperCase() + zone.slice(1)}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.zone && <p className="text-sm text-red-600">{errors.zone}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="ad_format">Ad Format</Label>
                                    <Select value={data.ad_format} onValueChange={(value) => setData('ad_format', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select ad format" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {adFormats.map((format) => (
                                                <SelectItem key={format} value={format}>
                                                    {format.charAt(0).toUpperCase() + format.slice(1)}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.ad_format && <p className="text-sm text-red-600">{errors.ad_format}</p>}
                                </div>
                            </div>

                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="ad_slot_id">Ad Slot ID</Label>
                                    <Input
                                        id="ad_slot_id"
                                        value={data.ad_slot_id}
                                        onChange={(e) => setData('ad_slot_id', e.target.value)}
                                        placeholder="1234567890"
                                    />
                                    {errors.ad_slot_id && <p className="text-sm text-red-600">{errors.ad_slot_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="priority">Priority</Label>
                                    <Input
                                        id="priority"
                                        type="number"
                                        min="1"
                                        max="10"
                                        value={data.priority}
                                        onChange={(e) => setData('priority', parseInt(e.target.value))}
                                    />
                                    {errors.priority && <p className="text-sm text-red-600">{errors.priority}</p>}
                                </div>
                            </div>

                            {/* Enabled Toggle */}
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="enabled"
                                    checked={data.enabled}
                                    onCheckedChange={(checked) => setData('enabled', checked)}
                                />
                                <Label htmlFor="enabled">Enable this ad configuration</Label>
                            </div>

                            {/* Page Types */}
                            <div className="space-y-2">
                                <Label>Page Types</Label>
                                <div className="grid gap-2 md:grid-cols-3">
                                    {pageTypes.map((pageType) => (
                                        <div key={pageType} className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                id={`page_${pageType}`}
                                                checked={data.page_types?.includes(pageType) || false}
                                                onChange={() => handlePageTypesChange(pageType)}
                                                className="rounded border-gray-300"
                                            />
                                            <Label htmlFor={`page_${pageType}`} className="text-sm">
                                                {pageType.charAt(0).toUpperCase() + pageType.slice(1)}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                                {errors.page_types && <p className="text-sm text-red-600">{errors.page_types}</p>}
                            </div>

                            {/* User Types */}
                            <div className="space-y-2">
                                <Label>User Types</Label>
                                <div className="grid gap-2 md:grid-cols-2">
                                    {userTypes.map((userType) => (
                                        <div key={userType} className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                id={`user_${userType}`}
                                                checked={data.user_types?.includes(userType) || false}
                                                onChange={() => handleUserTypesChange(userType)}
                                                className="rounded border-gray-300"
                                            />
                                            <Label htmlFor={`user_${userType}`} className="text-sm">
                                                {userType.charAt(0).toUpperCase() + userType.slice(1)}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                                {errors.user_types && <p className="text-sm text-red-600">{errors.user_types}</p>}
                            </div>

                            {/* Frequency Rules */}
                            <div className="space-y-4">
                                <Label>Frequency Rules</Label>
                                <div className="grid gap-4 md:grid-cols-3">
                                    <div className="space-y-2">
                                        <Label htmlFor="max_ads">Max Ads Per Page</Label>
                                        <Input
                                            id="max_ads"
                                            type="number"
                                            min="1"
                                            max="10"
                                            value={data.frequency_rules?.max_ads_per_page || 4}
                                            onChange={(e) => setData('frequency_rules', {
                                                ...data.frequency_rules,
                                                max_ads_per_page: parseInt(e.target.value)
                                            })}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="delay_seconds">Delay (seconds)</Label>
                                        <Input
                                            id="delay_seconds"
                                            type="number"
                                            min="0"
                                            max="60"
                                            value={data.frequency_rules?.delay_seconds || 3}
                                            onChange={(e) => setData('frequency_rules', {
                                                ...data.frequency_rules,
                                                delay_seconds: parseInt(e.target.value)
                                            })}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="grace_period">Grace Period (minutes)</Label>
                                        <Input
                                            id="grace_period"
                                            type="number"
                                            min="0"
                                            max="1440"
                                            value={data.frequency_rules?.grace_period_minutes || 0}
                                            onChange={(e) => setData('frequency_rules', {
                                                ...data.frequency_rules,
                                                grace_period_minutes: parseInt(e.target.value)
                                            })}
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <div className="flex items-center gap-4">
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Updating...' : 'Update Configuration'}
                                </Button>
                                <Button variant="outline" asChild>
                                    <Link href={route('admin.ads.index')}>Cancel</Link>
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
