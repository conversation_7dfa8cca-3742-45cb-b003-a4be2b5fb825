import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import {
    DollarSign,
    Eye,
    MoreHorizontal,
    MousePointer,
    Plus,
    RefreshCw,
    Settings,
    TrendingUp
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import AdSenseConfigTab from './adsense';

interface AdConfiguration {
    id: number;
    zone: string;
    enabled: boolean;
    page_types: string[];
    user_types: string[];
    ad_slot_id: string;
    ad_format: string;
    priority: number;
    frequency_rules: any;
    targeting_rules: any;
    created_at: string;
    updated_at: string;
}

interface Analytics {
    summary: {
        total_impressions: number;
        total_clicks: number;
        total_revenue: number;
        avg_ctr: number;
        avg_cpm: number;
        days_active: number;
    };
    trends: any[];
    period: {
        start_date: string;
        end_date: string;
        days: number;
    };
}

interface AdSenseConfig {
    enabled: boolean;
    client_id: string;
    auto_ads: boolean;
    debug: boolean;
    zones: {
        header_enabled: boolean;
        sidebar_enabled: boolean;
        content_enabled: boolean;
        footer_enabled: boolean;
        mobile_enabled: boolean;
    };
    frequency: {
        max_ads_per_page: number;
        delay_seconds: number;
        grace_period_minutes: number;
    };
}

interface AdSenseStatus {
    configured: boolean;
    enabled: boolean;
    client_id_valid: boolean;
    auto_ads_enabled: boolean;
    debug_mode: boolean;
    zones_enabled: Record<string, boolean>;
    status: 'healthy' | 'error';
    last_updated: string;
}

interface Props {
    configurations: AdConfiguration[];
    analytics: Analytics;
    zoneComparison: Record<string, any>;
    recentPerformance: any[];
    adSenseConfig: AdSenseConfig;
    adSenseStatus: AdSenseStatus;
    filters: {
        start_date: string;
        end_date: string;
    };
}

export default function AdManagementIndex({
    configurations,
    analytics,
    zoneComparison,
    recentPerformance,
    adSenseConfig,
    adSenseStatus,
    filters
}: Props) {
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [configToDelete, setConfigToDelete] = useState<AdConfiguration | null>(null);

    const handleToggleEnabled = async (config: AdConfiguration) => {
        try {
            await router.post(route('admin.ads.toggle', config.id), {}, {
                preserveScroll: true,
                onSuccess: () => {
                    // Flash success message will be handled by FlashMessageHandler component
                },
                onError: () => {
                    toast.error('Failed to toggle ad zone');
                }
            });
        } catch (error) {
            toast.error('Failed to toggle ad zone');
        }
    };

    const handleDelete = async () => {
        if (!configToDelete) return;

        try {
            await router.delete(route('admin.ads.destroy', configToDelete.id), {
                onSuccess: () => {
                    toast.success('Ad configuration deleted successfully');
                    setDeleteDialogOpen(false);
                    setConfigToDelete(null);
                },
                onError: () => {
                    toast.error('Failed to delete ad configuration');
                }
            });
        } catch (error) {
            toast.error('Failed to delete ad configuration');
        }
    };

    const handleInitializeDefaults = async () => {
        try {
            await router.post(route('admin.ads.initialize-defaults'), {}, {
                onSuccess: () => {
                    toast.success('Default ad configurations initialized successfully');
                },
                onError: () => {
                    toast.error('Failed to initialize default configurations');
                }
            });
        } catch (error) {
            toast.error('Failed to initialize default configurations');
        }
    };

    const handleClearCache = async () => {
        try {
            await router.post(route('admin.ads.clear-cache'), {}, {
                onSuccess: () => {
                    toast.success('Ad cache cleared successfully');
                },
                onError: () => {
                    toast.error('Failed to clear cache');
                }
            });
        } catch (error) {
            toast.error('Failed to clear cache');
        }
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
        }).format(amount);
    };

    const formatNumber = (num: number) => {
        return new Intl.NumberFormat('en-US').format(num);
    };

    const formatDays = (days: number) => {
        return Math.round(days);
    };

    const getZoneBadgeColor = (zone: string) => {
        const colors = {
            header: 'bg-blue-100 text-blue-800',
            sidebar: 'bg-green-100 text-green-800',
            content: 'bg-purple-100 text-purple-800',
            footer: 'bg-orange-100 text-orange-800',
            mobile: 'bg-pink-100 text-pink-800',
        };
        return colors[zone as keyof typeof colors] || 'bg-gray-100 text-gray-800';
    };

    return (
        <AppLayout>
            <Head title="Ad Management" />

            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Ad Management</h1>
                        <p className="text-muted-foreground">
                            Manage Google AdSense configurations and monitor performance
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={handleClearCache}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Clear Cache
                        </Button>
                        <Button variant="outline" onClick={handleInitializeDefaults}>
                            <Settings className="h-4 w-4 mr-2" />
                            Initialize Defaults
                        </Button>
                        <Button asChild>
                            <Link href={route('admin.ads.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Configuration
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Tabs */}
                <Tabs defaultValue="configurations" className="space-y-6">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="configurations">Ad Configurations</TabsTrigger>
                        <TabsTrigger value="adsense">AdSense Configuration</TabsTrigger>
                    </TabsList>

                    <TabsContent value="configurations" className="space-y-6">
                        {/* Analytics Summary */}
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
                            <Eye className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {formatNumber(analytics.summary.total_impressions)}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Last {formatDays(analytics.period.days)} days
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
                            <MousePointer className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {formatNumber(analytics.summary.total_clicks)}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                {analytics.summary.avg_ctr.toFixed(2)}% CTR
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {formatCurrency(analytics.summary.total_revenue)}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                ${analytics.summary.avg_cpm.toFixed(2)} CPM
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Days</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {analytics.summary.days_active}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Out of {formatDays(analytics.period.days)} days
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Ad Configurations Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Ad Configurations</CardTitle>
                        <CardDescription>
                            Manage ad zones and their settings
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Zone</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Pages</TableHead>
                                    <TableHead>Users</TableHead>
                                    <TableHead>Format</TableHead>
                                    <TableHead>Priority</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {configurations.map((config) => (
                                    <TableRow key={config.id}>
                                        <TableCell>
                                            <Badge className={getZoneBadgeColor(config.zone)}>
                                                {config.zone}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <Switch
                                                checked={config.enabled}
                                                onCheckedChange={() => handleToggleEnabled(config)}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-wrap gap-1">
                                                {config.page_types?.map((page) => (
                                                    <Badge key={page} variant="outline" className="text-xs">
                                                        {page}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-wrap gap-1">
                                                {config.user_types?.map((user) => (
                                                    <Badge key={user} variant="outline" className="text-xs">
                                                        {user}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </TableCell>
                                        <TableCell>{config.ad_format}</TableCell>
                                        <TableCell>{config.priority}</TableCell>
                                        <TableCell>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem asChild>
                                                        <Link href={route('admin.ads.edit', config.id)}>
                                                            Edit
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => {
                                                            setConfigToDelete(config);
                                                            setDeleteDialogOpen(true);
                                                        }}
                                                        className="text-red-600"
                                                    >
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
                    </TabsContent>

                    <TabsContent value="adsense" className="space-y-6">
                        <AdSenseConfigTab
                            config={adSenseConfig}
                            status={adSenseStatus}
                        />
                    </TabsContent>
                </Tabs>
            </div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the ad configuration
                            for the {configToDelete?.zone} zone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
