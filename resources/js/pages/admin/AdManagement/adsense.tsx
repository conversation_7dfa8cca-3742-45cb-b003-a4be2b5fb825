import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useForm } from '@inertiajs/react';
import { AlertCircle, CheckCircle, Settings, TestTube } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface AdSenseConfig {
    enabled: boolean;
    client_id: string;
    auto_ads: boolean;
    debug: boolean;
    zones: {
        header_enabled: boolean;
        sidebar_enabled: boolean;
        content_enabled: boolean;
        footer_enabled: boolean;
        mobile_enabled: boolean;
    };
    frequency: {
        max_ads_per_page: number;
        delay_seconds: number;
        grace_period_minutes: number;
    };
}

interface AdSenseStatus {
    configured: boolean;
    enabled: boolean;
    client_id_valid: boolean;
    auto_ads_enabled: boolean;
    debug_mode: boolean;
    zones_enabled: Record<string, boolean>;
    status: 'healthy' | 'error';
    last_updated: string;
}

interface Props {
    config: AdSenseConfig;
    status: AdSenseStatus;
}

export default function AdSenseConfigTab({ config, status }: Props) {
    const [testing, setTesting] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        enabled: config.enabled,
        client_id: config.client_id,
        auto_ads: config.auto_ads,
        debug: config.debug,
        zones: {
            header_enabled: config.zones.header_enabled,
            sidebar_enabled: config.zones.sidebar_enabled,
            content_enabled: config.zones.content_enabled,
            footer_enabled: config.zones.footer_enabled,
            mobile_enabled: config.zones.mobile_enabled,
        },
        frequency: {
            max_ads_per_page: config.frequency.max_ads_per_page,
            delay_seconds: config.frequency.delay_seconds,
            grace_period_minutes: config.frequency.grace_period_minutes,
        },
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.ads.adsense-config.update'), {
            onSuccess: () => {
                toast.success('AdSense configuration updated successfully');
            },
            onError: () => {
                toast.error('Failed to update AdSense configuration');
            },
        });
    };

    const handleTest = async () => {
        setTesting(true);
        try {
            const response = await fetch(route('admin.ads.adsense-config.test'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify(data),
            });

            const result = await response.json();
            
            if (result.success) {
                toast.success(result.message);
            } else {
                toast.error(result.message);
            }
        } catch (error) {
            toast.error('Failed to test AdSense configuration');
        } finally {
            setTesting(false);
        }
    };

    const getStatusIcon = () => {
        if (status.status === 'healthy') {
            return <CheckCircle className="h-5 w-5 text-green-500" />;
        }
        return <AlertCircle className="h-5 w-5 text-red-500" />;
    };

    const getStatusText = () => {
        if (status.status === 'healthy') {
            return 'Configuration is valid and healthy';
        }
        return 'Configuration has issues that need attention';
    };

    return (
        <div className="space-y-6">
            {/* Status Card */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        {getStatusIcon()}
                        AdSense Status
                    </CardTitle>
                    <CardDescription>
                        {getStatusText()}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div className="space-y-1">
                            <p className="text-sm font-medium">Configured</p>
                            <p className="text-sm text-muted-foreground">
                                {status.configured ? 'Yes' : 'No'}
                            </p>
                        </div>
                        <div className="space-y-1">
                            <p className="text-sm font-medium">Enabled</p>
                            <p className="text-sm text-muted-foreground">
                                {status.enabled ? 'Yes' : 'No'}
                            </p>
                        </div>
                        <div className="space-y-1">
                            <p className="text-sm font-medium">Client ID Valid</p>
                            <p className="text-sm text-muted-foreground">
                                {status.client_id_valid ? 'Yes' : 'No'}
                            </p>
                        </div>
                        <div className="space-y-1">
                            <p className="text-sm font-medium">Auto Ads</p>
                            <p className="text-sm text-muted-foreground">
                                {status.auto_ads_enabled ? 'Enabled' : 'Disabled'}
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Configuration Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Global Settings Card */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Settings className="h-5 w-5" />
                            Global Settings
                        </CardTitle>
                        <CardDescription>
                            Configure your Google AdSense global settings
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Enable AdSense */}
                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <Label htmlFor="enabled">Enable AdSense</Label>
                                <p className="text-sm text-muted-foreground">
                                    Turn on/off Google AdSense for your site
                                </p>
                            </div>
                            <Switch
                                id="enabled"
                                checked={data.enabled}
                                onCheckedChange={(checked) => setData('enabled', checked)}
                            />
                        </div>

                        {/* Client ID */}
                        <div className="space-y-2">
                            <Label htmlFor="client_id">AdSense Client ID</Label>
                            <Input
                                id="client_id"
                                type="text"
                                placeholder="ca-pub-xxxxxxxxxxxxxxxx"
                                value={data.client_id}
                                onChange={(e) => setData('client_id', e.target.value)}
                                className={errors.client_id ? 'border-red-500' : ''}
                            />
                            {errors.client_id && (
                                <p className="text-sm text-red-500">{errors.client_id}</p>
                            )}
                            <p className="text-sm text-muted-foreground">
                                Your Google AdSense publisher ID (format: ca-pub-xxxxxxxxxxxxxxxx)
                            </p>
                        </div>

                        {/* Auto Ads */}
                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <Label htmlFor="auto_ads">Auto Ads</Label>
                                <p className="text-sm text-muted-foreground">
                                    Let Google automatically place ads on your site
                                </p>
                            </div>
                            <Switch
                                id="auto_ads"
                                checked={data.auto_ads}
                                onCheckedChange={(checked) => setData('auto_ads', checked)}
                            />
                        </div>

                        {/* Debug Mode */}
                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <Label htmlFor="debug">Debug Mode</Label>
                                <p className="text-sm text-muted-foreground">
                                    Enable debug mode for testing (development only)
                                </p>
                            </div>
                            <Switch
                                id="debug"
                                checked={data.debug}
                                onCheckedChange={(checked) => setData('debug', checked)}
                            />
                        </div>
                    </CardContent>
                </Card>

                {/* Zone Configuration */}
                <Card>
                    <CardHeader>
                        <CardTitle>Zone Configuration</CardTitle>
                        <CardDescription>
                            Configure which zones are enabled for displaying ads
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-2">
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="header_enabled">Header Zone</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Display ads in the header area
                                    </p>
                                </div>
                                <Switch
                                    id="header_enabled"
                                    checked={data.zones.header_enabled}
                                    onCheckedChange={(checked) =>
                                        setData('zones', { ...data.zones, header_enabled: checked })
                                    }
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="sidebar_enabled">Sidebar Zone</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Display ads in the sidebar area
                                    </p>
                                </div>
                                <Switch
                                    id="sidebar_enabled"
                                    checked={data.zones.sidebar_enabled}
                                    onCheckedChange={(checked) =>
                                        setData('zones', { ...data.zones, sidebar_enabled: checked })
                                    }
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="content_enabled">Content Zone</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Display ads within content areas
                                    </p>
                                </div>
                                <Switch
                                    id="content_enabled"
                                    checked={data.zones.content_enabled}
                                    onCheckedChange={(checked) =>
                                        setData('zones', { ...data.zones, content_enabled: checked })
                                    }
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="footer_enabled">Footer Zone</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Display ads in the footer area
                                    </p>
                                </div>
                                <Switch
                                    id="footer_enabled"
                                    checked={data.zones.footer_enabled}
                                    onCheckedChange={(checked) =>
                                        setData('zones', { ...data.zones, footer_enabled: checked })
                                    }
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="mobile_enabled">Mobile Zone</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Display ads on mobile devices
                                    </p>
                                </div>
                                <Switch
                                    id="mobile_enabled"
                                    checked={data.zones.mobile_enabled}
                                    onCheckedChange={(checked) =>
                                        setData('zones', { ...data.zones, mobile_enabled: checked })
                                    }
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Frequency Settings */}
                <Card>
                    <CardHeader>
                        <CardTitle>Frequency Settings</CardTitle>
                        <CardDescription>
                            Configure frequency limits and timing for ad display
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-3">
                            <div className="space-y-2">
                                <Label htmlFor="max_ads_per_page">Max Ads Per Page</Label>
                                <Input
                                    id="max_ads_per_page"
                                    type="number"
                                    min="1"
                                    max="10"
                                    value={data.frequency.max_ads_per_page}
                                    onChange={(e) =>
                                        setData('frequency', {
                                            ...data.frequency,
                                            max_ads_per_page: parseInt(e.target.value) || 1
                                        })
                                    }
                                />
                                <p className="text-sm text-muted-foreground">
                                    Maximum number of ads per page (1-10)
                                </p>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="delay_seconds">Delay (seconds)</Label>
                                <Input
                                    id="delay_seconds"
                                    type="number"
                                    min="0"
                                    max="30"
                                    value={data.frequency.delay_seconds}
                                    onChange={(e) =>
                                        setData('frequency', {
                                            ...data.frequency,
                                            delay_seconds: parseInt(e.target.value) || 0
                                        })
                                    }
                                />
                                <p className="text-sm text-muted-foreground">
                                    Delay before showing ads (0-30 seconds)
                                </p>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="grace_period_minutes">Grace Period (minutes)</Label>
                                <Input
                                    id="grace_period_minutes"
                                    type="number"
                                    min="0"
                                    max="60"
                                    value={data.frequency.grace_period_minutes}
                                    onChange={(e) =>
                                        setData('frequency', {
                                            ...data.frequency,
                                            grace_period_minutes: parseInt(e.target.value) || 0
                                        })
                                    }
                                />
                                <p className="text-sm text-muted-foreground">
                                    Grace period for new users (0-60 minutes)
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Form Actions */}
                <div className="flex items-center justify-end gap-3 pt-6 border-t border-border">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={handleTest}
                        disabled={testing || processing}
                        className="min-w-[140px]"
                    >
                        <TestTube className="h-4 w-4 mr-2" />
                        {testing ? 'Testing...' : 'Test Configuration'}
                    </Button>
                    <Button
                        type="submit"
                        disabled={processing}
                        className="min-w-[140px]"
                    >
                        {processing ? 'Saving...' : 'Save Configuration'}
                    </Button>
                </div>
            </form>
        </div>
    );
}
