import ImpersonationSecurityCheck from '@/components/ImpersonationSecurityCheck';
import { StatCard, statCardColors } from '@/components/StatCard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserStatusDisplay } from '@/components/UserStatusDisplay';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import {
    Ban,
    CheckCircle,
    ChevronLeft,
    ChevronRight,
    Clock,
    Eye,
    Filter,
    Mail,
    MailCheck,
    MoreHorizontal,
    Search,
    Shield,
    UserCheck,
    UserPlus,
    Users,
    UserX
} from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    role: 'user' | 'content_manager' | 'admin';
    status: 'active' | 'pending' | 'suspended' | 'banned';
    approval_status: 'pending' | 'approved' | 'rejected';
    subscription_plan: 'free' | 'premium';
    created_at: string;
    last_login_at: string | null;
    login_count: number;
    searches_count: number;
    payment_requests_count: number;
    activity_logs_count: number;
    approved_by?: {
        name: string;
    };
    suspended_by?: {
        name: string;
    };
    suspension_reason?: string;
}

interface Stats {
    total_users: number;
    active_users: number;
    pending_approval: number;
    suspended_users: number;
    premium_users: number;
    email_verified: number;
    email_unverified: number;
    fully_active: number;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface Props {
    users: {
        data: User[];
        links: PaginationLink[];
        meta: PaginationMeta;
    };
    stats: Stats;
    filters: {
        search?: string;
        status?: string;
        approval_status?: string;
        subscription_plan?: string;
        date_from?: string;
        date_to?: string;
        sort_by?: string;
        sort_order?: string;
    };
}

const StatusBadge = ({ status }: { status: User['status'] }) => {
    const variants = {
        active: 'bg-green-100 text-green-800',
        pending: 'bg-yellow-100 text-yellow-800',
        suspended: 'bg-red-100 text-red-800',
        banned: 'bg-gray-100 text-gray-800',
    };

    return (
        <Badge className={variants[status]}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
    );
};

const ApprovalBadge = ({ status }: { status: User['approval_status'] }) => {
    const variants = {
        approved: 'bg-green-100 text-green-800',
        pending: 'bg-yellow-100 text-yellow-800',
        rejected: 'bg-red-100 text-red-800',
    };

    return (
        <Badge className={variants[status]}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
    );
};

const EmailVerificationBadge = ({ emailVerifiedAt }: { emailVerifiedAt: string | null }) => {
    const isVerified = emailVerifiedAt !== null;

    return (
        <Badge className={isVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
            {isVerified ? 'Verified' : 'Unverified'}
        </Badge>
    );
};

export default function UsersIndex({ users, stats, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || 'all');
    const [selectedApprovalStatus, setSelectedApprovalStatus] = useState(filters.approval_status || 'all');
    const [selectedSubscriptionPlan, setSelectedSubscriptionPlan] = useState(filters.subscription_plan || 'all');
    const [showImpersonationDialog, setShowImpersonationDialog] = useState(false);
    const [selectedUserForImpersonation, setSelectedUserForImpersonation] = useState<User | null>(null);

    const handlePageChange = (page: number) => {
        router.get('/admin/users', {
            ...filters,
            page,
            search: searchTerm,
            status: selectedStatus === 'all' ? '' : selectedStatus,
            approval_status: selectedApprovalStatus === 'all' ? '' : selectedApprovalStatus,
            subscription_plan: selectedSubscriptionPlan === 'all' ? '' : selectedSubscriptionPlan,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSearch = () => {
        router.get('/admin/users', {
            ...filters,
            search: searchTerm,
            status: selectedStatus === 'all' ? '' : selectedStatus,
            approval_status: selectedApprovalStatus === 'all' ? '' : selectedApprovalStatus,
            subscription_plan: selectedSubscriptionPlan === 'all' ? '' : selectedSubscriptionPlan,
        });
    };

    const handleApprove = (userId: number) => {
        router.post(`/admin/users/${userId}/approve`, {}, {
            preserveScroll: true,
        });
    };

    const handleSuspend = (userId: number) => {
        const reason = prompt('Please provide a reason for suspension:');
        if (reason) {
            router.post(`/admin/users/${userId}/suspend`, {
                reason,
            }, {
                preserveScroll: true,
            });
        }
    };

    const handleUnsuspend = (userId: number) => {
        router.post(`/admin/users/${userId}/unsuspend`, {}, {
            preserveScroll: true,
        });
    };

    const handleImpersonate = (user: User) => {
        setSelectedUserForImpersonation(user);
        setShowImpersonationDialog(true);
    };

    const handleImpersonationConfirm = (reason: string, duration: number) => {
        if (!selectedUserForImpersonation) return;

        router.post(`/admin/impersonate/${selectedUserForImpersonation.id}`, {
            reason: reason,
            duration: duration,
        }, {
            onSuccess: () => {
                setShowImpersonationDialog(false);
                setSelectedUserForImpersonation(null);
            },
            onError: () => {
                setShowImpersonationDialog(false);
                setSelectedUserForImpersonation(null);
            }
        });
    };

    return (
        <AppLayout>
            <Head title="User Management" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">User Management</h1>
                            <p className="text-muted-foreground mt-2">
                                Manage user accounts, approvals, and permissions
                            </p>
                        </div>
                        <Link href="/admin/users/create">
                            <Button>
                                <UserPlus className="h-4 w-4 mr-2" />
                                Create User
                            </Button>
                        </Link>
                    </div>

                    {/* Stats Grid - 2 rows, 4 cards each */}
                    <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 xl:grid-cols-4">
                        {/* First Row */}
                        <StatCard
                            title="Total Users"
                            value={stats.total_users}
                            icon={Users}
                            gradient={statCardColors.primary.gradient}
                            iconBg={statCardColors.primary.iconBg}
                        />

                        <StatCard
                            title="Active Users"
                            value={stats.active_users}
                            icon={UserCheck}
                            gradient={statCardColors.success.gradient}
                            iconBg={statCardColors.success.iconBg}
                        />

                        <StatCard
                            title="Pending Approval"
                            value={stats.pending_approval}
                            icon={Clock}
                            gradient={statCardColors.warning.gradient}
                            iconBg={statCardColors.warning.iconBg}
                        />

                        <StatCard
                            title="Suspended"
                            value={stats.suspended_users}
                            icon={UserX}
                            gradient={statCardColors.danger.gradient}
                            iconBg={statCardColors.danger.iconBg}
                        />

                        {/* Second Row */}
                        <StatCard
                            title="Premium Users"
                            value={stats.premium_users}
                            icon={UserPlus}
                            gradient={statCardColors.premium.gradient}
                            iconBg={statCardColors.premium.iconBg}
                        />

                        <StatCard
                            title="Email Verified"
                            value={stats.email_verified}
                            icon={MailCheck}
                            gradient={statCardColors.verified.gradient}
                            iconBg={statCardColors.verified.iconBg}
                        />

                        <StatCard
                            title="Email Unverified"
                            value={stats.email_unverified}
                            icon={Mail}
                            gradient={statCardColors.info.gradient}
                            iconBg={statCardColors.info.iconBg}
                        />

                        <StatCard
                            title="Fully Active"
                            value={stats.fully_active}
                            icon={Shield}
                            gradient={statCardColors.active.gradient}
                            iconBg={statCardColors.active.iconBg}
                        />
                    </div>

                    {/* Filters */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filters
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                                <div className="flex gap-2">
                                    <Input
                                        placeholder="Search users..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="flex-1"
                                    />
                                    <Button onClick={handleSearch}>
                                        <Search className="h-4 w-4" />
                                    </Button>
                                </div>

                                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Statuses</SelectItem>
                                        <SelectItem value="active">Active</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                        <SelectItem value="suspended">Suspended</SelectItem>
                                        <SelectItem value="banned">Banned</SelectItem>
                                    </SelectContent>
                                </Select>

                                <Select value={selectedApprovalStatus} onValueChange={setSelectedApprovalStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Approval Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Approval Statuses</SelectItem>
                                        <SelectItem value="approved">Approved</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                        <SelectItem value="rejected">Rejected</SelectItem>
                                    </SelectContent>
                                </Select>

                                <Select value={selectedSubscriptionPlan} onValueChange={setSelectedSubscriptionPlan}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Subscription Plan" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Plans</SelectItem>
                                        <SelectItem value="free">Free</SelectItem>
                                        <SelectItem value="premium">Premium</SelectItem>
                                    </SelectContent>
                                </Select>

                                <Button onClick={handleSearch} className="w-full">
                                    Apply Filters
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Users Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Users</CardTitle>
                            <CardDescription>
                                {users.meta?.total || users.data?.length || 0} users found
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    {/* Table Header */}
                                    <thead>
                                        <tr className="border-b border-border">
                                            <th className="text-left py-3 px-4 font-medium text-sm text-muted-foreground">Name</th>
                                            <th className="text-left py-3 px-4 font-medium text-sm text-muted-foreground">Email</th>
                                            <th className="text-left py-3 px-4 font-medium text-sm text-muted-foreground">Account Status</th>
                                            <th className="text-left py-3 px-4 font-medium text-sm text-muted-foreground">Searches</th>
                                            <th className="text-left py-3 px-4 font-medium text-sm text-muted-foreground">Logins</th>
                                            <th className="text-left py-3 px-4 font-medium text-sm text-muted-foreground">Joined</th>
                                            <th className="text-right py-3 px-4 font-medium text-sm text-muted-foreground">Actions</th>
                                        </tr>
                                    </thead>

                                    {/* Table Body */}
                                    <tbody>
                                        {users.data?.map((user) => (
                                            <tr key={user.id} className="border-b border-border hover:bg-accent/50 transition-colors">
                                                {/* Name */}
                                                <td className="py-3 px-4">
                                                    <div className="flex items-center gap-2">
                                                        <p className="text-sm font-medium text-foreground">
                                                            {user.name}
                                                        </p>
                                                        {user.subscription_plan === 'premium' && (
                                                            <Badge className="bg-purple-100 text-purple-800 text-xs">Premium</Badge>
                                                        )}
                                                    </div>
                                                </td>

                                                {/* Email */}
                                                <td className="py-3 px-4">
                                                    <p className="text-sm text-muted-foreground">
                                                        {user.email}
                                                    </p>
                                                </td>

                                                {/* Comprehensive Status */}
                                                <td className="py-3 px-4">
                                                    <UserStatusDisplay user={user} variant="compact" />
                                                </td>

                                                {/* Searches */}
                                                <td className="py-3 px-4">
                                                    <p className="text-sm text-foreground">
                                                        {user.searches_count}
                                                    </p>
                                                </td>

                                                {/* Logins */}
                                                <td className="py-3 px-4">
                                                    <p className="text-sm text-foreground">
                                                        {user.login_count}
                                                    </p>
                                                </td>

                                                {/* Joined Date */}
                                                <td className="py-3 px-4">
                                                    <p className="text-sm text-muted-foreground">
                                                        {new Date(user.created_at).toLocaleDateString()}
                                                    </p>
                                                </td>

                                                {/* Actions */}
                                                <td className="py-3 px-4">
                                                    <div className="flex items-center justify-end gap-1">
                                                        <Link href={`/admin/users/${user.id}`}>
                                                            <Button variant="outline" size="sm" title="View User">
                                                                <Eye className="h-3 w-3" />
                                                            </Button>
                                                        </Link>

                                                        <DropdownMenu>
                                                            <DropdownMenuTrigger asChild>
                                                                <Button variant="outline" size="sm" title="More Actions">
                                                                    <MoreHorizontal className="h-3 w-3" />
                                                                </Button>
                                                            </DropdownMenuTrigger>
                                                            <DropdownMenuContent align="end">
                                                                {user.approval_status === 'pending' && (
                                                                    <DropdownMenuItem onClick={() => handleApprove(user.id)}>
                                                                        <CheckCircle className="h-4 w-4 mr-2" />
                                                                        Approve User
                                                                    </DropdownMenuItem>
                                                                )}
                                                                {user.status === 'active' && (
                                                                    <DropdownMenuItem onClick={() => handleSuspend(user.id)}>
                                                                        <Ban className="h-4 w-4 mr-2" />
                                                                        Suspend User
                                                                    </DropdownMenuItem>
                                                                )}
                                                                {user.status === 'suspended' && (
                                                                    <DropdownMenuItem onClick={() => handleUnsuspend(user.id)}>
                                                                        <UserCheck className="h-4 w-4 mr-2" />
                                                                        Unsuspend User
                                                                    </DropdownMenuItem>
                                                                )}
                                                                <DropdownMenuItem onClick={() => handleImpersonate(user)}>
                                                                    <Eye className="h-4 w-4 mr-2" />
                                                                    Login as User
                                                                </DropdownMenuItem>
                                                            </DropdownMenuContent>
                                                        </DropdownMenu>
                                                    </div>
                                                </td>
                                            </tr>
                                        )) || []}
                                    </tbody>
                                </table>

                                {(!users.data || users.data.length === 0) && (
                                    <div className="text-center py-12">
                                        <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-foreground mb-2">No users found</h3>
                                        <p className="text-muted-foreground">
                                            No users match your current filters.
                                        </p>
                                    </div>
                                )}
                            </div>

                            {/* Pagination */}
                            {users.meta && users.meta.last_page > 1 && (
                                <div className="flex items-center justify-between pt-4 border-t">
                                    <p className="text-sm text-muted-foreground">
                                        Showing {users.meta.from} to {users.meta.to} of {users.meta.total} users
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(users.meta.current_page - 1)}
                                            disabled={users.meta.current_page === 1}
                                        >
                                            <ChevronLeft className="w-4 h-4 mr-1" />
                                            Previous
                                        </Button>

                                        {/* Page numbers */}
                                        {Array.from({ length: Math.min(5, users.meta.last_page) }, (_, i) => {
                                            let page;
                                            if (users.meta.last_page <= 5) {
                                                page = i + 1;
                                            } else if (users.meta.current_page <= 3) {
                                                page = i + 1;
                                            } else if (users.meta.current_page >= users.meta.last_page - 2) {
                                                page = users.meta.last_page - 4 + i;
                                            } else {
                                                page = users.meta.current_page - 2 + i;
                                            }

                                            return (
                                                <Button
                                                    key={page}
                                                    variant={page === users.meta.current_page ? 'default' : 'outline'}
                                                    size="sm"
                                                    onClick={() => handlePageChange(page)}
                                                >
                                                    {page}
                                                </Button>
                                            );
                                        })}

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(users.meta.current_page + 1)}
                                            disabled={users.meta.current_page === users.meta.last_page}
                                        >
                                            Next
                                            <ChevronRight className="w-4 h-4 ml-1" />
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>

            {/* Impersonation Security Check Dialog */}
            {selectedUserForImpersonation && (
                <ImpersonationSecurityCheck
                    user={selectedUserForImpersonation}
                    isOpen={showImpersonationDialog}
                    onClose={() => {
                        setShowImpersonationDialog(false);
                        setSelectedUserForImpersonation(null);
                    }}
                    onConfirm={handleImpersonationConfirm}
                />
            )}
        </AppLayout>
    );
}
