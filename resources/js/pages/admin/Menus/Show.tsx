import { DragDropMenuBuilder } from '@/components/admin/DragDropMenuBuilder';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm } from '@inertiajs/react';
import {
    ArrowLeft,
    Plus,
    Settings
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Page {
    id: number;
    title: string;
    slug: string;
}

interface Category {
    id: number;
    name: string;
    slug: string;
}

interface Brand {
    id: number;
    name: string;
    slug: string;
}

interface Model {
    id: number;
    name: string;
    slug: string;
}

interface MenuItem {
    id: number;
    menu_id: number;
    parent_id: number | null;
    title: string;
    url: string | null;
    target: string;
    icon: string | null;
    css_class: string | null;
    type: string;
    reference_id: number | null;
    order: number;
    is_active: boolean;
    children?: MenuItem[];
}

interface Menu {
    id: number;
    name: string;
    location: string;
    description: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    items: MenuItem[];
}

interface AvailableItems {
    pages: Page[];
    categories: Category[];
    brands: Brand[];
    models: Model[];
}

interface Props {
    menu: Menu;
    availableItems: AvailableItems;
    itemTypes: Record<string, string>;
    targetOptions: Record<string, string>;
}

type MenuItemFormData = {
    title: string;
    type: string;
    url: string;
    target: string;
    icon: string;
    css_class: string;
    reference_id: number | null;
    parent_id: number | null;
    is_active: boolean;
} & Record<string, any>;

export default function Show({ menu, availableItems, itemTypes, targetOptions }: Props) {
    const [activeTab, setActiveTab] = useState('items');
    const [isAddingItem, setIsAddingItem] = useState(false);
    const [editingItem, setEditingItem] = useState<MenuItem | null>(null);

    // Form for adding/editing menu items
    const { data, setData, post, put, processing, errors, reset } = useForm<MenuItemFormData>({
        title: '',
        type: 'custom',
        url: '',
        target: '_self',
        icon: '',
        css_class: '',
        reference_id: null,
        parent_id: null,
        is_active: true,
    });

    // Initialize form with item data for editing
    useEffect(() => {
        if (editingItem) {
            setData({
                title: editingItem.title,
                type: editingItem.type,
                url: editingItem.url || '',
                target: editingItem.target,
                icon: editingItem.icon || '',
                css_class: editingItem.css_class || '',
                reference_id: editingItem.reference_id,
                parent_id: editingItem.parent_id,
                is_active: editingItem.is_active,
            });
        }
    }, [editingItem]);

    // Handle adding a new menu item
    const handleAddItem = (e: React.FormEvent) => {
        e.preventDefault();

        post(`/admin/menus/${menu.id}/items`, {
            onSuccess: () => {
                toast.success('Menu item added successfully');
                setIsAddingItem(false);
                reset();
                // Refresh the page to get updated menu items
                router.reload();
            },
            onError: () => {
                toast.error('Failed to add menu item', {
                    description: 'Please check the form for errors and try again.',
                });
            },
        });
    };

    // Handle updating a menu item
    const handleUpdateItem = (e: React.FormEvent) => {
        e.preventDefault();

        if (!editingItem) return;

        put(`/admin/menus/${menu.id}/items/${editingItem.id}`, {
            onSuccess: () => {
                toast.success('Menu item updated successfully');
                setEditingItem(null);
                reset();
                // Refresh the page to get updated menu items
                router.reload();
            },
            onError: () => {
                toast.error('Failed to update menu item', {
                    description: 'Please check the form for errors and try again.',
                });
            },
        });
    };

    // Render reference options based on selected type
    const renderReferenceOptions = () => {
        switch (data.type) {
            case 'page':
                return availableItems.pages.map(page => (
                    <SelectItem key={page.id} value={page.id.toString()}>
                        {page.title}
                    </SelectItem>
                ));
            case 'category':
                return availableItems.categories.map(category => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                    </SelectItem>
                ));
            case 'brand':
                return availableItems.brands.map(brand => (
                    <SelectItem key={brand.id} value={brand.id.toString()}>
                        {brand.name}
                    </SelectItem>
                ));
            case 'model':
                return availableItems.models.map(model => (
                    <SelectItem key={model.id} value={model.id.toString()}>
                        {model.name}
                    </SelectItem>
                ));
            default:
                return null;
        }
    };

    // Render parent options
    const renderParentOptions = () => {
        return menu.items.map(item => (
            <SelectItem key={item.id} value={item.id.toString()}>
                {item.title}
            </SelectItem>
        ));
    };



    return (
        <AppLayout>
            <Head title={`Menu: ${menu.name}`} />

            <div className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="icon" asChild>
                            <Link href="/admin/menus">
                                <ArrowLeft className="h-4 w-4" />
                                <span className="sr-only">Back to menus</span>
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">{menu.name}</h1>
                            <p className="text-muted-foreground">
                                {menu.description || `Menu for ${menu.location} location`}
                            </p>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <Button asChild variant="outline">
                            <Link href={`/admin/menus/${menu.id}/edit`}>
                                <Settings className="mr-2 h-4 w-4" />
                                Menu Settings
                            </Link>
                        </Button>
                        <Button
                            onClick={() => {
                                setIsAddingItem(true);
                                setEditingItem(null);
                                reset();
                            }}
                        >
                            <Plus className="mr-2 h-4 w-4" />
                            Add Menu Item
                        </Button>
                    </div>
                </div>

                <Tabs defaultValue="items" value={activeTab} onValueChange={setActiveTab}>
                    <TabsList>
                        <TabsTrigger value="items">Menu Items</TabsTrigger>
                        {isAddingItem && (
                            <TabsTrigger value="add-item">Add Item</TabsTrigger>
                        )}
                        {editingItem && (
                            <TabsTrigger value="edit-item">Edit Item</TabsTrigger>
                        )}
                    </TabsList>

                    <TabsContent value="items" className="space-y-4">
                        <DragDropMenuBuilder
                            menuId={menu.id}
                            items={menu.items}
                            itemTypes={itemTypes}
                            onEditItem={(item) => {
                                setEditingItem(item);
                                setData({
                                    title: item.title,
                                    type: item.type,
                                    url: item.url || '',
                                    target: item.target,
                                    icon: item.icon || '',
                                    css_class: item.css_class || '',
                                    reference_id: item.reference_id,
                                    parent_id: item.parent_id,
                                    is_active: item.is_active,
                                });
                                setActiveTab('edit-item');
                            }}
                            onAddItem={() => {
                                setIsAddingItem(true);
                                setEditingItem(null);
                                reset();
                                setActiveTab('add-item');
                            }}
                        />
                    </TabsContent>

                    <TabsContent value="add-item">
                        <Card>
                            <CardHeader>
                                <CardTitle>Add Menu Item</CardTitle>
                                <CardDescription>
                                    Create a new item for this menu
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleAddItem} className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="title">Title *</Label>
                                            <Input
                                                id="title"
                                                value={data.title}
                                                onChange={e => setData('title', e.target.value)}
                                                placeholder="Menu item title"
                                                className={errors.title ? 'border-red-500' : ''}
                                            />
                                            {errors.title && (
                                                <p className="text-sm text-red-500">{errors.title}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="type">Type *</Label>
                                            <Select
                                                value={data.type}
                                                onValueChange={value => setData('type', value)}
                                            >
                                                <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Select type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(itemTypes).map(([key, name]) => (
                                                        <SelectItem key={key} value={key}>
                                                            {name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.type && (
                                                <p className="text-sm text-red-500">{errors.type}</p>
                                            )}
                                        </div>

                                        {data.type === 'custom' && (
                                            <div className="space-y-2">
                                                <Label htmlFor="url">URL *</Label>
                                                <Input
                                                    id="url"
                                                    value={data.url}
                                                    onChange={e => setData('url', e.target.value)}
                                                    placeholder="https://example.com"
                                                    className={errors.url ? 'border-red-500' : ''}
                                                />
                                                {errors.url && (
                                                    <p className="text-sm text-red-500">{errors.url}</p>
                                                )}
                                            </div>
                                        )}

                                        {data.type !== 'custom' && (
                                            <div className="space-y-2">
                                                <Label htmlFor="reference_id">Select {data.type} *</Label>
                                                <Select
                                                    value={data.reference_id?.toString() || ''}
                                                    onValueChange={value => setData('reference_id', parseInt(value))}
                                                >
                                                    <SelectTrigger className={errors.reference_id ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder={`Select ${data.type}`} />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {renderReferenceOptions()}
                                                    </SelectContent>
                                                </Select>
                                                {errors.reference_id && (
                                                    <p className="text-sm text-red-500">{errors.reference_id}</p>
                                                )}
                                            </div>
                                        )}

                                        <div className="space-y-2">
                                            <Label htmlFor="target">Open in</Label>
                                            <Select
                                                value={data.target}
                                                onValueChange={value => setData('target', value)}
                                            >
                                                <SelectTrigger className={errors.target ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Select target" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(targetOptions).map(([key, name]) => (
                                                        <SelectItem key={key} value={key}>
                                                            {name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.target && (
                                                <p className="text-sm text-red-500">{errors.target}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="parent_id">Parent Item (Optional)</Label>
                                            <Select
                                                value={data.parent_id?.toString() || 'none'}
                                                onValueChange={value => setData('parent_id', (value && value !== 'none') ? parseInt(value) : null)}
                                            >
                                                <SelectTrigger className={errors.parent_id ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="No parent (top level)" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="none">No parent (top level)</SelectItem>
                                                    {renderParentOptions()}
                                                </SelectContent>
                                            </Select>
                                            {errors.parent_id && (
                                                <p className="text-sm text-red-500">{errors.parent_id}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="icon">Icon (Optional)</Label>
                                            <Input
                                                id="icon"
                                                value={data.icon}
                                                onChange={e => setData('icon', e.target.value)}
                                                placeholder="Icon class or name"
                                                className={errors.icon ? 'border-red-500' : ''}
                                            />
                                            {errors.icon && (
                                                <p className="text-sm text-red-500">{errors.icon}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="css_class">CSS Class (Optional)</Label>
                                            <Input
                                                id="css_class"
                                                value={data.css_class}
                                                onChange={e => setData('css_class', e.target.value)}
                                                placeholder="Additional CSS classes"
                                                className={errors.css_class ? 'border-red-500' : ''}
                                            />
                                            {errors.css_class && (
                                                <p className="text-sm text-red-500">{errors.css_class}</p>
                                            )}
                                        </div>

                                        <div className="flex items-center space-x-2">
                                            <Switch
                                                id="is_active"
                                                checked={data.is_active}
                                                onCheckedChange={checked => setData('is_active', checked)}
                                            />
                                            <Label htmlFor="is_active">Active</Label>
                                        </div>
                                    </div>

                                    <div className="flex justify-end gap-2">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => {
                                                setIsAddingItem(false);
                                                setActiveTab('items');
                                                reset();
                                            }}
                                        >
                                            Cancel
                                        </Button>
                                        <Button type="submit" disabled={processing}>
                                            Add Menu Item
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="edit-item">
                        {editingItem && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Edit Menu Item</CardTitle>
                                    <CardDescription>
                                        Update the selected menu item
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleUpdateItem} className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="title">Title *</Label>
                                                <Input
                                                    id="title"
                                                    value={data.title}
                                                    onChange={e => setData('title', e.target.value)}
                                                    placeholder="Menu item title"
                                                    className={errors.title ? 'border-red-500' : ''}
                                                />
                                                {errors.title && (
                                                    <p className="text-sm text-red-500">{errors.title}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="type">Type *</Label>
                                                <Select
                                                    value={data.type}
                                                    onValueChange={value => setData('type', value)}
                                                >
                                                    <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Select type" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(itemTypes).map(([key, name]) => (
                                                            <SelectItem key={key} value={key}>
                                                                {name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {errors.type && (
                                                    <p className="text-sm text-red-500">{errors.type}</p>
                                                )}
                                            </div>

                                            {data.type === 'custom' && (
                                                <div className="space-y-2">
                                                    <Label htmlFor="url">URL *</Label>
                                                    <Input
                                                        id="url"
                                                        value={data.url}
                                                        onChange={e => setData('url', e.target.value)}
                                                        placeholder="https://example.com"
                                                        className={errors.url ? 'border-red-500' : ''}
                                                    />
                                                    {errors.url && (
                                                        <p className="text-sm text-red-500">{errors.url}</p>
                                                    )}
                                                </div>
                                            )}

                                            {data.type !== 'custom' && (
                                                <div className="space-y-2">
                                                    <Label htmlFor="reference_id">Select {data.type} *</Label>
                                                    <Select
                                                        value={data.reference_id?.toString() || ''}
                                                        onValueChange={value => setData('reference_id', parseInt(value))}
                                                    >
                                                        <SelectTrigger className={errors.reference_id ? 'border-red-500' : ''}>
                                                            <SelectValue placeholder={`Select ${data.type}`} />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {renderReferenceOptions()}
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.reference_id && (
                                                        <p className="text-sm text-red-500">{errors.reference_id}</p>
                                                    )}
                                                </div>
                                            )}

                                            <div className="space-y-2">
                                                <Label htmlFor="target">Open in</Label>
                                                <Select
                                                    value={data.target}
                                                    onValueChange={value => setData('target', value)}
                                                >
                                                    <SelectTrigger className={errors.target ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Select target" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(targetOptions).map(([key, name]) => (
                                                            <SelectItem key={key} value={key}>
                                                                {name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {errors.target && (
                                                    <p className="text-sm text-red-500">{errors.target}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="icon">Icon (Optional)</Label>
                                                <Input
                                                    id="icon"
                                                    value={data.icon}
                                                    onChange={e => setData('icon', e.target.value)}
                                                    placeholder="Icon class or name"
                                                    className={errors.icon ? 'border-red-500' : ''}
                                                />
                                                {errors.icon && (
                                                    <p className="text-sm text-red-500">{errors.icon}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="css_class">CSS Class (Optional)</Label>
                                                <Input
                                                    id="css_class"
                                                    value={data.css_class}
                                                    onChange={e => setData('css_class', e.target.value)}
                                                    placeholder="Additional CSS classes"
                                                    className={errors.css_class ? 'border-red-500' : ''}
                                                />
                                                {errors.css_class && (
                                                    <p className="text-sm text-red-500">{errors.css_class}</p>
                                                )}
                                            </div>

                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="is_active"
                                                    checked={data.is_active}
                                                    onCheckedChange={checked => setData('is_active', checked)}
                                                />
                                                <Label htmlFor="is_active">Active</Label>
                                            </div>
                                        </div>

                                        <div className="flex justify-end gap-2">
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={() => {
                                                    setEditingItem(null);
                                                    setActiveTab('items');
                                                    reset();
                                                }}
                                            >
                                                Cancel
                                            </Button>
                                            <Button type="submit" disabled={processing}>
                                                Update Item
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        )}
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}