import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm } from '@inertiajs/react';
import {
    ArrowLeft,
    Check,
    Edit,
    Filter,
    Plus,
    Save,
    Search,
    Trash2,
    X
} from 'lucide-react';
import React, { useCallback, useMemo, useState } from 'react';
import { toast } from 'sonner';

interface Category {
    id: number;
    name: string;
}

interface Brand {
    id: number;
    name: string;
    logo_url: string | null;
}

interface ModelData {
    id: number;
    name: string;
    model_number: string | null;
    release_year: number | null;
    brand: Brand;
    is_compatible: boolean;
    compatibility_notes: string | null;
    is_verified: boolean;
    display_type: string | null;
    display_size: string | null;
    location: string | null;
}

interface Part {
    id: number;
    name: string;
    part_number: string | null;
    manufacturer: string | null;
    category: Category;
}

interface Props {
    part: Part;
    allModels: ModelData[];
    showVerificationStatus?: boolean;
}

export default function EditCompatibility({ part, allModels, showVerificationStatus = true }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedBrand, setSelectedBrand] = useState('all');
    const [showCompatibleOnly, setShowCompatibleOnly] = useState(false);
    const [editingModel, setEditingModel] = useState<number | null>(null);
    const [selectedModels, setSelectedModels] = useState<number[]>([]);
    const { showDeleteConfirmation } = useDeleteConfirmation();

    // State for searchable brand select
    const [brandOptions, setBrandOptions] = useState<SearchableSelectOption[]>([]);
    const [brandLoading, setBrandLoading] = useState(false);

    // Form for editing individual compatibility
    const { data: editData, setData: setEditData, put, processing: editProcessing, reset: resetEdit } = useForm({
        compatibility_notes: '',
        is_verified: false as boolean,
        display_type: '',
        display_size: '',
        location: '',
    });

    // Get unique brands from all models
    const brands = useMemo(() => {
        return Array.from(
            new Map(allModels.map(model => [model.brand.id, model.brand])).values()
        );
    }, [allModels]);

    // Filter models based on search, brand, and compatibility status
    const filteredModels = useMemo(() => {
        return allModels.filter(model => {
            const matchesSearch = searchTerm === '' ||
                model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                model.brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (model.model_number && model.model_number.toLowerCase().includes(searchTerm.toLowerCase()));

            const matchesBrand = selectedBrand === 'all' || model.brand.id.toString() === selectedBrand;
            const matchesCompatibility = !showCompatibleOnly || model.is_compatible;

            return matchesSearch && matchesBrand && matchesCompatibility;
        });
    }, [allModels, searchTerm, selectedBrand, showCompatibleOnly]);

    const handleStartEdit = (model: ModelData) => {
        setEditingModel(model.id);
        setEditData({
            compatibility_notes: model.compatibility_notes || '',
            is_verified: model.is_verified,
            display_type: model.display_type || '',
            display_size: model.display_size || '',
            location: model.location || '',
        });
    };

    const handleCancelEdit = () => {
        setEditingModel(null);
        resetEdit();
    };

    const handleSaveEdit = (modelId: number) => {
        // Flash messages from the backend will be handled by FlashMessageHandler
        put(`/admin/parts/${part.id}/compatibility/${modelId}`, {
            onSuccess: () => {
                setEditingModel(null);
                resetEdit();
            }
        });
    };

    const handleToggleCompatibility = (model: ModelData) => {
        if (model.is_compatible) {
            // Remove compatibility
            showDeleteConfirmation({
                title: `Remove compatibility with ${model.brand.name} ${model.name}?`,
                description: "This will remove the compatibility relationship between this part and the selected model. This action cannot be undone.",
                onConfirm: () => {
                    // Flash messages from the backend will be handled by FlashMessageHandler
                    router.delete(`/admin/parts/${part.id}/compatibility/${model.id}`);
                }
            });
        } else {
            // Add compatibility
            // Flash messages from the backend will be handled by FlashMessageHandler
            router.post(`/admin/parts/${part.id}/compatibility`, {
                model_id: model.id.toString(),
                compatibility_notes: '',
                is_verified: false,
            });
        }
    };

    const handleBulkToggleCompatibility = (add: boolean) => {
        if (selectedModels.length === 0) {
            // Keep client-side validation toast for user experience
            toast.error('Please select models first.');
            return;
        }

        if (add) {
            // Add bulk compatibility
            const modelsToAdd = selectedModels.filter(modelId => {
                const model = allModels.find(m => m.id === modelId);
                return model && !model.is_compatible;
            });

            if (modelsToAdd.length === 0) {
                // Keep client-side validation toast for user experience
                toast.error('All selected models are already compatible.');
                return;
            }

            // Flash messages from the backend will be handled by FlashMessageHandler
            router.post(`/admin/parts/${part.id}/compatibility/bulk`, {
                model_ids: modelsToAdd,
                compatibility_notes: '',
                is_verified: false,
            }, {
                onSuccess: () => {
                    setSelectedModels([]);
                }
            });
        } else {
            // Remove bulk compatibility
            const modelsToRemove = selectedModels.filter(modelId => {
                const model = allModels.find(m => m.id === modelId);
                return model && model.is_compatible;
            });

            if (modelsToRemove.length === 0) {
                // Keep client-side validation toast for user experience
                toast.error('None of the selected models are currently compatible.');
                return;
            }

            showDeleteConfirmation({
                title: `Remove compatibility for ${modelsToRemove.length} models?`,
                description: "This will remove the compatibility relationships for all selected models. This action cannot be undone.",
                onConfirm: () => {
                    // Remove each model individually (since we don't have a bulk remove endpoint)
                    // Flash messages from the backend will be handled by FlashMessageHandler
                    let completed = 0;
                    modelsToRemove.forEach(modelId => {
                        router.delete(`/admin/parts/${part.id}/compatibility/${modelId}`, {
                            onSuccess: () => {
                                completed++;
                                if (completed === modelsToRemove.length) {
                                    setSelectedModels([]);
                                }
                            }
                        });
                    });
                }
            });
        }
    };

    const toggleModelSelection = (modelId: number) => {
        setSelectedModels(prev =>
            prev.includes(modelId)
                ? prev.filter(id => id !== modelId)
                : [...prev, modelId]
        );
    };

    const toggleSelectAll = () => {
        if (selectedModels.length === filteredModels.length) {
            setSelectedModels([]);
        } else {
            setSelectedModels(filteredModels.map(model => model.id));
        }
    };

    const compatibleCount = allModels.filter(model => model.is_compatible).length;
    const verifiedCount = allModels.filter(model => model.is_compatible && model.is_verified).length;

    // API call function for searchable brand select
    const searchBrands = useCallback(async (query: string) => {
        setBrandLoading(true);
        try {
            const response = await fetch(`/api/search/brands?q=${encodeURIComponent(query)}&limit=20`);
            if (response.ok) {
                const brands = await response.json();
                setBrandOptions(brands);
            }
        } catch (error) {
            console.error('Error searching brands:', error);
        } finally {
            setBrandLoading(false);
        }
    }, []);

    // Initialize brand options on mount
    React.useEffect(() => {
        searchBrands('');
    }, [searchBrands]);

    return (
        <AppLayout>
            <Head title={`Edit Compatibility - ${part.name} - Parts - Admin`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center gap-4">
                        <Link href={`/admin/parts/${part.id}/compatibility`}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Compatibility
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Edit Compatibility</h1>
                            <p className="text-muted-foreground">
                                Manage compatibility for "{part.name}" in table view
                            </p>
                        </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <Card>
                            <CardContent className="p-4">
                                <div className="text-2xl font-bold">{allModels.length}</div>
                                <p className="text-xs text-muted-foreground">Total Models</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-4">
                                <div className="text-2xl font-bold text-green-600">{compatibleCount}</div>
                                <p className="text-xs text-muted-foreground">Compatible</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-4">
                                <div className="text-2xl font-bold text-blue-600">{verifiedCount}</div>
                                <p className="text-xs text-muted-foreground">Verified</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-4">
                                <div className="text-2xl font-bold text-orange-600">{allModels.length - compatibleCount}</div>
                                <p className="text-xs text-muted-foreground">Not Compatible</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filters and Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="w-5 h-5" />
                                Filters & Actions
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                {/* Search */}
                                <div className="space-y-2">
                                    <Label htmlFor="search">Search Models</Label>
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                        <Input
                                            id="search"
                                            placeholder="Search models..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>

                                {/* Brand Filter */}
                                <div className="space-y-2">
                                    <Label htmlFor="brand-filter">Filter by Brand</Label>
                                    <SearchableSelect
                                        options={[
                                            { value: 'all', label: 'All brands' },
                                            ...brandOptions
                                        ]}
                                        value={selectedBrand}
                                        onValueChange={setSelectedBrand}
                                        onSearch={searchBrands}
                                        loading={brandLoading}
                                        placeholder="All brands"
                                        searchPlaceholder="Search brands..."
                                        allowClear={selectedBrand !== 'all'}
                                    />
                                </div>

                                {/* Compatibility Filter */}
                                <div className="space-y-2">
                                    <Label>Show Compatible Only</Label>
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="compatible-only"
                                            checked={showCompatibleOnly}
                                            onCheckedChange={setShowCompatibleOnly}
                                        />
                                        <Label htmlFor="compatible-only" className="text-sm">
                                            {showCompatibleOnly ? 'Compatible only' : 'All models'}
                                        </Label>
                                    </div>
                                </div>

                                {/* Bulk Actions */}
                                <div className="space-y-2">
                                    <Label>Bulk Actions</Label>
                                    <div className="flex gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleBulkToggleCompatibility(true)}
                                            disabled={selectedModels.length === 0}
                                        >
                                            <Plus className="w-4 h-4 mr-1" />
                                            Add
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleBulkToggleCompatibility(false)}
                                            disabled={selectedModels.length === 0}
                                            className="text-destructive hover:text-destructive"
                                        >
                                            <Trash2 className="w-4 h-4 mr-1" />
                                            Remove
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Models Table */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="flex items-center gap-2">
                                    <Edit className="w-5 h-5" />
                                    Models ({filteredModels.length})
                                </CardTitle>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm text-muted-foreground">
                                        {selectedModels.length} selected
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={toggleSelectAll}
                                    >
                                        {selectedModels.length === filteredModels.length ? 'Deselect All' : 'Select All'}
                                    </Button>
                                </div>
                            </div>
                            <CardDescription>
                                Click on compatibility status to toggle, or use the edit button to modify details
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full border-collapse">
                                    <thead>
                                        <tr className="border-b">
                                            <th className="text-left p-3 font-medium">
                                                <Checkbox
                                                    checked={selectedModels.length === filteredModels.length && filteredModels.length > 0}
                                                    onCheckedChange={toggleSelectAll}
                                                />
                                            </th>
                                            <th className="text-left p-3 font-medium">Brand</th>
                                            <th className="text-left p-3 font-medium">Model</th>
                                            <th className="text-left p-3 font-medium">Model Number</th>
                                            <th className="text-left p-3 font-medium">Release Year</th>
                                            <th className="text-left p-3 font-medium">Compatible</th>
                                            {showVerificationStatus && (
                                                <th className="text-left p-3 font-medium">Verified</th>
                                            )}
                                            <th className="text-left p-3 font-medium">Display Type</th>
                                            <th className="text-left p-3 font-medium">Display Size</th>
                                            <th className="text-left p-3 font-medium">Location</th>
                                            <th className="text-left p-3 font-medium">Notes</th>
                                            <th className="text-left p-3 font-medium">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredModels.map((model) => (
                                            <tr key={model.id} className="border-b hover:bg-muted/50">
                                                <td className="p-3">
                                                    <Checkbox
                                                        checked={selectedModels.includes(model.id)}
                                                        onCheckedChange={() => toggleModelSelection(model.id)}
                                                    />
                                                </td>
                                                <td className="p-3">
                                                    <div className="flex items-center gap-2">
                                                        {model.brand.logo_url && (
                                                            <img
                                                                src={model.brand.logo_url}
                                                                alt={model.brand.name}
                                                                className="w-6 h-6 object-contain"
                                                            />
                                                        )}
                                                        <span className="font-medium">{model.brand.name}</span>
                                                    </div>
                                                </td>
                                                <td className="p-3">
                                                    <div>
                                                        <div className="font-medium">{model.name}</div>
                                                        {model.model_number && (
                                                            <div className="text-sm text-muted-foreground">
                                                                {model.model_number}
                                                            </div>
                                                        )}
                                                        {model.release_year && (
                                                            <div className="text-sm text-muted-foreground">
                                                                {model.release_year}
                                                            </div>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="p-3">
                                                    <span className="text-sm">{model.model_number || '-'}</span>
                                                </td>
                                                <td className="p-3">
                                                    <span className="text-sm">{model.release_year || '-'}</span>
                                                </td>
                                                <td className="p-3">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleToggleCompatibility(model)}
                                                        className={model.is_compatible ? 'text-green-600 hover:text-green-700' : 'text-gray-400 hover:text-gray-600'}
                                                    >
                                                        {model.is_compatible ? <Check className="w-4 h-4" /> : <X className="w-4 h-4" />}
                                                    </Button>
                                                </td>
                                                {showVerificationStatus && (
                                                    <td className="p-3">
                                                        {model.is_compatible && (
                                                            <Badge variant={model.is_verified ? "default" : "secondary"}>
                                                                {model.is_verified ? 'Verified' : 'Unverified'}
                                                            </Badge>
                                                        )}
                                                    </td>
                                                )}
                                                <td className="p-3">
                                                    {editingModel === model.id ? (
                                                        <Input
                                                            value={editData.display_type}
                                                            onChange={(e) => setEditData('display_type', e.target.value)}
                                                            placeholder="Display type..."
                                                            className="text-sm"
                                                        />
                                                    ) : (
                                                        <span className="text-sm text-muted-foreground">
                                                            {model.display_type || (model.is_compatible ? '-' : '-')}
                                                        </span>
                                                    )}
                                                </td>
                                                <td className="p-3">
                                                    {editingModel === model.id ? (
                                                        <Input
                                                            value={editData.display_size}
                                                            onChange={(e) => setEditData('display_size', e.target.value)}
                                                            placeholder="Display size..."
                                                            className="text-sm"
                                                        />
                                                    ) : (
                                                        <span className="text-sm text-muted-foreground">
                                                            {model.display_size || (model.is_compatible ? '-' : '-')}
                                                        </span>
                                                    )}
                                                </td>
                                                <td className="p-3">
                                                    {editingModel === model.id ? (
                                                        <Input
                                                            value={editData.location}
                                                            onChange={(e) => setEditData('location', e.target.value)}
                                                            placeholder="Location..."
                                                            className="text-sm"
                                                        />
                                                    ) : (
                                                        <span className="text-sm text-muted-foreground">
                                                            {model.location || (model.is_compatible ? '-' : '-')}
                                                        </span>
                                                    )}
                                                </td>
                                                <td className="p-3 max-w-xs">
                                                    {editingModel === model.id ? (
                                                        <Textarea
                                                            value={editData.compatibility_notes}
                                                            onChange={(e) => setEditData('compatibility_notes', e.target.value)}
                                                            placeholder="Compatibility notes..."
                                                            rows={2}
                                                            className="text-sm"
                                                        />
                                                    ) : (
                                                        <span className="text-sm text-muted-foreground">
                                                            {model.compatibility_notes || (model.is_compatible ? 'No notes' : '-')}
                                                        </span>
                                                    )}
                                                </td>
                                                <td className="p-3">
                                                    <div className="flex items-center gap-1">
                                                        {model.is_compatible && (
                                                            <>
                                                                {editingModel === model.id ? (
                                                                    <>
                                                                        <div className="flex items-center gap-1 mr-2">
                                                                            <Switch
                                                                                checked={editData.is_verified}
                                                                                onCheckedChange={(checked) => setEditData('is_verified', checked)}
                                                                            />
                                                                            <span className="text-xs">Verified</span>
                                                                        </div>
                                                                        <Button
                                                                            variant="outline"
                                                                            size="sm"
                                                                            onClick={() => handleSaveEdit(model.id)}
                                                                            disabled={editProcessing}
                                                                        >
                                                                            <Save className="w-3 h-3" />
                                                                        </Button>
                                                                        <Button
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            onClick={handleCancelEdit}
                                                                        >
                                                                            <X className="w-3 h-3" />
                                                                        </Button>
                                                                    </>
                                                                ) : (
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={() => handleStartEdit(model)}
                                                                    >
                                                                        <Edit className="w-3 h-3" />
                                                                    </Button>
                                                                )}
                                                            </>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>

                                {filteredModels.length === 0 && (
                                    <div className="text-center py-8">
                                        <p className="text-muted-foreground">No models found matching your filters.</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
