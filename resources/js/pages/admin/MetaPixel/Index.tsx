import { MetaPixelPrivacySettings } from '@/components/analytics/MetaPixelPrivacySettings';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppSidebarLayout from '@/layouts/app/app-sidebar-layout';
import { Head, useForm } from '@inertiajs/react';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff,
  RefreshCw,
  Settings,
  Shield,
  TestTube,
  Trash2,
  XCircle,
  <PERSON>ap
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface MetaPixelConfig {
  enabled: boolean;
  pixel_id: string;
  access_token_masked?: string;
  conversions_api_enabled: boolean;
  test_event_code?: string;
  enabled_events: string[];
  custom_events: any[];
  respect_do_not_track: boolean;
  require_consent: boolean;
  consent_settings: Record<string, string>;
  debug_mode: boolean;
  automatic_matching: boolean;
  enable_deduplication: boolean;
  deduplication_method: string;
  lazy_load: boolean;
  event_batch_size: number;
  event_delay_ms: number;
}

interface MetaPixelStatus {
  enabled: boolean;
  conversions_api_enabled: boolean;
  pixel_id: string;
  pixel_id_valid: boolean;
  access_token_configured: boolean;
  debug_mode: boolean;
  enabled_events_count: number;
  deduplication_enabled: boolean;
  consent_required: boolean;
}

interface StandardEvent {
  name: string;
  description: string;
  parameters: string[];
  automatic: boolean;
}

interface Props {
  config: MetaPixelConfig;
  status: MetaPixelStatus;
  conversions_api_status: any;
  standard_events: Record<string, StandardEvent>;
  default_consent_settings: Record<string, string>;
  error?: string;
}

export default function MetaPixelIndex({
  config,
  status,
  conversions_api_status,
  standard_events,
  default_consent_settings,
  error
}: Props) {
  const [showAccessToken, setShowAccessToken] = useState(false);
  const [isTestingConfig, setIsTestingConfig] = useState(false);
  const [isTestingApi, setIsTestingApi] = useState(false);
  const [isClearingCache, setIsClearingCache] = useState(false);

  const { data, setData, post, processing, errors, reset } = useForm({
    enabled: config.enabled,
    pixel_id: config.pixel_id || '',
    access_token: '',
    conversions_api_enabled: config.conversions_api_enabled,
    test_event_code: config.test_event_code || '',
    enabled_events: config.enabled_events,
    respect_do_not_track: config.respect_do_not_track,
    require_consent: config.require_consent,
    consent_settings: config.consent_settings || default_consent_settings,
    debug_mode: config.debug_mode,
    automatic_matching: config.automatic_matching,
    enable_deduplication: config.enable_deduplication,
    deduplication_method: config.deduplication_method || 'event_id',
    lazy_load: config.lazy_load,
    event_batch_size: config.event_batch_size || 1,
    event_delay_ms: config.event_delay_ms || 0,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('admin.meta-pixel.update-config'), {
      onSuccess: () => {
        toast.success('Meta Pixel configuration updated successfully');
        reset('access_token');
      },
      onError: () => {
        toast.error('Failed to update configuration');
      },
    });
  };

  const handleTestConfiguration = () => {
    setIsTestingConfig(true);
    post(route('admin.meta-pixel.test-configuration'), {
      onFinish: () => setIsTestingConfig(false),
    });
  };

  const handleTestConversionsApi = () => {
    setIsTestingApi(true);
    post(route('admin.meta-pixel.test-conversions-api'), {
      onFinish: () => setIsTestingApi(false),
    });
  };

  const handleClearCache = () => {
    setIsClearingCache(true);
    post(route('admin.meta-pixel.clear-cache'), {
      onFinish: () => setIsClearingCache(false),
    });
  };

  const toggleEvent = (eventName: string) => {
    const currentEvents = data.enabled_events || [];
    const isEnabled = currentEvents.includes(eventName);

    if (isEnabled) {
      setData('enabled_events', currentEvents.filter(e => e !== eventName));
    } else {
      setData('enabled_events', [...currentEvents, eventName]);
    }
  };

  const getStatusIcon = (isValid: boolean) => {
    return isValid ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  return (
    <AppSidebarLayout>
      <Head title="Meta Pixel Configuration" />

      <div className="p-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Meta Pixel Configuration</h1>
          <p className="text-muted-foreground">
            Configure Meta Pixel (Facebook Pixel) and Conversions API for enhanced tracking and analytics.
          </p>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Status Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Status Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-center gap-2">
                {getStatusIcon(status.enabled)}
                <span className="text-sm">Meta Pixel {status.enabled ? 'Enabled' : 'Disabled'}</span>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(status.conversions_api_enabled)}
                <span className="text-sm">Conversions API {status.conversions_api_enabled ? 'Enabled' : 'Disabled'}</span>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(status.pixel_id_valid)}
                <span className="text-sm">Pixel ID {status.pixel_id_valid ? 'Valid' : 'Invalid'}</span>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(status.access_token_configured)}
                <span className="text-sm">Access Token {status.access_token_configured ? 'Configured' : 'Missing'}</span>
              </div>
            </div>

            <Separator className="my-4" />

            <div className="flex flex-wrap gap-2">
              <Badge variant={status.enabled ? "default" : "secondary"}>
                {status.enabled_events_count} Events Enabled
              </Badge>
              {status.debug_mode && (
                <Badge variant="outline">Debug Mode</Badge>
              )}
              {status.deduplication_enabled && (
                <Badge variant="outline">Deduplication Enabled</Badge>
              )}
              {status.consent_required && (
                <Badge variant="outline">Consent Required</Badge>
              )}
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="basic" className="space-y-4">
          <TabsList>
            <TabsTrigger value="basic">Basic Configuration</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="privacy">Privacy & Consent</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
            <TabsTrigger value="testing">Testing</TabsTrigger>
          </TabsList>

          <form onSubmit={handleSubmit}>
            <TabsContent value="basic" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Basic Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure your Meta Pixel ID and basic settings.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enabled"
                      checked={data.enabled}
                      onCheckedChange={(checked) => setData('enabled', checked)}
                    />
                    <Label htmlFor="enabled">Enable Meta Pixel</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="pixel_id">Pixel ID</Label>
                    <Input
                      id="pixel_id"
                      type="text"
                      value={data.pixel_id}
                      onChange={(e) => setData('pixel_id', e.target.value)}
                      placeholder="Enter your Meta Pixel ID"
                      disabled={!data.enabled}
                    />
                    {errors.pixel_id && (
                      <p className="text-sm text-red-500">{errors.pixel_id}</p>
                    )}
                  </div>

                  <Separator />

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="conversions_api_enabled"
                      checked={data.conversions_api_enabled}
                      onCheckedChange={(checked) => setData('conversions_api_enabled', checked)}
                    />
                    <Label htmlFor="conversions_api_enabled">Enable Conversions API</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="access_token">Access Token</Label>
                    <div className="flex gap-2">
                      <Input
                        id="access_token"
                        type={showAccessToken ? "text" : "password"}
                        value={data.access_token}
                        onChange={(e) => setData('access_token', e.target.value)}
                        placeholder={config.access_token_masked || "Enter your access token"}
                        disabled={!data.conversions_api_enabled}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => setShowAccessToken(!showAccessToken)}
                        disabled={!data.conversions_api_enabled}
                      >
                        {showAccessToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    {errors.access_token && (
                      <p className="text-sm text-red-500">{errors.access_token}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="test_event_code">Test Event Code (Optional)</Label>
                    <Input
                      id="test_event_code"
                      type="text"
                      value={data.test_event_code}
                      onChange={(e) => setData('test_event_code', e.target.value)}
                      placeholder="Enter test event code for debugging"
                      disabled={!data.conversions_api_enabled}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Event Configuration</CardTitle>
                  <CardDescription>
                    Select which events to track with Meta Pixel.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(standard_events).map(([eventKey, event]) => (
                      <div key={eventKey} className="flex items-center space-x-2 p-3 border rounded-lg">
                        <Switch
                          id={eventKey}
                          checked={data.enabled_events.includes(event.name)}
                          onCheckedChange={() => toggleEvent(event.name)}
                        />
                        <div className="flex-1">
                          <Label htmlFor={eventKey} className="font-medium">
                            {event.name}
                            {event.automatic && (
                              <Badge variant="secondary" className="ml-2 text-xs">Auto</Badge>
                            )}
                          </Label>
                          <p className="text-xs text-muted-foreground">{event.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="privacy" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Privacy & Consent Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure privacy settings and consent management for Meta Pixel tracking.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="respect_do_not_track"
                          checked={data.respect_do_not_track}
                          onCheckedChange={(checked) => setData('respect_do_not_track', checked)}
                        />
                        <Label htmlFor="respect_do_not_track">Respect Do Not Track</Label>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Automatically disable tracking when users have enabled "Do Not Track" in their browser.
                      </p>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="require_consent"
                          checked={data.require_consent}
                          onCheckedChange={(checked) => setData('require_consent', checked)}
                        />
                        <Label htmlFor="require_consent">Require User Consent</Label>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Require explicit user consent before tracking begins. Recommended for GDPR compliance.
                      </p>
                    </div>

                    <div className="space-y-4">
                      <h4 className="text-sm font-medium">Privacy Information</h4>
                      <div className="text-sm text-muted-foreground space-y-2">
                        <p>• User data is automatically anonymized for privacy protection</p>
                        <p>• Consent is stored with timestamps for audit purposes</p>
                        <p>• Users can withdraw consent at any time</p>
                        <p>• Tracking respects browser privacy settings</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Live Privacy Settings Preview */}
              <MetaPixelPrivacySettings
                showTitle={true}
                className="border-dashed"
              />
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Advanced Settings
                  </CardTitle>
                  <CardDescription>
                    Advanced configuration options for Meta Pixel.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="debug_mode"
                      checked={data.debug_mode}
                      onCheckedChange={(checked) => setData('debug_mode', checked)}
                    />
                    <Label htmlFor="debug_mode">Debug Mode</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="automatic_matching"
                      checked={data.automatic_matching}
                      onCheckedChange={(checked) => setData('automatic_matching', checked)}
                    />
                    <Label htmlFor="automatic_matching">Automatic Advanced Matching</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enable_deduplication"
                      checked={data.enable_deduplication}
                      onCheckedChange={(checked) => setData('enable_deduplication', checked)}
                    />
                    <Label htmlFor="enable_deduplication">Enable Event Deduplication</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="lazy_load"
                      checked={data.lazy_load}
                      onCheckedChange={(checked) => setData('lazy_load', checked)}
                    />
                    <Label htmlFor="lazy_load">Lazy Load Pixel</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="testing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TestTube className="h-5 w-5" />
                    Testing & Debugging
                  </CardTitle>
                  <CardDescription>
                    Test your Meta Pixel configuration and debug issues.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleTestConfiguration}
                      disabled={isTestingConfig}
                    >
                      {isTestingConfig ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <TestTube className="h-4 w-4 mr-2" />
                      )}
                      Test Configuration
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleTestConversionsApi}
                      disabled={isTestingApi || !data.conversions_api_enabled}
                    >
                      {isTestingApi ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <TestTube className="h-4 w-4 mr-2" />
                      )}
                      Test Conversions API
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleClearCache}
                      disabled={isClearingCache}
                    >
                      {isClearingCache ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4 mr-2" />
                      )}
                      Clear Cache
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <div className="flex justify-end gap-2 mt-6">
              <Button type="submit" disabled={processing}>
                {processing ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                Save Configuration
              </Button>
            </div>
          </form>
        </Tabs>
      </div>
    </AppSidebarLayout>
  );
}
