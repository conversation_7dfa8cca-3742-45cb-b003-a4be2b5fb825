import React, { createContext, useContext, useEffect, useState } from 'react';

interface AccordionContextType {
    expandedGroupId: string | null;
    setExpandedGroupId: (groupId: string | null) => void;
    expandGroup: (groupId: string) => void;
}

const AccordionContext = createContext<AccordionContextType | null>(null);

interface AccordionProviderProps {
    children: React.ReactNode;
    storageKey?: string;
    defaultExpandedGroupId?: string | null;
}

export function AccordionProvider({ 
    children, 
    storageKey = 'sidebar_accordion_state',
    defaultExpandedGroupId = null 
}: AccordionProviderProps) {
    const [expandedGroupId, setExpandedGroupId] = useState<string | null>(() => {
        // Try to get saved state from localStorage
        if (storageKey) {
            try {
                const saved = localStorage.getItem(storageKey);
                return saved ? JSON.parse(saved) : defaultExpandedGroupId;
            } catch {
                return defaultExpandedGroupId;
            }
        }
        return defaultExpandedGroupId;
    });

    // Save state to localStorage whenever it changes
    useEffect(() => {
        if (storageKey) {
            try {
                localStorage.setItem(storageKey, JSON.stringify(expandedGroupId));
            } catch {
                // Silently fail if localStorage is not available
            }
        }
    }, [expandedGroupId, storageKey]);

    const expandGroup = (groupId: string) => {
        // If clicking the same group that's already expanded, collapse it
        if (expandedGroupId === groupId) {
            setExpandedGroupId(null);
        } else {
            // Otherwise expand the new group (and collapse any currently expanded group)
            setExpandedGroupId(groupId);
        }
    };

    const value: AccordionContextType = {
        expandedGroupId,
        setExpandedGroupId,
        expandGroup
    };

    return (
        <AccordionContext.Provider value={value}>
            {children}
        </AccordionContext.Provider>
    );
}

export function useAccordion() {
    const context = useContext(AccordionContext);
    if (!context) {
        throw new Error('useAccordion must be used within an AccordionProvider');
    }
    return context;
}
