import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { type SharedData } from '@/types';
import { api, handleApiError } from '@/utils/api';
import { router, usePage } from '@inertiajs/react';
import { AlertTriangle, ChevronDown, Clock, Eye, LogOut, Minimize2, Shield } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface ImpersonationStatus {
    is_impersonating: boolean;
    impersonating_user_id?: number;
    original_admin_id?: number;
    expires_at?: string;
    remaining_minutes?: number;
}

// Configuration constants
const POLLING_INTERVAL = 30000; // 30 seconds
const RETRY_INTERVAL = 5000; // 5 seconds for retries
const MAX_RETRY_ATTEMPTS = 3;
const DEBUG_MODE = process.env.NODE_ENV === 'development';

export default function ImpersonationBanner() {
    const [impersonationStatus, setImpersonationStatus] = useState<ImpersonationStatus | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const [lastFetchTime, setLastFetchTime] = useState<number>(0);
    const [isMinimized, setIsMinimized] = useState(false);
    const [remainingTime, setRemainingTime] = useState<number | null>(null);
    const { auth } = usePage<SharedData>().props;

    // Refs for cleanup
    const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
    const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);
    const mountedRef = useRef(true);

    // Debug logging helper
    const debugLog = useCallback((message: string, data?: any) => {
        if (DEBUG_MODE) {
            console.log(`[ImpersonationBanner] ${message}`, data || '');
        }
    }, []);

    // Clear all timers
    const clearTimers = useCallback(() => {
        if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
        }
        if (retryTimeoutRef.current) {
            clearTimeout(retryTimeoutRef.current);
            retryTimeoutRef.current = null;
        }
        if (countdownIntervalRef.current) {
            clearInterval(countdownIntervalRef.current);
            countdownIntervalRef.current = null;
        }
    }, []);

    // Format time remaining in a human-readable format
    const formatTimeRemaining = useCallback((minutes: number): string => {
        if (minutes <= 0) return '0m';

        const hours = Math.floor(minutes / 60);
        const mins = Math.floor(minutes % 60);
        const secs = Math.floor((minutes % 1) * 60);

        if (hours > 0) {
            return `${hours}h ${mins}m`;
        } else if (mins > 0) {
            return `${mins}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }, []);

    // Start countdown timer
    const startCountdown = useCallback((expiresAt: string) => {
        if (countdownIntervalRef.current) {
            clearInterval(countdownIntervalRef.current);
        }

        const updateRemainingTime = () => {
            if (!mountedRef.current) return;

            try {
                const now = new Date().getTime();
                const expiry = new Date(expiresAt).getTime();
                const diffMs = expiry - now;
                const diffMinutes = diffMs / (1000 * 60);

                if (diffMinutes <= 0) {
                    setRemainingTime(0);
                    if (countdownIntervalRef.current) {
                        clearInterval(countdownIntervalRef.current);
                        countdownIntervalRef.current = null;
                    }
                    // Trigger a status refresh when expired
                    fetchImpersonationStatus();
                } else {
                    setRemainingTime(diffMinutes);
                }
            } catch (error) {
                debugLog('Error calculating remaining time', error);
                setRemainingTime(null);
            }
        };

        // Update immediately
        updateRemainingTime();

        // Update every second for live countdown
        countdownIntervalRef.current = setInterval(updateRemainingTime, 1000);
    }, [debugLog]);

    const fetchImpersonationStatus = useCallback(async (isRetry = false) => {
        if (!mountedRef.current) return;

        const now = Date.now();
        setLastFetchTime(now);

        if (!isRetry) {
            setError(null);
            debugLog('Fetching impersonation status', { userId: auth?.user?.id, timestamp: now });
        } else {
            debugLog('Retrying impersonation status fetch', { attempt: retryCount + 1 });
        }

        // Only check impersonation status if user is authenticated
        if (!auth?.user?.id) {
            debugLog('No authenticated user, setting impersonation to false');
            setImpersonationStatus({ is_impersonating: false });
            setIsLoading(false);
            setRetryCount(0);
            return;
        }

        try {
            const response = await api.get<ImpersonationStatus>('/api/impersonation/status', {
                requireAuth: false // Let the server handle auth, don't pre-check on client
            });

            if (!mountedRef.current) return;

            if (response.ok && response.data) {
                debugLog('Successfully fetched impersonation status', response.data);
                setImpersonationStatus(response.data);
                setRetryCount(0); // Reset retry count on success

                // Start countdown if impersonating and has expiry
                if (response.data.is_impersonating && response.data.expires_at) {
                    startCountdown(response.data.expires_at);
                }

                // Start polling if impersonating
                if (response.data.is_impersonating && !pollingIntervalRef.current) {
                    debugLog('Starting polling for impersonation status');
                    pollingIntervalRef.current = setInterval(() => {
                        fetchImpersonationStatus(false);
                    }, POLLING_INTERVAL);
                }
            } else if (response.status === 401 || response.status === 403) {
                debugLog('Authentication error, setting impersonation to false', { status: response.status });
                setImpersonationStatus({ is_impersonating: false });
                setRetryCount(0);
            } else {
                const errorMsg = response.error || 'Failed to fetch impersonation status';
                debugLog('API error', { status: response.status, error: errorMsg });

                if (retryCount < MAX_RETRY_ATTEMPTS) {
                    setRetryCount(prev => prev + 1);
                    retryTimeoutRef.current = setTimeout(() => {
                        fetchImpersonationStatus(true);
                    }, RETRY_INTERVAL);
                } else {
                    setError(errorMsg);
                    setImpersonationStatus({ is_impersonating: false });
                    setRetryCount(0);
                }
            }
        } catch (err) {
            if (!mountedRef.current) return;

            const errorMessage = err instanceof Error ? err.message : 'Network error';
            debugLog('Network error', { error: errorMessage, retryCount });

            if (retryCount < MAX_RETRY_ATTEMPTS) {
                setRetryCount(prev => prev + 1);
                retryTimeoutRef.current = setTimeout(() => {
                    fetchImpersonationStatus(true);
                }, RETRY_INTERVAL);
            } else {
                setError(errorMessage);
                setImpersonationStatus({ is_impersonating: false });
                setRetryCount(0);
                handleApiError(errorMessage, 'Failed to check impersonation status');
            }
        } finally {
            if (mountedRef.current) {
                setIsLoading(false);
            }
        }
    }, [auth?.user?.id, retryCount, debugLog]);

    // Effect for initial fetch and cleanup
    useEffect(() => {
        debugLog('Component mounted, fetching initial status');
        fetchImpersonationStatus();

        return () => {
            debugLog('Component unmounting, cleaning up');
            mountedRef.current = false;
            clearTimers();
        };
    }, [fetchImpersonationStatus, clearTimers, debugLog]);

    // Effect for page navigation events - re-check status when user navigates
    useEffect(() => {
        const handlePageChange = () => {
            debugLog('Page changed, re-checking impersonation status');
            // Small delay to ensure session is updated
            setTimeout(() => {
                if (mountedRef.current) {
                    fetchImpersonationStatus();
                }
            }, 100);
        };

        // Listen to page prop changes (Inertia navigation)
        handlePageChange();
    }, [auth?.user?.id, fetchImpersonationStatus, debugLog]);

    // Effect to stop polling when not impersonating
    useEffect(() => {
        if (!impersonationStatus?.is_impersonating && pollingIntervalRef.current) {
            debugLog('Stopping polling - not impersonating');
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
        }
    }, [impersonationStatus?.is_impersonating, debugLog]);

    const handleEndImpersonation = useCallback(() => {
        debugLog('Ending impersonation session');

        // Clear polling while ending impersonation
        if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
        }

        // Use Inertia.js router which should handle CSRF automatically
        router.post('/admin/impersonate/end', {}, {
            onSuccess: () => {
                debugLog('Impersonation ended successfully');
                setImpersonationStatus({ is_impersonating: false });
                setError(null);
                setRetryCount(0);
            },
            onError: (errors) => {
                debugLog('Failed to end impersonation', errors);
                console.error('Failed to end impersonation:', errors);

                // If there's a CSRF error, try to refresh the page
                if (errors && (errors.message?.includes('CSRF') || errors.message?.includes('419'))) {
                    debugLog('CSRF error detected, refreshing page');
                    window.location.reload();
                } else {
                    // For other errors, redirect to admin dashboard
                    debugLog('Redirecting to admin dashboard due to error');
                    window.location.href = '/admin/dashboard';
                }
            },
        });
    }, [debugLog]);

    // Show loading state only briefly on initial load
    if (isLoading && !impersonationStatus) {
        return null;
    }

    // Show error state in development for debugging
    if (DEBUG_MODE && error && !impersonationStatus?.is_impersonating) {
        return (
            <div className="fixed top-0 left-0 right-0 z-50 bg-red-500 text-white shadow-lg">
                <div className="container mx-auto px-4 py-1">
                    <div className="flex items-center gap-2 text-sm">
                        <AlertTriangle className="h-4 w-4" />
                        <span>Impersonation Banner Debug: {error}</span>
                        <span className="text-red-200">
                            (Retry {retryCount}/{MAX_RETRY_ATTEMPTS})
                        </span>
                    </div>
                </div>
            </div>
        );
    }

    // Only show banner when actually impersonating
    if (!impersonationStatus?.is_impersonating) {
        return null;
    }

    return (
        <>
            {/* Spacer div to push content down */}
            <div className={`transition-all duration-300 ${
                isMinimized ? 'h-8' : 'h-auto'
            }`} style={{
                height: isMinimized ? '32px' : 'auto',
                minHeight: isMinimized ? '32px' : '80px'
            }} />

            <div className={`fixed top-0 left-0 right-0 z-50 bg-orange-500 text-white shadow-lg transition-all duration-300 ${
                isMinimized ? 'h-8' : 'h-auto'
            }`}>
                <div className="container mx-auto px-4">
                {isMinimized ? (
                    // Minimized view
                    <div className="flex items-center justify-between h-8 text-sm">
                        <div className="flex items-center gap-2 text-white">
                            <Shield className="h-3 w-3" />
                            <span className="font-medium">Impersonating User {impersonationStatus.impersonating_user_id}</span>
                            {remainingTime !== null && (
                                <div className="flex items-center gap-1 text-orange-100">
                                    <Clock className="h-3 w-3" />
                                    <span>{formatTimeRemaining(remainingTime)}</span>
                                </div>
                            )}
                        </div>
                        <div className="flex items-center gap-1">
                            <Button
                                onClick={handleEndImpersonation}
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-white hover:bg-orange-600 hover:text-white text-xs"
                            >
                                <LogOut className="h-3 w-3 mr-1" />
                                Exit
                            </Button>
                            <Button
                                onClick={() => setIsMinimized(false)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-white hover:bg-orange-600 hover:text-white"
                                title="Expand banner"
                            >
                                <ChevronDown className="h-3 w-3" />
                            </Button>
                        </div>
                    </div>
                ) : (
                    // Full view
                    <div className="py-3">
                        <Alert className="border-orange-600 bg-orange-500 text-white">
                            <Shield className="h-4 w-4 text-white" />
                            <AlertDescription className="flex items-center justify-between text-white">
                                <div className="flex items-center gap-3">
                                    <div className="flex items-center gap-2">
                                        <Eye className="h-4 w-4" />
                                        <span className="font-medium text-white">
                                            You are currently impersonating a user
                                        </span>
                                    </div>
                                    {impersonationStatus.impersonating_user_id && (
                                        <span className="text-orange-100 bg-orange-600 px-2 py-1 rounded text-sm">
                                            User ID: {impersonationStatus.impersonating_user_id}
                                        </span>
                                    )}
                                    {remainingTime !== null && (
                                        <div className="flex items-center gap-1 text-orange-100 bg-orange-600 px-2 py-1 rounded text-sm">
                                            <Clock className="h-3 w-3" />
                                            <span className="font-mono">{formatTimeRemaining(remainingTime)} remaining</span>
                                        </div>
                                    )}
                                    {DEBUG_MODE && (
                                        <span className="text-orange-200 text-xs bg-orange-600 px-2 py-1 rounded">
                                            Debug: Last check {Math.round((Date.now() - lastFetchTime) / 1000)}s ago
                                        </span>
                                    )}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        onClick={() => setIsMinimized(true)}
                                        variant="ghost"
                                        size="sm"
                                        className="text-white hover:bg-orange-600 hover:text-white"
                                        title="Minimize banner"
                                    >
                                        <Minimize2 className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        onClick={handleEndImpersonation}
                                        variant="outline"
                                        size="sm"
                                        className="bg-white text-orange-600 hover:bg-orange-50 border-white font-medium"
                                    >
                                        <LogOut className="h-4 w-4 mr-2" />
                                        Return to Admin
                                    </Button>
                                </div>
                            </AlertDescription>
                        </Alert>
                    </div>
                )}
            </div>
        </div>
        </>
    );
}
