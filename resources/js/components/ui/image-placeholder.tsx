import { cn } from '@/lib/utils';
import { ImageIcon } from 'lucide-react';
import React from 'react';

interface ImagePlaceholderProps {
    className?: string;
    width?: number;
    height?: number;
    text?: string;
    showIcon?: boolean;
}

export function ImagePlaceholder({ 
    className, 
    width = 400, 
    height = 300, 
    text = "No Image Available",
    showIcon = true 
}: ImagePlaceholderProps) {
    return (
        <div 
            className={cn(
                "flex flex-col items-center justify-center bg-gray-50 border-2 border-dashed border-gray-200 rounded-lg",
                className
            )}
            style={{ width, height }}
        >
            {showIcon && (
                <ImageIcon className="w-12 h-12 text-gray-400 mb-2" />
            )}
            <span className="text-sm text-gray-500 font-medium">{text}</span>
        </div>
    );
}

interface ImageWithFallbackProps extends React.ImgHTMLAttributes<HTMLImageElement> {
    fallbackClassName?: string;
    fallbackText?: string;
    showFallbackIcon?: boolean;
}

export function ImageWithFallback({
    src,
    alt,
    className,
    fallbackClassName,
    fallbackText = "Image not available",
    showFallbackIcon = true,
    onError,
    ...props
}: ImageWithFallbackProps) {
    const [hasError, setHasError] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);
    const [imageLoaded, setImageLoaded] = React.useState(false);

    // Reset states when src changes
    React.useEffect(() => {
        if (src) {
            setHasError(false);
            setIsLoading(true);
            setImageLoaded(false);
        } else {
            setHasError(true);
            setIsLoading(false);
            setImageLoaded(false);
        }
    }, [src]);

    const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
        setHasError(true);
        setIsLoading(false);
        setImageLoaded(false);
        if (onError) {
            onError(e);
        }
    };

    const handleLoad = () => {
        setIsLoading(false);
        setHasError(false);
        setImageLoaded(true);
    };

    // Show fallback if there's an error or no src
    if (hasError || !src) {
        return (
            <ImagePlaceholder
                className={cn(className, fallbackClassName)}
                text={fallbackText}
                showIcon={showFallbackIcon}
            />
        );
    }

    return (
        <div className={cn("relative w-full h-full")}>
            {/* Loading placeholder */}
            {isLoading && !imageLoaded && (
                <div className={cn(
                    "absolute inset-0 flex items-center justify-center bg-gray-100 animate-pulse rounded-inherit",
                    fallbackClassName
                )}>
                    {showFallbackIcon && fallbackText && (
                        <span className="text-xs text-gray-500">Loading...</span>
                    )}
                </div>
            )}

            {/* Actual image */}
            <img
                src={src}
                alt={alt}
                className={cn(
                    className,
                    "transition-opacity duration-200",
                    imageLoaded ? 'opacity-100' : 'opacity-0'
                )}
                onError={handleError}
                onLoad={handleLoad}
                {...props}
            />
        </div>
    );
}
