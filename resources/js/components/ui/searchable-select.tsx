import { But<PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from "@/components/ui/command"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { Check, ChevronDown, Search, X } from "lucide-react"
import * as React from "react"
import { useCallback, useEffect, useRef, useState } from "react"

export interface SearchableSelectOption {
  value: string
  label: string
  description?: string
  image?: string
  disabled?: boolean
  data?: any
}

interface SearchableSelectProps {
  options: SearchableSelectOption[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyText?: string
  disabled?: boolean
  className?: string
  onSearch?: (query: string) => void
  loading?: boolean
  allowClear?: boolean
  renderOption?: (option: SearchableSelectOption) => React.ReactNode
  renderSelected?: (option: SearchableSelectOption) => React.ReactNode
}

export function SearchableSelect({
  options = [],
  value,
  onValueChange,
  placeholder = "Select an option...",
  searchPlaceholder = "Search...",
  emptyText = "No options found",
  disabled = false,
  className,
  onSearch,
  loading = false,
  allowClear = false,
  renderOption,
  renderSelected,
}: SearchableSelectProps) {
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const searchTimeoutRef = useRef<NodeJS.Timeout>()

  const selectedOption = options.find(option => option.value === value)

  // Debounced search with improved performance
  const debouncedSearch = useCallback((query: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    searchTimeoutRef.current = setTimeout(() => {
      if (onSearch) {
        onSearch(query)
      }
    }, query.length < 2 ? 500 : 300) // Longer delay for short queries
  }, [onSearch])

  useEffect(() => {
    if (onSearch) {
      if (searchQuery.length >= 1) {
        debouncedSearch(searchQuery)
      } else if (searchQuery.length === 0) {
        // Load initial options when query is empty
        debouncedSearch('')
      }
    }
  }, [searchQuery, debouncedSearch])

  // Load initial options when component mounts and onSearch is provided
  useEffect(() => {
    if (onSearch && options.length === 0) {
      debouncedSearch('')
    }
  }, []) // Only run on mount

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [])

  // Filter options based on search query if no external search handler
  const filteredOptions = React.useMemo(() => {
    if (onSearch) {
      // External search handling
      return options
    }

    if (!searchQuery) {
      return options
    }

    return options.filter(option =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      option.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }, [options, searchQuery])

  const handleSelect = (selectedValue: string) => {
    if (selectedValue === value) {
      // If same value is selected, close the popover
      setOpen(false)
      return
    }

    onValueChange?.(selectedValue)
    setOpen(false)
    setSearchQuery("")
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onValueChange?.("")
    setSearchQuery("")
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setOpen(false)
    } else if (e.key === "Enter" && !open) {
      setOpen(true)
    }
  }

  const defaultRenderOption = (option: SearchableSelectOption) => (
    <div className="flex items-center gap-2 w-full">
      {option.image && (
        <img
          src={option.image}
          alt={option.label}
          className="w-4 h-4 object-contain flex-shrink-0"
        />
      )}
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{option.label}</div>
        {option.description && (
          <div className="text-sm text-muted-foreground truncate">
            {option.description}
          </div>
        )}
      </div>
    </div>
  )

  const defaultRenderSelected = (option: SearchableSelectOption) => (
    <div className="flex items-center gap-2 flex-1 min-w-0">
      {option.image && (
        <img
          src={option.image}
          alt={option.label}
          className="w-4 h-4 object-contain flex-shrink-0"
        />
      )}
      <span className="truncate">{option.label}</span>
    </div>
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            !selectedOption && "text-muted-foreground",
            className
          )}
          disabled={disabled}
          onKeyDown={handleKeyDown}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {selectedOption ? (
              renderSelected ? renderSelected(selectedOption) : defaultRenderSelected(selectedOption)
            ) : (
              <span className="truncate">{placeholder}</span>
            )}
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            {allowClear && selectedOption && (
              <X
                className="h-4 w-4 opacity-50 hover:opacity-100"
                onClick={handleClear}
              />
            )}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] max-w-[95vw] p-0" align="start">
        <Command shouldFilter={!onSearch}>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
              autoFocus
            />
          </div>
          <CommandList className="max-h-[300px] overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                Loading...
              </div>
            ) : filteredOptions.length === 0 ? (
              <CommandEmpty>{emptyText}</CommandEmpty>
            ) : (
              <CommandGroup>
                {filteredOptions.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={handleSelect}
                    disabled={option.disabled}
                    className="cursor-pointer"
                  >
                    {renderOption ? renderOption(option) : defaultRenderOption(option)}
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        value === option.value ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
