import { cn } from '@/lib/utils';
import React, { useEffect, useRef, useState } from 'react';

interface AdContainerProps {
    zone: 'header' | 'sidebar' | 'content' | 'footer' | 'mobile';
    size: string;
    page: string;
    className?: string;
    responsive?: boolean;
    style?: React.CSSProperties;
    adSlot?: string;
    adFormat?: string;
    fullWidthResponsive?: boolean;
}

/**
 * AdContainer Component
 * 
 * Renders a Google AdSense ad unit with proper configuration
 * Handles responsive sizing and lazy loading
 */
export function AdContainer({
    zone,
    size,
    page,
    className,
    responsive = true,
    style,
    adSlot,
    adFormat = 'auto',
    fullWidthResponsive = true,
    ...props
}: AdContainerProps) {
    const adRef = useRef<HTMLDivElement>(null);
    const [isLoaded, setIsLoaded] = useState(false);
    const [hasError, setHasError] = useState(false);

    // Get AdSense configuration from environment or config
    const adClient = import.meta.env.VITE_ADSENSE_CLIENT_ID || 'ca-pub-0000000000000000';
    const defaultAdSlot = adSlot || '0000000000';

    // Ad size configurations
    const adSizes = {
        'header-desktop': { width: 728, height: 90 },
        'header-mobile': { width: 320, height: 50 },
        'sidebar-desktop': { width: 300, height: 600 },
        'sidebar-small': { width: 160, height: 600 },
        'content-rectangle': { width: 300, height: 250 },
        'footer-desktop': { width: 728, height: 90 },
        'footer-mobile': { width: 320, height: 100 },
        'mobile-banner': { width: 320, height: 50 },
        'mobile-rectangle': { width: 300, height: 250 },
    };

    const sizeConfig = adSizes[size as keyof typeof adSizes] || { width: 300, height: 250 };

    useEffect(() => {
        const loadAd = () => {
            try {
                // Check if adsbygoogle is available
                if (typeof window !== 'undefined' && (window as any).adsbygoogle) {
                    // Push the ad configuration
                    ((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
                    setIsLoaded(true);
                } else {
                    // Retry after a short delay if adsbygoogle is not ready
                    setTimeout(loadAd, 100);
                }
            } catch (error) {
                console.error('Error loading AdSense ad:', error);
                setHasError(true);
            }
        };

        // Load ad with a small delay to ensure proper rendering
        const timer = setTimeout(loadAd, 100);

        return () => clearTimeout(timer);
    }, []);

    // Don't render if there's an error
    if (hasError) {
        return null;
    }

    return (
        <div
            className={cn(
                'ad-container',
                `ad-zone-${zone}`,
                'flex items-center justify-center',
                'border border-dashed border-gray-200 dark:border-gray-700',
                'bg-gray-50 dark:bg-gray-800/50',
                'rounded-lg',
                'transition-all duration-200',
                className
            )}
            style={{
                minWidth: responsive ? 'auto' : sizeConfig.width,
                minHeight: responsive ? 'auto' : sizeConfig.height,
                ...style,
            }}
            data-zone={zone}
            data-page={page}
            {...props}
        >
            <ins
                ref={adRef}
                className="adsbygoogle"
                style={{
                    display: 'block',
                    width: responsive ? '100%' : sizeConfig.width,
                    height: responsive ? 'auto' : sizeConfig.height,
                }}
                data-ad-client={adClient}
                data-ad-slot={defaultAdSlot}
                data-ad-format={responsive ? adFormat : 'fixed'}
                data-full-width-responsive={responsive && fullWidthResponsive ? 'true' : 'false'}
                data-ad-zone={zone}
                data-ad-page={page}
            />
            
            {/* Loading indicator */}
            {!isLoaded && (
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-xs text-gray-400 dark:text-gray-500">
                        Loading ad...
                    </div>
                </div>
            )}
        </div>
    );
}

/**
 * Predefined ad components for common placements
 */

export function HeaderBannerAd({ page, className, ...props }: { page: string; className?: string }) {
    return (
        <AdContainer
            zone="header"
            size="header-desktop"
            page={page}
            className={cn('hidden md:block', className)}
            responsive={true}
            {...props}
        />
    );
}

export function MobileHeaderBannerAd({ page, className, ...props }: { page: string; className?: string }) {
    return (
        <AdContainer
            zone="header"
            size="header-mobile"
            page={page}
            className={cn('block md:hidden', className)}
            responsive={true}
            {...props}
        />
    );
}

export function SidebarAd({ page, className, ...props }: { page: string; className?: string }) {
    return (
        <AdContainer
            zone="sidebar"
            size="sidebar-desktop"
            page={page}
            className={cn('hidden lg:block', className)}
            responsive={false}
            {...props}
        />
    );
}

export function ContentRectangleAd({ page, className, ...props }: { page: string; className?: string }) {
    return (
        <AdContainer
            zone="content"
            size="content-rectangle"
            page={page}
            className={className}
            responsive={true}
            {...props}
        />
    );
}

export function FooterBannerAd({ page, className, ...props }: { page: string; className?: string }) {
    return (
        <AdContainer
            zone="footer"
            size="footer-desktop"
            page={page}
            className={cn('hidden md:block', className)}
            responsive={true}
            {...props}
        />
    );
}

export function MobileFooterBannerAd({ page, className, ...props }: { page: string; className?: string }) {
    return (
        <AdContainer
            zone="footer"
            size="footer-mobile"
            page={page}
            className={cn('block md:hidden', className)}
            responsive={true}
            {...props}
        />
    );
}

export function MobileBannerAd({ page, className, ...props }: { page: string; className?: string }) {
    return (
        <AdContainer
            zone="mobile"
            size="mobile-banner"
            page={page}
            className={cn('block lg:hidden', className)}
            responsive={true}
            {...props}
        />
    );
}

export function MobileRectangleAd({ page, className, ...props }: { page: string; className?: string }) {
    return (
        <AdContainer
            zone="mobile"
            size="mobile-rectangle"
            page={page}
            className={cn('block lg:hidden', className)}
            responsive={true}
            {...props}
        />
    );
}
