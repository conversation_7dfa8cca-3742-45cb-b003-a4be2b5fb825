// Core ad components
export { AdContainer } from './AdContainer';
export { AdConfigScript, AdScript, AutoAdsScript } from './AdScript';

// Predefined ad components
export {
    ContentRectangleAd,
    FooterBannerAd, HeaderBannerAd, MobileBannerAd, MobileFooterBannerAd, MobileHeaderBannerAd, MobileRectangleAd, SidebarAd
} from './AdContainer';

// Ad wrapper components
export {
    AdPlaceholder, AdWrapper, ConditionalAd, ContentAdZone,
    FooterAdZone, HeaderAdZone, MobileAdZone, SidebarAdZone, SmartAd
} from './AdWrapper';

// Ad layout components
export {
    AdLayoutConfigs, AdLayoutWrapper,
    ContentAdSeparator, InContentAd, ResponsiveAdGrid
} from './AdLayoutWrapper';

// Hooks
export { useAdState, useAdTracking, useAds } from '@/hooks/use-ads';

// Types
export interface AdZoneProps {
    page: string;
    className?: string;
}

export interface AdContainerProps {
    zone: 'header' | 'sidebar' | 'content' | 'footer' | 'mobile';
    size: string;
    page: string;
    className?: string;
    responsive?: boolean;
    style?: React.CSSProperties;
    adSlot?: string;
    adFormat?: string;
    fullWidthResponsive?: boolean;
}

export interface AdConfiguration {
    enabled: boolean;
    zones: {
        header: boolean;
        sidebar: boolean;
        content: boolean;
        footer: boolean;
        mobile: boolean;
    };
    frequency: {
        maxAdsPerPage: number;
        delaySeconds: number;
        gracePeriodMinutes: number;
    };
    targeting: {
        page: string;
        userType: 'free' | 'premium' | 'admin' | 'guest';
        deviceType: 'desktop' | 'tablet' | 'mobile';
    };
}
