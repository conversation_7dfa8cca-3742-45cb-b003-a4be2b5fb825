import { Head } from '@inertiajs/react';
import { useEffect } from 'react';

interface AdScriptProps {
    clientId?: string;
    enabled?: boolean;
}

/**
 * AdScript Component
 * 
 * Loads the Google AdSense script and initializes the adsbygoogle array
 * Should be included once in the application head
 */
export function AdScript({
    clientId = import.meta.env.VITE_ADSENSE_CLIENT_ID || 'ca-pub-0000000000000000',
    enabled = true
}: AdScriptProps) {
    useEffect(() => {
        if (!enabled || typeof window === 'undefined') {
            return;
        }

        // Initialize adsbygoogle array if it doesn't exist
        if (!(window as any).adsbygoogle) {
            (window as any).adsbygoogle = [];
        }

        // Log AdSense initialization for debugging
        if (process.env.NODE_ENV === 'development') {
            console.log('AdSense script initialized with client ID:', clientId);
        }
    }, [clientId, enabled]);

    if (!enabled) {
        return null;
    }

    return (
        <Head>
            <script
                async
                src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${clientId}`}
                crossOrigin="anonymous"
            />
        </Head>
    );
}

/**
 * AdSense Auto Ads Script
 * 
 * Enables Google AdSense Auto Ads for automatic ad placement
 * Use this in addition to manual ad placements if desired
 */
export function AutoAdsScript({
    clientId = import.meta.env.VITE_ADSENSE_CLIENT_ID || 'ca-pub-0000000000000000',
    enabled = false
}: AdScriptProps) {
    if (!enabled) {
        return null;
    }

    return (
        <Head>
            <script
                async
                src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${clientId}`}
                crossOrigin="anonymous"
            />
            <script
                dangerouslySetInnerHTML={{
                    __html: `
                        (adsbygoogle = window.adsbygoogle || []).push({
                            google_ad_client: "${clientId}",
                            enable_page_level_ads: true
                        });
                    `
                }}
            />
        </Head>
    );
}

/**
 * AdSense Configuration Script
 * 
 * Provides global configuration for AdSense ads
 */
export function AdConfigScript({
    clientId = import.meta.env.VITE_ADSENSE_CLIENT_ID || 'ca-pub-0000000000000000',
    enabled = true,
    config = {}
}: AdScriptProps & { config?: Record<string, any> }) {
    if (!enabled) {
        return null;
    }

    const defaultConfig = {
        // Default AdSense configuration
        overlays: true,
        privacy_settings: {
            child_directed_treatment: false,
            under_age_of_consent: false,
            restricted_data_processing: false,
        },
        ...config
    };

    return (
        <Head>
            <script
                dangerouslySetInnerHTML={{
                    __html: `
                        window.adsbygoogle = window.adsbygoogle || [];
                        window.adsenseConfig = ${JSON.stringify(defaultConfig)};
                        
                        // Global AdSense error handler
                        window.addEventListener('error', function(e) {
                            if (e.target && e.target.src && e.target.src.includes('googlesyndication.com')) {
                                console.warn('AdSense script error:', e);
                            }
                        });
                    `
                }}
            />
        </Head>
    );
}
