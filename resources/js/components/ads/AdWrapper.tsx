import React from 'react';
import { useAds, useAdTracking } from '@/hooks/use-ads';
import { 
    Ad<PERSON><PERSON>r, 
    HeaderBannerAd, 
    MobileHeaderBannerAd, 
    SidebarAd, 
    ContentRectangleAd, 
    FooterBannerAd, 
    MobileFooterBannerAd,
    MobileBannerAd,
    MobileRectangleAd
} from './AdContainer';

interface AdWrapperProps {
    zone: 'header' | 'sidebar' | 'content' | 'footer' | 'mobile';
    page: string;
    children?: React.ReactNode;
    className?: string;
    fallback?: React.ReactNode;
}

/**
 * AdWrapper Component
 * 
 * Wraps ad components with user-based display logic
 * Only renders ads for free users, respects admin settings
 */
export function AdWrapper({ zone, page, children, className, fallback }: AdWrapperProps) {
    const { shouldShowAds, canShowZone } = useAds();
    const { trackAdImpression } = useAdTracking();

    // Don't render anything if ads shouldn't be shown
    if (!shouldShowAds || !canShowZone(zone)) {
        return fallback ? <>{fallback}</> : null;
    }

    // Track impression when component mounts
    React.useEffect(() => {
        trackAdImpression(zone, page);
    }, [zone, page, trackAdImpression]);

    return (
        <div className={className} data-ad-wrapper={zone}>
            {children}
        </div>
    );
}

/**
 * Predefined ad wrapper components for easy use
 */

export function HeaderAdZone({ page, className }: { page: string; className?: string }) {
    return (
        <AdWrapper zone="header" page={page} className={className}>
            <div className="w-full space-y-2">
                <HeaderBannerAd page={page} />
                <MobileHeaderBannerAd page={page} />
            </div>
        </AdWrapper>
    );
}

export function SidebarAdZone({ page, className }: { page: string; className?: string }) {
    return (
        <AdWrapper zone="sidebar" page={page} className={className}>
            <SidebarAd page={page} />
        </AdWrapper>
    );
}

export function ContentAdZone({ page, className }: { page: string; className?: string }) {
    return (
        <AdWrapper zone="content" page={page} className={className}>
            <ContentRectangleAd page={page} />
        </AdWrapper>
    );
}

export function FooterAdZone({ page, className }: { page: string; className?: string }) {
    return (
        <AdWrapper zone="footer" page={page} className={className}>
            <div className="w-full space-y-2">
                <FooterBannerAd page={page} />
                <MobileFooterBannerAd page={page} />
            </div>
        </AdWrapper>
    );
}

export function MobileAdZone({ page, className, type = 'banner' }: { 
    page: string; 
    className?: string; 
    type?: 'banner' | 'rectangle' 
}) {
    return (
        <AdWrapper zone="mobile" page={page} className={className}>
            {type === 'banner' ? (
                <MobileBannerAd page={page} />
            ) : (
                <MobileRectangleAd page={page} />
            )}
        </AdWrapper>
    );
}

/**
 * Smart Ad Component
 * 
 * Automatically selects the best ad format based on screen size and zone
 */
export function SmartAd({ 
    zone, 
    page, 
    className,
    priority = 'medium'
}: { 
    zone: 'header' | 'sidebar' | 'content' | 'footer' | 'mobile';
    page: string;
    className?: string;
    priority?: 'high' | 'medium' | 'low';
}) {
    const { shouldShowAds, canShowZone } = useAds();

    if (!shouldShowAds || !canShowZone(zone)) {
        return null;
    }

    const adProps = {
        page,
        className,
        'data-priority': priority,
    };

    switch (zone) {
        case 'header':
            return <HeaderAdZone {...adProps} />;
        case 'sidebar':
            return <SidebarAdZone {...adProps} />;
        case 'content':
            return <ContentAdZone {...adProps} />;
        case 'footer':
            return <FooterAdZone {...adProps} />;
        case 'mobile':
            return <MobileAdZone {...adProps} />;
        default:
            return null;
    }
}

/**
 * Ad Placeholder Component
 * 
 * Shows a placeholder when ads are disabled or for premium users
 */
export function AdPlaceholder({ 
    zone, 
    message,
    className 
}: { 
    zone: string; 
    message?: string;
    className?: string;
}) {
    const { userType } = useAds();

    const defaultMessage = userType === 'premium' 
        ? 'Ad-free experience for premium users' 
        : 'Ads disabled';

    return (
        <div 
            className={`ad-placeholder border border-dashed border-gray-200 dark:border-gray-700 rounded-lg p-4 text-center text-sm text-gray-500 dark:text-gray-400 ${className}`}
            data-zone={zone}
        >
            {message || defaultMessage}
        </div>
    );
}

/**
 * Conditional Ad Component
 * 
 * Shows ads to free users, placeholder to premium users, nothing to admins
 */
export function ConditionalAd({
    zone,
    page,
    className,
    showPlaceholder = false,
    placeholderMessage,
}: {
    zone: 'header' | 'sidebar' | 'content' | 'footer' | 'mobile';
    page: string;
    className?: string;
    showPlaceholder?: boolean;
    placeholderMessage?: string;
}) {
    const { shouldShowAds, userType } = useAds();

    if (shouldShowAds) {
        return <SmartAd zone={zone} page={page} className={className} />;
    }

    if (showPlaceholder && userType !== 'admin') {
        return (
            <AdPlaceholder 
                zone={zone} 
                message={placeholderMessage}
                className={className}
            />
        );
    }

    return null;
}
