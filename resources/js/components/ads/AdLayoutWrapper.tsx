import React from 'react';
import { HeaderAdZone, FooterAdZone, MobileAdZone } from './AdWrapper';
import { useAds } from '@/hooks/use-ads';

interface AdLayoutWrapperProps {
    children: React.ReactNode;
    page: string;
    showHeaderAd?: boolean;
    showFooterAd?: boolean;
    showMobileAds?: boolean;
    className?: string;
}

/**
 * AdLayoutWrapper Component
 * 
 * Wraps page content with ad zones in appropriate positions
 * Automatically handles responsive ad placement
 */
export function AdLayoutWrapper({
    children,
    page,
    showHeaderAd = true,
    showFooterAd = true,
    showMobileAds = true,
    className = '',
}: AdLayoutWrapperProps) {
    const { shouldShowAds } = useAds();

    if (!shouldShowAds) {
        return <div className={className}>{children}</div>;
    }

    return (
        <div className={`ad-layout-wrapper ${className}`}>
            {/* Header Ad Zone */}
            {showHeaderAd && (
                <div className="ad-header-zone mb-4">
                    <HeaderAdZone 
                        page={page}
                        className="w-full flex justify-center"
                    />
                </div>
            )}

            {/* Mobile Top Ad - Only on mobile */}
            {showMobileAds && (
                <div className="ad-mobile-top-zone mb-4 block lg:hidden">
                    <MobileAdZone 
                        page={page}
                        type="banner"
                        className="w-full flex justify-center"
                    />
                </div>
            )}

            {/* Main Content */}
            <div className="ad-content-wrapper">
                {children}
            </div>

            {/* Mobile Bottom Ad - Only on mobile */}
            {showMobileAds && (
                <div className="ad-mobile-bottom-zone mt-4 block lg:hidden">
                    <MobileAdZone 
                        page={page}
                        type="rectangle"
                        className="w-full flex justify-center"
                    />
                </div>
            )}

            {/* Footer Ad Zone */}
            {showFooterAd && (
                <div className="ad-footer-zone mt-6">
                    <FooterAdZone 
                        page={page}
                        className="w-full flex justify-center"
                    />
                </div>
            )}
        </div>
    );
}

/**
 * Content Ad Separator Component
 * 
 * Use this to add content ads between sections
 */
export function ContentAdSeparator({ 
    page, 
    className = '',
    spacing = 'my-6'
}: { 
    page: string; 
    className?: string;
    spacing?: string;
}) {
    const { shouldShowAds, canShowZone } = useAds();

    if (!shouldShowAds || !canShowZone('content')) {
        return null;
    }

    return (
        <div className={`content-ad-separator ${spacing} ${className}`}>
            <div className="w-full flex justify-center">
                <div className="max-w-md w-full">
                    {/* Content Rectangle Ad */}
                    <div className="border-t border-b border-gray-200 dark:border-gray-700 py-4">
                        <div className="text-xs text-gray-400 text-center mb-2">Advertisement</div>
                        <MobileAdZone 
                            page={page}
                            type="rectangle"
                            className="w-full flex justify-center"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}

/**
 * Responsive Ad Grid Component
 * 
 * Creates a responsive layout with sidebar ads on desktop
 */
export function ResponsiveAdGrid({
    children,
    page,
    showSidebarAd = true,
    className = '',
}: {
    children: React.ReactNode;
    page: string;
    showSidebarAd?: boolean;
    className?: string;
}) {
    const { shouldShowAds, canShowZone } = useAds();

    const showSidebar = shouldShowAds && canShowZone('sidebar') && showSidebarAd;

    return (
        <div className={`responsive-ad-grid ${className}`}>
            <div className={`grid gap-6 ${showSidebar ? 'lg:grid-cols-[1fr_300px]' : 'grid-cols-1'}`}>
                {/* Main Content */}
                <div className="main-content">
                    {children}
                </div>

                {/* Sidebar Ad - Desktop Only */}
                {showSidebar && (
                    <div className="sidebar-ad-container hidden lg:block">
                        <div className="sticky top-4 space-y-4">
                            <div className="text-xs text-gray-400 text-center">Advertisement</div>
                            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-2">
                                <MobileAdZone 
                                    page={page}
                                    type="rectangle"
                                    className="w-full flex justify-center"
                                />
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

/**
 * In-Content Ad Component
 * 
 * Use this to insert ads within content (e.g., between paragraphs, in lists)
 */
export function InContentAd({
    page,
    className = '',
    style = 'native', // 'native' | 'banner' | 'rectangle'
}: {
    page: string;
    className?: string;
    style?: 'native' | 'banner' | 'rectangle';
}) {
    const { shouldShowAds, canShowZone } = useAds();

    if (!shouldShowAds || !canShowZone('content')) {
        return null;
    }

    const adType = style === 'banner' ? 'banner' : 'rectangle';

    return (
        <div className={`in-content-ad ${className}`}>
            <div className="my-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-400 text-center mb-2">Sponsored</div>
                <MobileAdZone 
                    page={page}
                    type={adType}
                    className="w-full flex justify-center"
                />
            </div>
        </div>
    );
}

/**
 * Page-specific ad layout configurations
 */
export const AdLayoutConfigs = {
    home: {
        showHeaderAd: true,
        showFooterAd: true,
        showMobileAds: true,
        maxAdsPerPage: 3,
    },
    search: {
        showHeaderAd: true,
        showFooterAd: true,
        showMobileAds: true,
        showSidebarAd: true,
        maxAdsPerPage: 4,
    },
    dashboard: {
        showHeaderAd: false,
        showFooterAd: false,
        showMobileAds: true,
        showSidebarAd: true,
        maxAdsPerPage: 2,
    },
    details: {
        showHeaderAd: true,
        showFooterAd: true,
        showMobileAds: true,
        showSidebarAd: true,
        maxAdsPerPage: 4,
    },
} as const;
