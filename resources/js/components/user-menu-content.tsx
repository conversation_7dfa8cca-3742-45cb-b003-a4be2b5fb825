import { Badge } from '@/components/ui/badge';
import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { UserInfo } from '@/components/user-info';
import { useMobileNavigation } from '@/hooks/use-mobile-navigation';
import { type User } from '@/types';
import { Link, router } from '@inertiajs/react';
import {
    BarChart3,
    Bell,
    Crown,
    HelpCircle,
    Keyboard,
    LogOut,
    Settings,
    Shield,
    User as UserIcon
} from 'lucide-react';
import { Dispatch, SetStateAction } from 'react';

interface UserMenuContentProps {
    user: User;
    isAdmin?: boolean;
    isAdminView?: boolean;
    setIsAdminView?: Dispatch<SetStateAction<boolean>>;
}

export function UserMenuContent({ user, isAdmin = false, isAdminView = false, setIsAdminView }: UserMenuContentProps) {
    const cleanup = useMobileNavigation();

    const handleLogout = () => {
        cleanup();
        router.flushAll();
    };

    const toggleView = () => {
        if (setIsAdminView) {
            setIsAdminView(prev => !prev);
        }
        cleanup();
    };

    return (
        <>
            <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-3 px-2 py-2 text-left">
                    <UserInfo user={user} showEmail={true} />
                    <div className="flex flex-col gap-1 items-start text-left">
                        {user.subscription_plan === 'premium' && (
                            <Badge variant="outline" className="text-xs bg-gradient-to-r from-amber-50 to-yellow-50 text-amber-700 border-amber-200 dark:from-amber-950 dark:to-yellow-950 dark:text-amber-300 dark:border-amber-800">
                                <Crown className="w-3 h-3 mr-1" />
                                Premium
                            </Badge>
                        )}
                        {isAdmin && (
                            <Badge variant="outline" className="text-xs bg-gradient-to-r from-orange-50 to-red-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-red-950 dark:text-orange-300 dark:border-orange-800">
                                <Shield className="w-3 h-3 mr-1" />
                                Admin
                            </Badge>
                        )}
                    </div>
                </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />

            {/* Admin View Switcher */}
            {isAdmin && (
                <>
                    <DropdownMenuGroup>
                        <DropdownMenuItem onSelect={toggleView} className="flex items-center gap-3 p-3">
                            {isAdminView ? (
                                <>
                                    <UserIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                    <div className="flex-1 text-left">
                                        <div className="font-medium text-left">Switch to User View</div>
                                        <div className="text-xs text-muted-foreground text-left">Experience as a regular user</div>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <Shield className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                                    <div className="flex-1 text-left">
                                        <div className="font-medium text-left">Switch to Admin View</div>
                                        <div className="text-xs text-muted-foreground text-left">Access admin features</div>
                                    </div>
                                </>
                            )}
                        </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                </>
            )}

            {/* Main Menu Items */}
            <DropdownMenuGroup>
                <DropdownMenuItem asChild>
                    <Link className="flex items-center gap-3 p-3" href={route('notifications.index')} as="button" prefetch onClick={cleanup}>
                        <Bell className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <div className="flex-1 text-left">
                            <div className="font-medium text-left">Notifications</div>
                            <div className="text-xs text-muted-foreground text-left">View your alerts</div>
                        </div>
                    </Link>
                </DropdownMenuItem>

                {isAdmin && (
                    <DropdownMenuItem asChild>
                        <Link className="flex items-center gap-3 p-3" href={route('admin.analytics.index')} as="button" prefetch onClick={cleanup}>
                            <BarChart3 className="h-4 w-4 text-green-600 dark:text-green-400" />
                            <div className="flex-1 text-left">
                                <div className="font-medium text-left">Analytics</div>
                                <div className="text-xs text-muted-foreground text-left">System insights</div>
                            </div>
                        </Link>
                    </DropdownMenuItem>
                )}

                <DropdownMenuItem asChild>
                    <Link className="flex items-center gap-3 p-3" href={route('profile.edit')} as="button" prefetch onClick={cleanup}>
                        <Settings className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                        <div className="flex-1 text-left">
                            <div className="font-medium text-left">Settings</div>
                            <div className="text-xs text-muted-foreground text-left">Manage your account</div>
                        </div>
                    </Link>
                </DropdownMenuItem>

                <DropdownMenuItem className="flex items-center gap-3 p-3">
                    <HelpCircle className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    <div className="flex-1 text-left">
                        <div className="font-medium text-left">Help & Support</div>
                        <div className="text-xs text-muted-foreground text-left">Get assistance</div>
                    </div>
                </DropdownMenuItem>

                <DropdownMenuItem className="flex items-center gap-3 p-3">
                    <Keyboard className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    <div className="flex-1 text-left">
                        <div className="font-medium text-left">Keyboard Shortcuts</div>
                        <div className="text-xs text-muted-foreground text-left">Press ⌘K to search</div>
                    </div>
                </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            {/* Logout */}
            <DropdownMenuItem asChild>
                <Link className="flex items-center gap-3 p-3 text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400" method="post" href={route('logout')} as="button" onClick={handleLogout}>
                    <LogOut className="h-4 w-4" />
                    <div className="flex-1 text-left">
                        <div className="font-medium text-left">Log out</div>
                        <div className="text-xs text-muted-foreground text-left">Sign out of your account</div>
                    </div>
                </Link>
            </DropdownMenuItem>
        </>
    );
}
