import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ImageWithFallback } from '@/components/ui/image-placeholder';
import { 
    ZoomIn, 
    X, 
    ChevronLeft, 
    ChevronRight, 
    Smartphone,
    Image as ImageIcon 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileModel {
    id: number;
    name: string;
    image_url?: string;
    images?: string[];
    brand: {
        id: number;
        name: string;
        logo_url?: string;
    };
}

interface ModelImagePreviewProps {
    model: MobileModel;
    className?: string;
}

export default function ModelImagePreview({ model, className }: ModelImagePreviewProps) {
    const [isPreviewOpen, setIsPreviewOpen] = useState(false);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);
    const [imageError, setImageError] = useState(false);

    // Get all available images
    const allImages = React.useMemo(() => {
        const images: string[] = [];
        if (model.image_url) images.push(model.image_url);
        if (model.images && model.images.length > 0) {
            model.images.forEach(img => {
                if (!images.includes(img)) images.push(img);
            });
        }
        return images;
    }, [model.image_url, model.images]);

    const hasImages = allImages.length > 0;
    const currentImage = hasImages ? allImages[selectedImageIndex] : null;

    const handleImageClick = () => {
        if (hasImages) {
            setIsPreviewOpen(true);
        }
    };

    const handleClosePreview = () => {
        setIsPreviewOpen(false);
    };

    const handlePreviousImage = () => {
        if (allImages.length > 1) {
            setSelectedImageIndex(prev => 
                prev === 0 ? allImages.length - 1 : prev - 1
            );
        }
    };

    const handleNextImage = () => {
        if (allImages.length > 1) {
            setSelectedImageIndex(prev => 
                prev === allImages.length - 1 ? 0 : prev + 1
            );
        }
    };

    // Keyboard navigation
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (!isPreviewOpen) return;

            switch (event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    handlePreviousImage();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    handleNextImage();
                    break;
                case 'Escape':
                    event.preventDefault();
                    handleClosePreview();
                    break;
            }
        };

        if (isPreviewOpen) {
            document.addEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'unset';
        };
    }, [isPreviewOpen, allImages.length]);

    const handleImageError = () => {
        setImageError(true);
    };

    const renderFallback = () => (
        <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-300">
            {model.brand.logo_url ? (
                <img
                    src={model.brand.logo_url}
                    alt={model.brand.name}
                    className="w-16 h-16 object-contain mb-3 opacity-60"
                    onError={(e) => {
                        e.currentTarget.style.display = 'none';
                    }}
                />
            ) : (
                <Smartphone className="w-16 h-16 text-gray-400 mb-3" />
            )}
            <p className="text-sm text-gray-500 text-center px-4">
                No image available
            </p>
            <p className="text-xs text-gray-400 text-center px-4 mt-1">
                {model.brand.name} {model.name}
            </p>
        </div>
    );

    return (
        <>
            <Card className={cn("overflow-hidden", className)}>
                <CardContent className="p-0">
                    <div className="relative group">
                        <div 
                            className={cn(
                                "aspect-square w-full relative overflow-hidden bg-gray-50",
                                hasImages && !imageError && "cursor-pointer"
                            )}
                            onClick={handleImageClick}
                        >
                            {hasImages && !imageError ? (
                                <>
                                    <img
                                        src={currentImage!}
                                        alt={`${model.brand.name} ${model.name}`}
                                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                                        onError={handleImageError}
                                    />
                                    
                                    {/* Hover overlay */}
                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-3">
                                            <ZoomIn className="w-6 h-6 text-gray-700" />
                                        </div>
                                    </div>

                                    {/* Image counter for multiple images */}
                                    {allImages.length > 1 && (
                                        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                                            {selectedImageIndex + 1}/{allImages.length}
                                        </div>
                                    )}
                                </>
                            ) : (
                                renderFallback()
                            )}
                        </div>

                        {/* Navigation arrows for multiple images */}
                        {hasImages && allImages.length > 1 && !imageError && (
                            <>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-white/80 hover:bg-white/90 rounded-full w-8 h-8 p-0"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handlePreviousImage();
                                    }}
                                >
                                    <ChevronLeft className="w-4 h-4" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-white/80 hover:bg-white/90 rounded-full w-8 h-8 p-0"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleNextImage();
                                    }}
                                >
                                    <ChevronRight className="w-4 h-4" />
                                </Button>
                            </>
                        )}
                    </div>

                    {/* Image info */}
                    <div className="p-4 bg-gray-50/50">
                        <div className="flex items-center gap-2">
                            <ImageIcon className="w-4 h-4 text-gray-500" />
                            <span className="text-sm font-medium text-gray-700">
                                {model.brand.name} {model.name}
                            </span>
                        </div>
                        {hasImages && !imageError && (
                            <p className="text-xs text-gray-500 mt-1">
                                Click to view full size
                            </p>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Full-size preview modal */}
            <Dialog open={isPreviewOpen} onOpenChange={handleClosePreview}>
                <DialogContent className="max-w-7xl w-[95vw] h-[95vh] p-0 overflow-hidden border-0 bg-transparent shadow-2xl">
                    <DialogTitle className="sr-only">
                        {model.brand.name} {model.name} - Image Preview
                    </DialogTitle>
                    <div className="relative w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-900 rounded-xl overflow-hidden">
                        {/* Header with close button and image info */}
                        <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/80 via-black/40 to-transparent p-4">
                            <div className="flex items-center justify-between">
                                <div className="text-white">
                                    <h3 className="text-lg font-semibold">{model.brand.name} {model.name}</h3>
                                    {allImages.length > 1 && (
                                        <p className="text-sm text-gray-300">
                                            Image {selectedImageIndex + 1} of {allImages.length}
                                        </p>
                                    )}
                                </div>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-white hover:bg-white/20 rounded-full w-10 h-10 p-0"
                                    onClick={handleClosePreview}
                                >
                                    <X className="w-5 h-5" />
                                </Button>
                            </div>
                        </div>

                        {/* Main image */}
                        <div className="flex items-center justify-center w-full h-full p-8">
                            {currentImage && (
                                <img
                                    src={currentImage}
                                    alt={`${model.brand.name} ${model.name}`}
                                    className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                                />
                            )}
                        </div>

                        {/* Navigation arrows for modal */}
                        {allImages.length > 1 && (
                            <>
                                <Button
                                    variant="ghost"
                                    size="lg"
                                    className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 rounded-full w-12 h-12 p-0"
                                    onClick={handlePreviousImage}
                                >
                                    <ChevronLeft className="w-6 h-6" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="lg"
                                    className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 rounded-full w-12 h-12 p-0"
                                    onClick={handleNextImage}
                                >
                                    <ChevronRight className="w-6 h-6" />
                                </Button>
                            </>
                        )}

                        {/* Bottom info bar */}
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4">
                            <div className="text-center text-white">
                                <p className="text-sm text-gray-300">
                                    Use arrow keys to navigate • Press ESC to close
                                </p>
                            </div>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
}
