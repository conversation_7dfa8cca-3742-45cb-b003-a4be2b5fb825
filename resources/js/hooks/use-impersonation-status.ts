import { api } from '@/utils/api';
import { useCallback, useEffect, useState } from 'react';

interface ImpersonationStatus {
    is_impersonating: boolean;
    impersonating_user_id?: number;
    original_admin_id?: number;
    expires_at?: string;
    remaining_minutes?: number;
}

export function useImpersonationStatus() {
    const [status, setStatus] = useState<ImpersonationStatus | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    const fetchStatus = useCallback(async () => {
        try {
            const response = await api.get('/api/admin/impersonation/status');
            setStatus(response.data);
        } catch (error) {
            // If there's an error (like 404 or 403), assume not impersonating
            setStatus({ is_impersonating: false });
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchStatus();
    }, [fetchStatus]);

    return {
        isImpersonating: status?.is_impersonating ?? false,
        status,
        isLoading,
        refetch: fetchStatus
    };
}
