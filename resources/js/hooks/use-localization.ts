import axios from 'axios';
import { useEffect, useState } from 'react';

interface CountryData {
    country_code: string;
    country_name: string;
    region: string;
    city: string;
    timezone: string;
    currency: string;
    is_bangladesh: boolean;
    detected_at: string;
    ip_hash: string;
    is_fallback?: boolean;
}

interface PaymentGateway {
    id: number;
    gateway_name: string;
    display_name: string;
    description: string;
    is_enabled: boolean;
    supported_countries: string[];
    supported_currencies: string[];
    primary_currency: string;
    features: string[];
    supports_subscriptions: boolean;
    supports_one_time_payments: boolean;
}

interface PaymentGatewayData {
    country_code: string;
    currency: string;
    primary_gateway: PaymentGateway | null;
    available_gateways: PaymentGateway[];
    gateway_preferences: {
        primary: string;
        secondary: string[];
        currency: string;
        recommended_order: string[];
    };
}

interface LocalizationData {
    country: CountryData;
    payment_gateways: PaymentGatewayData;
    pricing: any; // Will be defined based on pricing structure
    localization_settings: {
        auto_select_gateway: boolean;
        show_currency_conversion: boolean;
        preferred_language: string;
    };
}

interface UseLocalizationReturn {
    localizationData: LocalizationData | null;
    isLoading: boolean;
    error: string | null;
    refetch: () => Promise<void>;
    getPreferredGateway: () => string | null;
    isCountrySupported: (gatewayName: string) => boolean;
    formatPrice: (amount: number | string, currency?: string) => string;
}

export function useLocalization(): UseLocalizationReturn {
    const [localizationData, setLocalizationData] = useState<LocalizationData | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchLocalizationData = async () => {
        try {
            setIsLoading(true);
            setError(null);
            
            const response = await axios.get('/api/localization/data');
            
            if (response.data.success) {
                setLocalizationData(response.data.data);
            } else {
                throw new Error(response.data.error || 'Failed to fetch localization data');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An error occurred';
            setError(errorMessage);
            console.error('Localization fetch error:', err);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchLocalizationData();
    }, []);

    const getPreferredGateway = (): string | null => {
        if (!localizationData) return null;
        
        const { payment_gateways } = localizationData;
        
        // Return the primary gateway if available
        if (payment_gateways.primary_gateway) {
            return payment_gateways.primary_gateway.gateway_name;
        }
        
        // Fallback to first available gateway
        if (payment_gateways.available_gateways.length > 0) {
            return payment_gateways.available_gateways[0].gateway_name;
        }
        
        return null;
    };

    const isCountrySupported = (gatewayName: string): boolean => {
        if (!localizationData) return false;
        
        const gateway = localizationData.payment_gateways.available_gateways.find(
            g => g.gateway_name === gatewayName
        );
        
        if (!gateway) return false;
        
        const { country_code } = localizationData.country;
        
        // Check if gateway supports this country
        return gateway.supported_countries.includes('*') || 
               gateway.supported_countries.includes(country_code);
    };

    const formatPrice = (amount: number | string, currency?: string): string => {
        const curr = currency || localizationData?.country.currency || 'USD';

        // Convert amount to number if it's a string, handle invalid values
        const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

        // Check if the conversion resulted in a valid number
        if (isNaN(numericAmount) || numericAmount === null || numericAmount === undefined) {
            console.warn('Invalid amount passed to formatPrice:', amount);
            return `${curr} 0.00`;
        }

        switch (curr) {
            case 'BDT':
                return `৳${numericAmount.toLocaleString()}`;
            case 'USD':
                return `$${numericAmount.toFixed(2)}`;
            case 'EUR':
                return `€${numericAmount.toFixed(2)}`;
            case 'GBP':
                return `£${numericAmount.toFixed(2)}`;
            default:
                return `${curr} ${numericAmount.toFixed(2)}`;
        }
    };

    return {
        localizationData,
        isLoading,
        error,
        refetch: fetchLocalizationData,
        getPreferredGateway,
        isCountrySupported,
        formatPrice,
    };
}

// Hook for getting country-specific pricing plans
export function useLocalizedPricing(countryCode?: string) {
    const [plans, setPlans] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchPricingPlans = async () => {
        try {
            setIsLoading(true);
            setError(null);
            
            const params = countryCode ? { country: countryCode } : {};
            const response = await axios.get('/api/localization/pricing-plans', { params });
            
            if (response.data.success) {
                setPlans(response.data.data.plans);
            } else {
                throw new Error(response.data.error || 'Failed to fetch pricing plans');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An error occurred';
            setError(errorMessage);
            console.error('Pricing plans fetch error:', err);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchPricingPlans();
    }, [countryCode]);

    return {
        plans,
        isLoading,
        error,
        refetch: fetchPricingPlans,
    };
}

// Hook for getting available payment gateways
export function usePaymentGateways(countryCode?: string) {
    const [gateways, setGateways] = useState<PaymentGatewayData | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchPaymentGateways = async () => {
        try {
            setIsLoading(true);
            setError(null);
            
            const params = countryCode ? { country: countryCode } : {};
            const response = await axios.get('/api/localization/payment-gateways', { params });
            
            if (response.data.success) {
                setGateways(response.data.data);
            } else {
                throw new Error(response.data.error || 'Failed to fetch payment gateways');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An error occurred';
            setError(errorMessage);
            console.error('Payment gateways fetch error:', err);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchPaymentGateways();
    }, [countryCode]);

    return {
        gateways,
        isLoading,
        error,
        refetch: fetchPaymentGateways,
    };
}

// Utility function to get currency symbol
export function getCurrencySymbol(currency: string): string {
    const symbols: Record<string, string> = {
        'BDT': '৳',
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'CAD': 'C$',
        'AUD': 'A$',
        'JPY': '¥',
    };
    
    return symbols[currency] || currency;
}

// Utility function to format price with currency
export function formatCurrency(amount: number | string, currency: string): string {
    const symbol = getCurrencySymbol(currency);

    // Convert amount to number if it's a string, handle invalid values
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    // Check if the conversion resulted in a valid number
    if (isNaN(numericAmount) || numericAmount === null || numericAmount === undefined) {
        console.warn('Invalid amount passed to formatCurrency:', amount);
        return `${symbol}0.00`;
    }

    if (currency === 'BDT') {
        return `${symbol}${numericAmount.toLocaleString()}`;
    }

    return `${symbol}${numericAmount.toFixed(2)}`;
}
