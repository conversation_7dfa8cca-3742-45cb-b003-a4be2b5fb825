import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';

/**
 * Hook to check if the current user can manage content (admin or content manager)
 * Uses the backend-provided role and permission data for consistency
 */
export function useContentManager(): {
    isAdmin: boolean;
    isContentManager: boolean;
    canManageContent: boolean;
} {
    const { auth } = usePage<SharedData>().props;

    try {
        // Check if user exists
        if (!auth?.user) {
            return {
                isAdmin: false,
                isContentManager: false,
                canManageContent: false,
            };
        }

        // Use backend-provided values for consistency
        const isAdmin = Boolean(auth.user.isAdmin);
        const isContentManager = Boolean(auth.user.isContentManager);
        const canManageContent = Boolean(auth.user.canManageContent);

        return {
            isAdmin,
            isContentManager,
            canManageContent,
        };
    } catch (error) {
        console.error('Error in content manager detection:', error);
        return {
            isAdmin: false,
            isContentManager: false,
            canManageContent: false,
        };
    }
}
