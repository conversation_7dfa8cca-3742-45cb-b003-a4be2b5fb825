import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';

/**
 * Hook to check if the current user is an admin
 * Uses the backend-provided isAdmin property for reliable role detection
 */
export function useAdmin(): boolean {
    const { auth } = usePage<SharedData>().props;

    try {
        // Check if user exists
        if (!auth?.user) {
            return false;
        }

        // Use backend-provided isAdmin value only - no email fallback
        return <PERSON><PERSON>an(auth.user.isAdmin);
    } catch (error) {
        console.error('Error in admin detection:', error);
        return false;
    }
}
