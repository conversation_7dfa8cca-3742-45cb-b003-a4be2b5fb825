import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import { useMemo } from 'react';

interface AdConfiguration {
    enabled: boolean;
    zones: {
        header: boolean;
        sidebar: boolean;
        content: boolean;
        footer: boolean;
        mobile: boolean;
    };
    frequency: {
        maxAdsPerPage: number;
        delaySeconds: number;
        gracePeriodMinutes: number;
    };
    targeting: {
        page: string;
        userType: 'free' | 'premium' | 'admin' | 'guest';
        deviceType: 'desktop' | 'tablet' | 'mobile';
    };
}

/**
 * Hook to determine if ads should be shown to the current user
 * 
 * Ads are shown only to:
 * - Free users (not premium, not admin)
 * - Users who have been on the site for more than the grace period
 * - Pages where ads are enabled
 */
export function useAds(): {
    shouldShowAds: boolean;
    canShowZone: (zone: string) => boolean;
    adConfig: AdConfiguration;
    userType: 'free' | 'premium' | 'admin' | 'guest';
} {
    const { auth, adSettings } = usePage<SharedData & { adSettings?: any }>().props;

    const userType = useMemo(() => {
        if (!auth?.user) return 'guest';
        if (auth.user.isAdmin) return 'admin';
        if (auth.user.isPremium) return 'premium';
        return 'free';
    }, [auth]);

    const shouldShowAds = useMemo(() => {
        // First check if AdSense is globally enabled
        if (adSettings && adSettings.enabled === false) {
            return false;
        }

        // Don't show ads to admin users
        if (userType === 'admin') {
            return false;
        }

        // Don't show ads to premium users
        if (userType === 'premium') {
            return false;
        }

        // Show ads to free users and guests
        return userType === 'free' || userType === 'guest';
    }, [userType, adSettings]);

    const adConfig: AdConfiguration = useMemo(() => {
        // If ads shouldn't be shown globally, return disabled config
        if (!shouldShowAds) {
            return {
                enabled: false,
                zones: {
                    header: false,
                    sidebar: false,
                    content: false,
                    footer: false,
                    mobile: false,
                },
                frequency: {
                    maxAdsPerPage: 0,
                    delaySeconds: 0,
                    gracePeriodMinutes: 0,
                },
                targeting: {
                    page: window.location.pathname,
                    userType,
                    deviceType: getDeviceType(),
                },
            };
        }

        const defaultConfig: AdConfiguration = {
            enabled: shouldShowAds,
            zones: {
                header: true,
                sidebar: true,
                content: true,
                footer: true,
                mobile: true,
            },
            frequency: {
                maxAdsPerPage: 4,
                delaySeconds: 3,
                gracePeriodMinutes: 0, // No grace period for now
            },
            targeting: {
                page: window.location.pathname,
                userType,
                deviceType: getDeviceType(),
            },
        };

        // Merge with server-provided ad settings if available
        if (adSettings) {
            return {
                ...defaultConfig,
                ...adSettings,
                zones: {
                    ...defaultConfig.zones,
                    ...adSettings.zones,
                },
                frequency: {
                    ...defaultConfig.frequency,
                    ...adSettings.frequency,
                },
            };
        }

        return defaultConfig;
    }, [shouldShowAds, userType, adSettings]);

    const canShowZone = (zone: string): boolean => {
        if (!shouldShowAds || !adConfig.enabled) {
            return false;
        }

        return adConfig.zones[zone as keyof typeof adConfig.zones] ?? false;
    };

    return {
        shouldShowAds,
        canShowZone,
        adConfig,
        userType,
    };
}

/**
 * Hook to track ad performance and user interactions
 */
export function useAdTracking() {
    const { shouldShowAds, userType } = useAds();

    const trackAdImpression = (zone: string, page: string, adSlot?: string) => {
        if (!shouldShowAds) return;

        // Track ad impression
        try {
            // Send tracking data to backend
            fetch('/api/ads/track-impression', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    zone,
                    page,
                    ad_slot: adSlot,
                    user_type: userType,
                    timestamp: new Date().toISOString(),
                }),
            }).catch(error => {
                console.warn('Failed to track ad impression:', error);
            });
        } catch (error) {
            console.warn('Error tracking ad impression:', error);
        }
    };

    const trackAdClick = (zone: string, page: string, adSlot?: string) => {
        if (!shouldShowAds) return;

        // Track ad click
        try {
            fetch('/api/ads/track-click', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    zone,
                    page,
                    ad_slot: adSlot,
                    user_type: userType,
                    timestamp: new Date().toISOString(),
                }),
            }).catch(error => {
                console.warn('Failed to track ad click:', error);
            });
        } catch (error) {
            console.warn('Error tracking ad click:', error);
        }
    };

    return {
        trackAdImpression,
        trackAdClick,
    };
}

/**
 * Utility function to detect device type
 */
function getDeviceType(): 'desktop' | 'tablet' | 'mobile' {
    if (typeof window === 'undefined') return 'desktop';

    const width = window.innerWidth;
    
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
}

/**
 * Hook to manage ad loading and error states
 */
export function useAdState() {
    const { shouldShowAds } = useAds();

    const handleAdLoad = (zone: string, page: string) => {
        if (!shouldShowAds) return;

        console.log(`Ad loaded in zone: ${zone} on page: ${page}`);
    };

    const handleAdError = (zone: string, page: string, error: any) => {
        console.warn(`Ad error in zone: ${zone} on page: ${page}`, error);
    };

    const handleAdBlock = (zone: string, page: string) => {
        console.log(`Ad blocked in zone: ${zone} on page: ${page}`);
    };

    return {
        handleAdLoad,
        handleAdError,
        handleAdBlock,
    };
}
