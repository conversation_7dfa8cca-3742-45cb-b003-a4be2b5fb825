import { AdConfigScript, AdScript } from '@/components/ads';
import AnalyticsProvider, { usePageTracking, useUserTracking } from '@/components/analytics/AnalyticsProvider';
import { MetaPixelProvider } from '@/components/analytics/MetaPixelProvider';
import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppSidebarHeader } from '@/components/app-sidebar-header';
import { GlobalSearchCommand } from '@/components/global-search-command';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import { MobileQuickActions } from '@/components/mobile-quick-actions';
import { useAdmin } from '@/hooks/use-admin';
import { useAds } from '@/hooks/use-ads';
import { type BreadcrumbItem } from '@/types';
import { type PropsWithChildren } from 'react';

function AppSidebarLayoutContent({ children, breadcrumbs = [] }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    const isAdmin = useAdmin();
    const { shouldShowAds } = useAds();

    // Initialize analytics tracking
    usePageTracking();
    useUserTracking();

    return (
        <>
            {/* AdSense Scripts - Load only if ads should be shown */}
            <AdScript
                enabled={shouldShowAds}
                clientId={import.meta.env.VITE_ADSENSE_CLIENT_ID}
            />
            <AdConfigScript
                enabled={shouldShowAds}
                clientId={import.meta.env.VITE_ADSENSE_CLIENT_ID}
            />

            <ImpersonationBanner />
            <AppShell variant="sidebar">
                <AppSidebar />
                <AppContent variant="sidebar" className="overflow-x-hidden">
                    <AppSidebarHeader breadcrumbs={breadcrumbs} />
                    {children}
                </AppContent>
            </AppShell>
            <GlobalSearchCommand isAdmin={isAdmin} />
            <MobileQuickActions />
        </>
    );
}

export default function AppSidebarLayout({ children, breadcrumbs = [] }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    return (
        <AnalyticsProvider>
            <MetaPixelProvider>
                <AppSidebarLayoutContent breadcrumbs={breadcrumbs}>
                    {children}
                </AppSidebarLayoutContent>
            </MetaPixelProvider>
        </AnalyticsProvider>
    );
}
