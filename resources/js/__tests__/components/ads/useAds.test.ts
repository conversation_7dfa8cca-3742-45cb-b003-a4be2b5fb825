/**
 * @jest-environment jsdom
 */

import { renderHook } from '@testing-library/react';
import { useAds } from '@/hooks/use-ads';

// Mock the usePage hook from Inertia
jest.mock('@inertiajs/react', () => ({
    usePage: jest.fn(),
}));

const mockUsePage = require('@inertiajs/react').usePage;

describe('useAds hook', () => {
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        
        // Mock window.location.pathname
        Object.defineProperty(window, 'location', {
            value: {
                pathname: '/test-page',
            },
            writable: true,
        });
    });

    it('should return shouldShowAds as false for admin users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        isAdmin: true,
                        isPremium: false,
                    },
                },
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.shouldShowAds).toBe(false);
        expect(result.current.userType).toBe('admin');
    });

    it('should return shouldShowAds as false for premium users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        isAdmin: false,
                        isPremium: true,
                    },
                },
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.shouldShowAds).toBe(false);
        expect(result.current.userType).toBe('premium');
    });

    it('should return shouldShowAds as true for free users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        isAdmin: false,
                        isPremium: false,
                    },
                },
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.shouldShowAds).toBe(true);
        expect(result.current.userType).toBe('free');
    });

    it('should return shouldShowAds as true for guest users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: null,
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.shouldShowAds).toBe(true);
        expect(result.current.userType).toBe('guest');
    });

    it('should return correct ad configuration with default values', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        isAdmin: false,
                        isPremium: false,
                    },
                },
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.adConfig).toEqual({
            enabled: true,
            zones: {
                header: true,
                sidebar: true,
                content: true,
                footer: true,
                mobile: true,
            },
            frequency: {
                maxAdsPerPage: 4,
                delaySeconds: 3,
                gracePeriodMinutes: 0,
            },
            targeting: {
                page: '/test-page',
                userType: 'free',
                deviceType: expect.any(String),
            },
        });
    });

    it('should merge server-provided ad settings with defaults', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        isAdmin: false,
                        isPremium: false,
                    },
                },
                adSettings: {
                    zones: {
                        header: false,
                        sidebar: true,
                    },
                    frequency: {
                        maxAdsPerPage: 2,
                    },
                },
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.adConfig.zones.header).toBe(false);
        expect(result.current.adConfig.zones.sidebar).toBe(true);
        expect(result.current.adConfig.zones.content).toBe(true); // Default value
        expect(result.current.adConfig.frequency.maxAdsPerPage).toBe(2);
        expect(result.current.adConfig.frequency.delaySeconds).toBe(3); // Default value
    });

    it('should return false for canShowZone when ads are disabled', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        isAdmin: true,
                        isPremium: false,
                    },
                },
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.canShowZone('header')).toBe(false);
        expect(result.current.canShowZone('sidebar')).toBe(false);
    });

    it('should return correct value for canShowZone when ads are enabled', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        isAdmin: false,
                        isPremium: false,
                    },
                },
                adSettings: {
                    zones: {
                        header: true,
                        sidebar: false,
                    },
                },
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.canShowZone('header')).toBe(true);
        expect(result.current.canShowZone('sidebar')).toBe(false);
        expect(result.current.canShowZone('content')).toBe(true); // Default enabled
    });

    it('should handle missing auth gracefully', () => {
        mockUsePage.mockReturnValue({
            props: {},
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.shouldShowAds).toBe(true);
        expect(result.current.userType).toBe('guest');
    });

    it('should handle missing user in auth gracefully', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {},
            },
        });

        const { result } = renderHook(() => useAds());

        expect(result.current.shouldShowAds).toBe(true);
        expect(result.current.userType).toBe('guest');
    });
});

// Mock device type detection
function getDeviceType(): 'desktop' | 'tablet' | 'mobile' {
    if (typeof window === 'undefined') return 'desktop';

    const width = window.innerWidth;
    
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
}

describe('Device type detection', () => {
    beforeEach(() => {
        // Reset window dimensions
        Object.defineProperty(window, 'innerWidth', {
            writable: true,
            configurable: true,
            value: 1024,
        });
    });

    it('should detect desktop correctly', () => {
        window.innerWidth = 1200;
        expect(getDeviceType()).toBe('desktop');
    });

    it('should detect tablet correctly', () => {
        window.innerWidth = 800;
        expect(getDeviceType()).toBe('tablet');
    });

    it('should detect mobile correctly', () => {
        window.innerWidth = 600;
        expect(getDeviceType()).toBe('mobile');
    });

    it('should handle edge cases', () => {
        window.innerWidth = 768;
        expect(getDeviceType()).toBe('tablet');

        window.innerWidth = 1024;
        expect(getDeviceType()).toBe('desktop');
    });
});
