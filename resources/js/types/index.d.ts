import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User | null;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

// Core Entity Interfaces
export interface Brand {
    id: number;
    name: string;
    slug?: string;
    description?: string;
    logo_url: string | null;
    country?: string | null;
    website?: string | null;
    is_active?: boolean;
    created_at?: string;
    updated_at?: string;
    models_count?: number;
}

export interface Category {
    id: number;
    name: string;
    slug?: string;
    description?: string | null;
    parent_id?: number | null;
    is_active?: boolean;
    created_at?: string;
    updated_at?: string;
}

// Pivot table data for model-part relationships
export interface ModelPartPivot {
    compatibility_notes: string | null;
    is_verified: boolean;
    display_type?: string | null;
    display_size?: string | null;
    location?: string | null;
}

// Mobile Model interface - used in compatibility contexts
export interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number: string | null;
    release_year: number | null;
    brand_id?: number;
    brand: Brand;
    specifications?: Record<string, string | number | boolean | null> | null;
    images?: string[] | null;
    is_active?: boolean;
    created_at?: string;
    updated_at?: string;
    parts_count?: number;
    pivot?: ModelPartPivot;
    is_blurred?: boolean; // For copy protection
}

// Part interface - comprehensive definition
export interface Part {
    id: number;
    category_id?: number;
    name: string;
    slug?: string;
    part_number: string | null;
    manufacturer: string | null;
    description: string | null;
    specifications?: Record<string, string | number | boolean | null> | null;
    images?: string[] | null;
    is_active?: boolean;
    sales_button_name?: string | null;
    sales_button_url?: string | null;
    created_at?: string;
    updated_at?: string;
    category: Category;
    models?: MobileModel[];
    models_count?: number;
    is_favorited?: boolean; // For user favorites
}

// Column configuration for dynamic tables
export interface ColumnConfig {
    enabled: boolean;
    required: boolean;
    order: number;
    label: string;
    source: string;
    priority: number;
    minBreakpoint: string;
    width?: string;
    minWidth?: string;
    maxWidth?: string;
    truncate?: boolean;
}

export interface CompatibilityColumns {
    [key: string]: ColumnConfig;
}

// Pagination interfaces
export interface PaginatedResponse<T> {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

// Search and filter interfaces
export interface SearchFilters {
    categories: Category[];
    brands: Brand[];
    manufacturers: string[];
    release_years: number[];
}

export interface AppliedFilters {
    category_id?: string;
    brand_id?: string;
    manufacturer?: string;
    release_year?: string;
    [key: string]: string | boolean | null | undefined;
}

// Guest search configuration
export interface GuestSearchConfig {
    enable_partial_results: boolean;
    max_visible_results: number;
    blur_intensity: string;
    show_signup_cta: boolean;
}

// Copy protection configuration
export interface CopyProtectionConfig {
    enabled: boolean;
    blur_intensity: string;
    max_visible_results: number;
    show_signup_cta: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    categories?: Category[];
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    subscription_plan?: string;
    subscription_status?: string;
    subscription_ends_at?: string | null;
    role?: 'user' | 'content_manager' | 'admin';
    status?: 'active' | 'pending' | 'suspended' | 'banned';
    approval_status?: 'pending' | 'approved' | 'rejected';
    isAdmin?: boolean;
    isContentManager?: boolean;
    canManageContent?: boolean;
    canAccessAdminDashboard?: boolean;
    isPremium?: boolean;
    remaining_searches?: number;
    [key: string]: unknown; // This allows for additional properties...
}

export interface PricingPlan {
    id: number;
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_active: boolean;
    is_default: boolean;
    is_popular: boolean;
    sort_order: number;
    formatted_price: string;
    metadata?: Record<string, unknown>;

    // Payment method configurations
    online_payment_enabled: boolean;
    offline_payment_enabled: boolean;
    crypto_payment_enabled: boolean;

    // Paddle integration
    paddle_price_id_monthly?: string;
    paddle_price_id_yearly?: string;
    paddle_product_id?: string;
    has_paddle_integration: boolean;

    // ShurjoPay integration
    shurjopay_price_id_monthly?: string;
    shurjopay_price_id_yearly?: string;
    shurjopay_product_id?: string;
    has_shurjopay_integration: boolean;

    // Coinbase Commerce integration
    coinbase_commerce_price_id_monthly?: string;
    coinbase_commerce_price_id_yearly?: string;
    coinbase_commerce_product_id?: string;
    has_coinbase_commerce_integration: boolean;

    // Billing cycle support
    supports_monthly: boolean;
    supports_yearly: boolean;
    supports_coinbase_commerce_monthly?: boolean;
    supports_coinbase_commerce_yearly?: boolean;

    // Payment method availability
    supports_online_payment: boolean;
    supports_crypto_payment: boolean;
    has_online_payment_enabled: boolean;
    has_offline_payment_enabled: boolean;
    has_crypto_payment_enabled: boolean;
    has_any_payment_method: boolean;

    // Fee configuration
    paddle_fee_percentage?: number;
    paddle_fee_fixed?: number;
    shurjopay_fee_percentage?: number;
    shurjopay_fee_fixed?: number;
    coinbase_commerce_fee_percentage?: number;
    coinbase_commerce_fee_fixed?: number;
    offline_fee_percentage?: number;
    offline_fee_fixed?: number;
    fee_handling?: string;
    show_fees_breakdown?: boolean;
    tax_percentage?: number;
    tax_inclusive?: boolean;
    has_fees_configured?: boolean;

    // Timestamps
    created_at?: string;
    updated_at?: string;
    subscriptions_count: number;
}

export interface UserSearch {
    id: number;
    user_id: number;
    search_query: string;
    search_type: string;
    results_count: number;
    created_at: string;
    user?: User;
}

// Dashboard-specific interfaces
export interface DashboardStats {
    total_searches: number;
    searches_today: number;
    searches_this_week: number;
    week_growth_percentage: number;
    success_rate: number;
    favorite_items: number;
    remaining_searches: number;
    is_premium: boolean;
    subscription_plan: string;
}

export interface RecentSearch {
    id: number;
    query: string;
    type: string;
    results: number;
    date: string;
    created_at: string;
}

export interface TopCategory {
    name: string;
    count: number;
    percentage: number;
}

export interface SubscriptionInfo {
    plan: string;
    status: string;
    is_premium: boolean;
    ends_at: string | null;
    active_subscription: {
        plan_name: string;
        current_period_end: string;
        status: string;
    } | null;
}

export interface SearchAnalytics {
    daily_activity: Array<{
        date: string;
        searches: number;
        avg_results: number;
        success_rate: number;
    }>;
}

export interface DashboardData {
    stats: DashboardStats;
    recent_searches: RecentSearch[];
    top_categories: TopCategory[];
    notifications_count: number;
    subscription_info: SubscriptionInfo;
    search_analytics: SearchAnalytics;
}
