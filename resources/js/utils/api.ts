/**
 * API Utility for handling authenticated requests
 * Provides centralized authentication, CSRF token handling, and error management
 */

interface ApiRequestOptions extends RequestInit {
    requireAuth?: boolean;
    skipCsrf?: boolean;
}

interface ApiResponse<T = any> {
    data?: T;
    error?: string;
    status: number;
    ok: boolean;
}

/**
 * Get CSRF token from meta tag or cookie
 */
function getCsrfToken(): string | null {
    // First try to get from meta tag (preferred for Inertia apps)
    const metaTag = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
    if (metaTag?.content) {
        return metaTag.content;
    }

    // Fallback to XSRF-TOKEN cookie
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'XSRF-TOKEN') {
            return decodeURIComponent(value);
        }
    }

    return null;
}

/**
 * Check if user is authenticated by checking auth data from Inertia
 */
function isAuthenticated(): boolean {
    try {
        // Check if we have Inertia page data with auth info
        const inertiaData = (window as any).page?.props?.auth;
        return !!(inertiaData?.user?.id);
    } catch {
        return false;
    }
}

/**
 * Enhanced fetch wrapper with authentication and CSRF handling
 */
export async function apiRequest<T = any>(
    url: string, 
    options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> {
    const {
        requireAuth = true,
        skipCsrf = false,
        headers = {},
        ...fetchOptions
    } = options;

    // Check authentication if required
    if (requireAuth && !isAuthenticated()) {
        return {
            data: undefined,
            error: 'User not authenticated',
            status: 401,
            ok: false
        };
    }

    // Prepare headers
    const requestHeaders: Record<string, string> = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        ...(headers as Record<string, string>)
    };

    // Add CSRF token for state-changing requests
    if (!skipCsrf && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(fetchOptions.method?.toUpperCase() || 'GET')) {
        const csrfToken = getCsrfToken();
        if (csrfToken) {
            requestHeaders['X-CSRF-TOKEN'] = csrfToken;
        }
    }

    try {
        const response = await fetch(url, {
            credentials: 'same-origin', // Include cookies for session auth
            ...fetchOptions,
            headers: requestHeaders
        });

        let data: T | undefined;
        const contentType = response.headers.get('content-type');
        
        if (contentType?.includes('application/json')) {
            try {
                data = await response.json();
            } catch {
                // Response might not be valid JSON
                data = undefined;
            }
        }

        return {
            data,
            error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
            status: response.status,
            ok: response.ok
        };
    } catch (error) {
        return {
            data: undefined,
            error: error instanceof Error ? error.message : 'Network error',
            status: 0,
            ok: false
        };
    }
}

/**
 * Convenience methods for different HTTP verbs
 */
export const api = {
    get: <T = any>(url: string, options?: Omit<ApiRequestOptions, 'method'>) =>
        apiRequest<T>(url, { ...options, method: 'GET' }),
    
    post: <T = any>(url: string, data?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>) =>
        apiRequest<T>(url, { 
            ...options, 
            method: 'POST', 
            body: data ? JSON.stringify(data) : undefined 
        }),
    
    put: <T = any>(url: string, data?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>) =>
        apiRequest<T>(url, { 
            ...options, 
            method: 'PUT', 
            body: data ? JSON.stringify(data) : undefined 
        }),
    
    patch: <T = any>(url: string, data?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>) =>
        apiRequest<T>(url, { 
            ...options, 
            method: 'PATCH', 
            body: data ? JSON.stringify(data) : undefined 
        }),
    
    delete: <T = any>(url: string, options?: Omit<ApiRequestOptions, 'method'>) =>
        apiRequest<T>(url, { ...options, method: 'DELETE' })
};

/**
 * Helper to handle API errors consistently
 */
export function handleApiError(error: string | undefined, fallbackMessage = 'An error occurred'): void {
    if (error) {
        console.error('API Error:', error);
        // You can extend this to show toast notifications, etc.
    }
}

/**
 * Type guard to check if response has data
 */
export function hasApiData<T>(response: ApiResponse<T>): response is ApiResponse<T> & { data: T } {
    return response.ok && response.data !== undefined;
}
