<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\ShurjoPayLogger;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;

class ShurjoPayLoggerTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any cached log entries
        ShurjoPayLogger::clearLogCache();
    }

    /** @test */
    public function logging_is_disabled_when_shurjopay_logging_enabled_is_false()
    {
        Config::set('shurjopay.logging.enabled', false);
        
        $this->assertFalse(ShurjoPayLogger::isLoggingEnabled());
    }

    /** @test */
    public function logging_is_enabled_when_shurjopay_logging_enabled_is_true()
    {
        Config::set('shurjopay.logging.enabled', true);
        
        $this->assertTrue(ShurjoPayLogger::isLoggingEnabled());
    }

    /** @test */
    public function debug_is_disabled_when_either_shurjopay_debug_or_app_debug_is_false()
    {
        Config::set('shurjopay.debug', false);
        Config::set('app.debug', true);
        
        $this->assertFalse(ShurjoPayLogger::isDebugEnabled());
        
        Config::set('shurjopay.debug', true);
        Config::set('app.debug', false);
        
        $this->assertFalse(ShurjoPayLogger::isDebugEnabled());
    }

    /** @test */
    public function debug_is_enabled_when_both_shurjopay_debug_and_app_debug_are_true()
    {
        Config::set('shurjopay.debug', true);
        Config::set('app.debug', true);
        
        $this->assertTrue(ShurjoPayLogger::isDebugEnabled());
    }

    /** @test */
    public function info_logging_does_not_occur_when_logging_is_disabled()
    {
        Config::set('shurjopay.logging.enabled', false);
        
        Log::shouldReceive('channel')->never();
        Log::shouldReceive('info')->never();
        
        ShurjoPayLogger::info('Test message');
    }

    /** @test */
    public function debug_logging_does_not_occur_when_logging_or_debug_is_disabled()
    {
        Config::set('shurjopay.logging.enabled', true);
        Config::set('shurjopay.debug', false);
        Config::set('app.debug', true);
        
        Log::shouldReceive('channel')->never();
        Log::shouldReceive('debug')->never();
        
        ShurjoPayLogger::debug('Test message');
    }

    /** @test */
    public function error_logging_always_occurs_regardless_of_logging_setting()
    {
        Config::set('shurjopay.logging.enabled', false);
        
        Log::shouldReceive('error')
            ->once()
            ->with('Test error', ['source' => 'shurjopay']);
        
        ShurjoPayLogger::error('Test error');
    }

    /** @test */
    public function log_once_prevents_duplicate_logging()
    {
        Config::set('shurjopay.logging.enabled', true);
        
        Log::shouldReceive('channel')
            ->with('shurjopay')
            ->once()
            ->andReturnSelf();
        Log::shouldReceive('info')
            ->once()
            ->with('Test message', []);
        
        // First call should log
        ShurjoPayLogger::logOnce('info', 'Test message', [], 1);
        
        // Second call should not log (cached)
        ShurjoPayLogger::logOnce('info', 'Test message', [], 1);
    }

    /** @test */
    public function configuration_handles_string_boolean_values_correctly()
    {
        // Test that string "false" is properly converted to boolean false
        Config::set('shurjopay.logging.enabled', 'false');
        $this->assertFalse(filter_var(config('shurjopay.logging.enabled'), FILTER_VALIDATE_BOOLEAN));
        
        // Test that string "true" is properly converted to boolean true
        Config::set('shurjopay.logging.enabled', 'true');
        $this->assertTrue(filter_var(config('shurjopay.logging.enabled'), FILTER_VALIDATE_BOOLEAN));
        
        // Test that typo "flase" is properly converted to boolean false
        Config::set('shurjopay.logging.enabled', 'flase');
        $this->assertFalse(filter_var(config('shurjopay.logging.enabled'), FILTER_VALIDATE_BOOLEAN));
    }
}
