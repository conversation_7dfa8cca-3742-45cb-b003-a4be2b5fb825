<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\LocalizedPricingService;
use App\Models\PricingPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;

class LocalizedPricingServiceTest extends TestCase
{
    use RefreshDatabase;

    private LocalizedPricingService $localizedPricingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->localizedPricingService = app(LocalizedPricingService::class);

        // Create test pricing plans
        $this->createTestPlans();
    }

    private function createTestPlans(): void
    {
        // Create USD plan
        PricingPlan::create([
            'name' => 'test_plan',
            'display_name' => 'Test Plan',
            'description' => 'Test plan description',
            'price' => 19.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Feature 1', 'Feature 2'],
            'search_limit' => 100,
            'model_view_limit' => 50,
            'parts_per_model_limit' => 10,
            'is_active' => true,
            'is_public' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
        ]);

        // Create BDT plan
        PricingPlan::create([
            'name' => 'test_plan_bd',
            'display_name' => 'Test Plan (Bangladesh)',
            'description' => 'Test plan description - Localized for Bangladesh',
            'price' => 2199,
            'currency' => 'BDT',
            'interval' => 'month',
            'features' => ['Feature 1', 'Feature 2'],
            'search_limit' => 100,
            'model_view_limit' => 50,
            'parts_per_model_limit' => 10,
            'is_active' => true,
            'is_public' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'metadata' => [
                'localized_for' => 'BD',
                'base_plan_id' => 1,
                'conversion_rate' => 110,
            ],
        ]);
    }

    public function test_gets_localized_plans_for_bangladesh(): void
    {
        $plans = $this->localizedPricingService->getLocalizedPlansForCountry('BD');

        $this->assertNotEmpty($plans);

        // Should return BDT plans for Bangladesh
        foreach ($plans as $plan) {
            $this->assertEquals('BDT', $plan->currency);
        }
    }

    public function test_gets_localized_plans_for_other_countries(): void
    {
        $plans = $this->localizedPricingService->getLocalizedPlansForCountry('US');

        $this->assertNotEmpty($plans);

        // Should return USD plans for other countries
        foreach ($plans as $plan) {
            $this->assertEquals('USD', $plan->currency);
        }
    }

    public function test_gets_localized_plan_by_name(): void
    {
        // Test Bangladesh - should get BDT version
        $plan = $this->localizedPricingService->getLocalizedPlan('test_plan', 'BD');
        $this->assertNotNull($plan);
        $this->assertEquals('test_plan_bd', $plan->name);
        $this->assertEquals('BDT', $plan->currency);

        // Test US - should get USD version
        $plan = $this->localizedPricingService->getLocalizedPlan('test_plan', 'US');
        $this->assertNotNull($plan);
        $this->assertEquals('test_plan', $plan->name);
        $this->assertEquals('USD', $plan->currency);
    }

    public function test_formats_plan_for_country(): void
    {
        $plan = PricingPlan::where('name', 'test_plan_bd')->first();
        $formatted = $this->localizedPricingService->formatPlanForCountry($plan, 'BD');

        $this->assertArrayHasKey('formatted_price', $formatted);
        $this->assertArrayHasKey('currency_symbol', $formatted);
        $this->assertArrayHasKey('localized_for_country', $formatted);
        $this->assertArrayHasKey('is_localized', $formatted);

        $this->assertEquals('BD', $formatted['localized_for_country']);
        $this->assertTrue($formatted['is_localized']);
        $this->assertEquals('৳', $formatted['currency_symbol']);
    }

    public function test_gets_plan_recommendations_for_country(): void
    {
        $recommendations = $this->localizedPricingService->getPlanRecommendationsForCountry('BD');

        $this->assertArrayHasKey('currency', $recommendations);
        $this->assertArrayHasKey('country_code', $recommendations);
        $this->assertArrayHasKey('total_plans', $recommendations);

        $this->assertEquals('BDT', $recommendations['currency']);
        $this->assertEquals('BD', $recommendations['country_code']);
        $this->assertIsInt($recommendations['total_plans']);
    }

    public function test_gets_pricing_statistics(): void
    {
        $stats = $this->localizedPricingService->getPricingStatistics();

        $this->assertArrayHasKey('total_plans', $stats);
        $this->assertArrayHasKey('bdt_plans', $stats);
        $this->assertArrayHasKey('usd_plans', $stats);
        $this->assertArrayHasKey('active_plans', $stats);
        $this->assertArrayHasKey('public_plans', $stats);
        $this->assertArrayHasKey('localization_coverage', $stats);

        $this->assertIsInt($stats['total_plans']);
        $this->assertIsInt($stats['bdt_plans']);
        $this->assertIsInt($stats['usd_plans']);
        $this->assertIsArray($stats['localization_coverage']);
    }
}
