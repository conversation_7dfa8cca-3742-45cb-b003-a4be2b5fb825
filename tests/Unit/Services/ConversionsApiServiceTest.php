<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ConversionsApiService;
use App\Models\MetaPixelConfig;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ConversionsApiServiceTest extends TestCase
{
    use RefreshDatabase;

    private ConversionsApiService $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up HTTP fake for all tests
        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $this->service = app(ConversionsApiService::class);
    }

    public function test_send_event_returns_false_when_not_enabled()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_id',
            'conversions_api_enabled' => false,
        ]);

        $eventData = [
            'event_name' => 'PageView',
            'event_time' => time(),
            'action_source' => 'website',
        ];

        $result = $this->service->sendEvent($eventData);

        $this->assertFalse($result['success']);
    }

    public function test_send_event_returns_false_when_no_config()
    {
        $eventData = [
            'event_name' => 'PageView',
            'event_time' => time(),
            'action_source' => 'website',
        ];

        $result = $this->service->sendEvent($eventData);

        $this->assertFalse($result['success']);
    }

    public function test_send_event_makes_http_request_when_enabled()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        // Clear cache and create service after config is created
        MetaPixelConfig::clearCache();
        $service = app(ConversionsApiService::class);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $eventData = [
            'event_name' => 'PageView',
            'event_time' => time(),
            'action_source' => 'website',
        ];

        $result = $service->sendEvent($eventData);

        $this->assertTrue($result['success']);

        Http::assertSent(function ($request) {
            return $request->url() === 'https://graph.facebook.com/v21.0/test_pixel_123/events' &&
                   $request->hasHeader('Authorization', 'Bearer test_access_token');
        });
    }

    public function test_send_event_handles_api_error_gracefully()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['error' => 'Invalid token'], 400)
        ]);

        Log::shouldReceive('error')->once();

        $eventData = [
            'event_name' => 'PageView',
            'event_time' => time(),
            'action_source' => 'website',
        ];

        $result = $this->service->sendEvent($eventData);

        $this->assertFalse($result['success']);
    }

    public function test_send_event_includes_required_fields()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        // Clear cache to ensure fresh config is loaded
        Cache::forget('meta_pixel_config');

        // Create a fresh service instance to ensure it uses the new config
        $service = new ConversionsApiService();

        $eventData = [
            'event_name' => 'PageView',
            'event_time' => 1234567890,
            'action_source' => 'website',
        ];

        $result = $service->sendEvent($eventData);

        // Verify the service call was successful
        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['events_received']);
    }

    public function test_send_purchase_event_includes_purchase_data()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        // Clear cache to ensure fresh config is loaded
        Cache::forget('meta_pixel_config');

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        // Create a fresh service instance to ensure it uses the new config
        $service = new ConversionsApiService();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'John Doe',
        ]);

        $result = $service->trackPurchase(
            value: 99.99,
            currency: 'USD',
            contentIds: ['product_123'],
            user: $user
        );

        // Verify the service call was successful
        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['events_received']);
    }

    public function test_send_lead_event_includes_lead_data()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $result = $this->service->trackLead(
            value: 50.00,
            currency: 'EUR'
        );

        $this->assertTrue($result['success']);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $event = $data['data'][0];

            return $event['event_name'] === 'Lead' &&
                   $event['custom_data']['value'] === 50.00 &&
                   $event['custom_data']['currency'] === 'EUR';
        });
    }

    public function test_send_search_event_includes_search_data()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $result = $this->service->trackSearch(
            searchString: 'mobile phone',
            contentCategory: 'electronics'
        );

        $this->assertTrue($result['success']);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $event = $data['data'][0];

            return $event['event_name'] === 'Search' &&
                   $event['custom_data']['search_string'] === 'mobile phone' &&
                   $event['custom_data']['content_category'] === 'electronics';
        });
    }

    public function test_test_connection_returns_success_when_api_responds()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $result = $this->service->testConnection();

        $this->assertTrue($result['success']);
        $this->assertStringContainsString('Connection successful', $result['message']);
    }



    public function test_test_connection_returns_failure_when_not_configured()
    {
        $result = $this->service->testConnection();

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('not configured', $result['error']);
    }
}
