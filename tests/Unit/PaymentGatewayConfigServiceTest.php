<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\PaymentGatewayConfigService;
use App\Models\PaymentGatewayConfig;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PaymentGatewayConfigServiceTest extends TestCase
{
    use RefreshDatabase;

    private PaymentGatewayConfigService $gatewayConfigService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->gatewayConfigService = app(PaymentGatewayConfigService::class);

        // Seed default gateway configurations
        PaymentGatewayConfig::seedDefaults();
    }

    public function test_gets_available_gateways_for_bangladesh(): void
    {
        $result = $this->gatewayConfigService->getAvailableGatewaysForCountry('BD');

        $this->assertEquals('BD', $result['country_code']);
        $this->assertEquals('BDT', $result['currency']);
        $this->assertNotNull($result['primary_gateway']);
        $this->assertEquals('shurjopay', $result['primary_gateway']['gateway_name']);

        // Should have SurjoPay and offline payment available
        $gatewayNames = collect($result['available_gateways'])->pluck('gateway_name')->toArray();
        $this->assertContains('shurjopay', $gatewayNames);
        $this->assertContains('offline', $gatewayNames);
    }

    public function test_gets_available_gateways_for_other_countries(): void
    {
        $result = $this->gatewayConfigService->getAvailableGatewaysForCountry('US');

        $this->assertEquals('US', $result['country_code']);
        $this->assertEquals('USD', $result['currency']);
        $this->assertNotNull($result['primary_gateway']);
        $this->assertEquals('paddle', $result['primary_gateway']['gateway_name']);

        // Should have Paddle, Coinbase Commerce, and offline payment available
        $gatewayNames = collect($result['available_gateways'])->pluck('gateway_name')->toArray();
        $this->assertContains('paddle', $gatewayNames);
        $this->assertContains('coinbase_commerce', $gatewayNames);
        $this->assertContains('offline', $gatewayNames);
    }

    public function test_enables_gateway(): void
    {
        // Disable a gateway first
        $gateway = PaymentGatewayConfig::where('gateway_name', 'paddle')->first();
        $gateway->update(['is_enabled' => false]);

        // Enable it
        $result = $this->gatewayConfigService->enableGateway('paddle');

        $this->assertTrue($result);
        $this->assertTrue($gateway->fresh()->is_enabled);
    }

    public function test_disables_gateway(): void
    {
        // Ensure gateway is enabled first
        $gateway = PaymentGatewayConfig::where('gateway_name', 'paddle')->first();
        $gateway->update(['is_enabled' => true]);

        // Disable it
        $result = $this->gatewayConfigService->disableGateway('paddle');

        $this->assertTrue($result);
        $this->assertFalse($gateway->fresh()->is_enabled);
    }

    public function test_validates_gateway_config(): void
    {
        $validConfig = [
            'display_name' => 'Test Gateway',
            'is_enabled' => true,
            'supported_currencies' => ['USD'],
            'supported_countries' => ['US'],
        ];

        $errors = $this->gatewayConfigService->validateGatewayConfig('paddle', $validConfig);
        $this->assertEmpty($errors);

        $invalidConfig = [
            'display_name' => '', // Empty display name
            'is_enabled' => 'not_boolean', // Invalid boolean
        ];

        $errors = $this->gatewayConfigService->validateGatewayConfig('paddle', $invalidConfig);
        $this->assertNotEmpty($errors);
    }

    public function test_gets_gateway_statistics(): void
    {
        $stats = $this->gatewayConfigService->getGatewayStatistics();

        $this->assertArrayHasKey('total_gateways', $stats);
        $this->assertArrayHasKey('enabled_gateways', $stats);
        $this->assertArrayHasKey('configured_gateways', $stats);
        $this->assertArrayHasKey('disabled_gateways', $stats);
        $this->assertArrayHasKey('configuration_completion', $stats);

        $this->assertIsInt($stats['total_gateways']);
        $this->assertIsInt($stats['enabled_gateways']);
        $this->assertIsNumeric($stats['configuration_completion']);
    }
}
