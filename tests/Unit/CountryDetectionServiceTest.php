<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\CountryDetectionService;
use App\Services\IpSearchTrackingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class CountryDetectionServiceTest extends TestCase
{
    private CountryDetectionService $countryDetectionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->countryDetectionService = app(CountryDetectionService::class);
    }

    public function test_detects_bangladesh_from_ip(): void
    {
        // Test with a known Bangladesh IP
        $result = $this->countryDetectionService->getCountryFromIp('*************');

        $this->assertEquals('BD', $result['country_code']);
        $this->assertEquals('Bangladesh', $result['country_name']);
        $this->assertEquals('BDT', $result['currency']);
        $this->assertTrue($result['is_bangladesh']);
    }

    public function test_detects_us_from_ip(): void
    {
        // Test with a known US IP (Google DNS)
        $result = $this->countryDetectionService->getCountryFromIp('*******');

        $this->assertEquals('US', $result['country_code']);
        $this->assertEquals('United States', $result['country_name']);
        $this->assertEquals('USD', $result['currency']);
        $this->assertFalse($result['is_bangladesh']);
    }

    public function test_returns_fallback_for_invalid_ip(): void
    {
        $result = $this->countryDetectionService->getCountryFromIp('invalid-ip');

        // The service might return different fallback data, so let's check the structure
        $this->assertArrayHasKey('country_code', $result);
        $this->assertArrayHasKey('currency', $result);
        $this->assertArrayHasKey('is_bangladesh', $result);
        $this->assertIsBool($result['is_bangladesh']);

        // For invalid IP, it should use fallback or testing IP
        $this->assertContains($result['country_code'], ['US', 'BD']); // Could be either depending on testing IP
    }

    public function test_caches_country_detection_results(): void
    {
        Cache::flush();

        $ip = '*******';

        // First call should cache the result
        $result1 = $this->countryDetectionService->getCountryFromIp($ip);

        // Second call should return cached result
        $result2 = $this->countryDetectionService->getCountryFromIp($ip);

        $this->assertEquals($result1, $result2);
    }

    public function test_gets_payment_gateways_for_bangladesh(): void
    {
        $gateways = $this->countryDetectionService->getPaymentGatewaysForCountry('BD');

        $this->assertEquals('shurjopay', $gateways['primary']);
        $this->assertEquals('BDT', $gateways['currency']);
        $this->assertContains('shurjopay', $gateways['available_gateways']);
        $this->assertContains('offline', $gateways['available_gateways']);
    }

    public function test_gets_payment_gateways_for_other_countries(): void
    {
        $gateways = $this->countryDetectionService->getPaymentGatewaysForCountry('US');

        $this->assertEquals('paddle', $gateways['primary']);
        $this->assertEquals('USD', $gateways['currency']);
        $this->assertContains('paddle', $gateways['available_gateways']);
        $this->assertContains('coinbase_commerce', $gateways['available_gateways']);
        $this->assertContains('offline', $gateways['available_gateways']);
    }

    public function test_should_use_localized_pricing_for_bangladesh(): void
    {
        $this->assertTrue($this->countryDetectionService->shouldUseLocalizedPricing('BD'));
        $this->assertFalse($this->countryDetectionService->shouldUseLocalizedPricing('US'));
    }

    public function test_gets_localized_currency(): void
    {
        $this->assertEquals('BDT', $this->countryDetectionService->getLocalizedCurrency('BD'));
        $this->assertEquals('USD', $this->countryDetectionService->getLocalizedCurrency('US'));
        $this->assertEquals('USD', $this->countryDetectionService->getLocalizedCurrency('CA'));
    }
}
