<?php

namespace Tests\Integration;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AuthenticationIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test complete authentication flow for API endpoints
     */
    public function test_complete_authentication_flow(): void
    {
        $user = User::factory()->create();

        // Test unauthenticated access
        $this->getJson('/api/impersonation/status')->assertStatus(401);
        $this->getJson('/api/notifications/unread-count')->assertStatus(401);
        $this->getJson('/api/notifications/recent')->assertStatus(401);

        // Test authenticated access
        $impersonationResponse = $this->actingAs($user)->getJson('/api/impersonation/status');
        $countResponse = $this->actingAs($user)->getJson('/api/notifications/unread-count');
        $recentResponse = $this->actingAs($user)->getJson('/api/notifications/recent');

        $impersonationResponse->assertStatus(200);
        $countResponse->assertStatus(200);
        $recentResponse->assertStatus(200);

        // Verify response structures
        $impersonationResponse->assertJsonStructure([
            'is_impersonating',
            'impersonating_user_id',
            'original_admin_id',
            'expires_at',
            'remaining_minutes'
        ]);

        $countResponse->assertJsonStructure(['count']);
        $recentResponse->assertJsonStructure(['notifications']);
    }

    /**
     * Test session persistence across multiple requests
     */
    public function test_session_persistence(): void
    {
        $user = User::factory()->create();

        // Authenticate user
        $this->actingAs($user);

        // Make multiple requests to ensure session persists
        for ($i = 0; $i < 3; $i++) {
            $response = $this->getJson('/api/impersonation/status');
            $response->assertStatus(200);
        }
    }

    /**
     * Test concurrent requests don't interfere with each other
     */
    public function test_concurrent_requests(): void
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Make requests as different users
        $response1 = $this->actingAs($user1)->getJson('/api/notifications/unread-count');
        $response2 = $this->actingAs($user2)->getJson('/api/notifications/unread-count');

        $response1->assertStatus(200);
        $response2->assertStatus(200);
    }

    /**
     * Test proper headers are handled correctly
     */
    public function test_proper_headers_handling(): void
    {
        $user = User::factory()->create();

        // Test with required headers
        $response = $this->actingAs($user)
            ->withHeaders([
                'Accept' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest',
                'Content-Type' => 'application/json'
            ])
            ->getJson('/api/impersonation/status');

        $response->assertStatus(200);

        // Test without proper headers (should still work for GET requests)
        $response2 = $this->actingAs($user)->getJson('/api/impersonation/status');
        $response2->assertStatus(200);
    }

    /**
     * Test error handling and recovery
     */
    public function test_error_handling_and_recovery(): void
    {
        $user = User::factory()->create();

        // Test with invalid endpoint
        $response = $this->actingAs($user)->getJson('/api/invalid-endpoint');
        $response->assertStatus(404);

        // Test that valid endpoints still work after error
        $validResponse = $this->actingAs($user)->getJson('/api/impersonation/status');
        $validResponse->assertStatus(200);
    }

    /**
     * Test rate limiting doesn't affect normal usage
     */
    public function test_normal_usage_not_rate_limited(): void
    {
        $user = User::factory()->create();

        // Make several requests in quick succession
        for ($i = 0; $i < 10; $i++) {
            $response = $this->actingAs($user)->getJson('/api/impersonation/status');
            $response->assertStatus(200);
        }
    }

    /**
     * Test API responses have correct content types
     */
    public function test_api_responses_content_types(): void
    {
        $user = User::factory()->create();

        $impersonationResponse = $this->actingAs($user)->getJson('/api/impersonation/status');
        $notificationResponse = $this->actingAs($user)->getJson('/api/notifications/unread-count');

        $impersonationResponse->assertHeader('Content-Type', 'application/json');
        $notificationResponse->assertHeader('Content-Type', 'application/json');
    }

    /**
     * Test middleware chain works correctly
     */
    public function test_middleware_chain(): void
    {
        $user = User::factory()->create();

        // Test that auth middleware is applied
        $this->getJson('/api/impersonation/status')->assertStatus(401);

        // Test that authenticated requests pass through all middleware
        $response = $this->actingAs($user)
            ->withHeaders([
                'Accept' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest'
            ])
            ->getJson('/api/impersonation/status');

        $response->assertStatus(200);
    }
}
