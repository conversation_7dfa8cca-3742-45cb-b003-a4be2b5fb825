import AdSenseConfigTab from '@/pages/admin/AdManagement/adsense';
import { useForm } from '@inertiajs/react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock dependencies
vi.mock('@inertiajs/react', () => ({
    useForm: vi.fn(),
    route: vi.fn((name: string) => {
        const routes: Record<string, string> = {
            'admin.ads.adsense-config.update': '/admin.ads.adsense-config.update',
            'admin.ads.adsense-config.test': '/admin.ads.adsense-config.test',
        };
        return routes[name] || `/admin/ads/${name.split('.').pop()}`;
    }),
}));

vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

// Mock fetch for test configuration
const mockFetch = vi.fn();
Object.defineProperty(global, 'fetch', {
    value: mockFetch,
    writable: true,
});

// Mock document.querySelector for CSRF token
const originalQuerySelector = document.querySelector;

describe('AdSenseConfigTab', () => {
    const mockConfig = {
        enabled: true,
        client_id: 'ca-pub-1234567890123456',
        auto_ads: false,
        debug: false,
        zones: {
            header_enabled: true,
            sidebar_enabled: true,
            content_enabled: false,
            footer_enabled: true,
            mobile_enabled: true,
        },
        frequency: {
            max_ads_per_page: 4,
            delay_seconds: 3,
            grace_period_minutes: 0,
        },
    };

    const mockStatus = {
        configured: true,
        enabled: true,
        client_id_valid: true,
        auto_ads_enabled: false,
        debug_mode: false,
        zones_enabled: {
            header: true,
            sidebar: true,
            content: false,
            footer: true,
            mobile: true,
        },
        status: 'healthy' as const,
        last_updated: '2024-01-01T00:00:00Z',
    };

    const mockFormMethods = {
        data: mockConfig,
        setData: vi.fn(),
        post: vi.fn(),
        processing: false,
        errors: {},
    };

    beforeEach(() => {
        vi.clearAllMocks();
        (useForm as any).mockReturnValue(mockFormMethods);
        mockFetch.mockResolvedValue({
            json: () => Promise.resolve({ success: true, message: 'Test successful' }),
        });

        // Reset document.querySelector mock
        document.querySelector = vi.fn((selector: string) => {
            if (selector === 'meta[name="csrf-token"]') {
                return { getAttribute: vi.fn(() => 'mock-csrf-token') };
            }
            return originalQuerySelector.call(document, selector);
        });
    });

    it('renders all main sections correctly', () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        // Check if all main sections are present
        expect(screen.getByText('AdSense Status')).toBeInTheDocument();
        expect(screen.getByText('Global Settings')).toBeInTheDocument();
        expect(screen.getByText('Zone Configuration')).toBeInTheDocument();
        expect(screen.getByText('Frequency Settings')).toBeInTheDocument();
    });

    it('displays status information correctly', () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        expect(screen.getByText('Configuration is valid and healthy')).toBeInTheDocument();

        // Check for specific status values using getAllByText since there are multiple "Yes" texts
        const yesTexts = screen.getAllByText('Yes');
        expect(yesTexts).toHaveLength(3); // Configured, Enabled, Client ID Valid

        expect(screen.getByText('Disabled')).toBeInTheDocument(); // Auto Ads status
    });

    it('renders form controls with correct values', () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        // Check client ID input
        const clientIdInput = screen.getByDisplayValue('ca-pub-1234567890123456');
        expect(clientIdInput).toBeInTheDocument();

        // Check switches
        const enabledSwitch = screen.getByRole('switch', { name: /enable adsense/i });
        expect(enabledSwitch).toBeChecked();
    });

    it('positions action buttons correctly at the bottom', () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        const saveButton = screen.getByRole('button', { name: /save configuration/i });
        const testButton = screen.getByRole('button', { name: /test configuration/i });

        expect(saveButton).toBeInTheDocument();
        expect(testButton).toBeInTheDocument();

        // Check if buttons are in the action area (should have proper styling)
        const actionArea = saveButton.closest('div');
        expect(actionArea).toHaveClass('flex', 'items-center', 'justify-end', 'gap-3', 'pt-6', 'border-t');
    });

    it('handles form submission correctly', async () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        const saveButton = screen.getByRole('button', { name: /save configuration/i });
        fireEvent.click(saveButton);

        await waitFor(() => {
            expect(mockFormMethods.post).toHaveBeenCalledWith('/admin.ads.adsense-config.update', {
                onSuccess: expect.any(Function),
                onError: expect.any(Function),
            });
        });
    });

    it('handles test configuration correctly', async () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        const testButton = screen.getByRole('button', { name: /test configuration/i });

        // Verify button is not disabled initially
        expect(testButton).not.toBeDisabled();
        expect(testButton).toHaveTextContent('Test Configuration');

        // Click the button
        fireEvent.click(testButton);

        // Wait for the button text to change to "Testing..." indicating the async operation started
        await waitFor(() => {
            expect(screen.getByText('Testing...')).toBeInTheDocument();
        });

        // Wait for the operation to complete and button to return to normal state
        await waitFor(() => {
            expect(screen.getByText('Test Configuration')).toBeInTheDocument();
        }, { timeout: 3000 });

        // Verify button is enabled again after operation completes
        expect(testButton).not.toBeDisabled();
    });

    it('displays proper spacing between cards', () => {
        const { container } = render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        // Find the root container that has the space-y-6 class
        const rootContainer = container.querySelector('.space-y-6');
        expect(rootContainer).toBeInTheDocument();
        expect(rootContainer).toHaveClass('space-y-6');
    });

    it('shows loading states correctly', () => {
        const processingFormMethods = { ...mockFormMethods, processing: true };
        (useForm as any).mockReturnValue(processingFormMethods);

        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        expect(screen.getByText('Saving...')).toBeInTheDocument();
    });

    it('handles zone configuration switches', () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        const headerZoneSwitch = screen.getByRole('switch', { name: /header zone/i });
        fireEvent.click(headerZoneSwitch);

        expect(mockFormMethods.setData).toHaveBeenCalledWith('zones', {
            ...mockConfig.zones,
            header_enabled: !mockConfig.zones.header_enabled,
        });
    });

    it('handles frequency settings inputs', () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        const maxAdsInput = screen.getByLabelText(/max ads per page/i);
        fireEvent.change(maxAdsInput, { target: { value: '5' } });

        expect(mockFormMethods.setData).toHaveBeenCalledWith('frequency', {
            ...mockConfig.frequency,
            max_ads_per_page: 5,
        });
    });

    it('displays error status correctly', () => {
        const errorStatus = { ...mockStatus, status: 'error' as const };
        render(<AdSenseConfigTab config={mockConfig} status={errorStatus} />);

        expect(screen.getByText('Configuration has issues that need attention')).toBeInTheDocument();
    });

    it('shows validation errors', () => {
        const formWithErrors = {
            ...mockFormMethods,
            errors: { client_id: 'Invalid client ID format' },
        };
        (useForm as any).mockReturnValue(formWithErrors);

        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        expect(screen.getByText('Invalid client ID format')).toBeInTheDocument();
    });

    it('maintains responsive design classes', () => {
        render(<AdSenseConfigTab config={mockConfig} status={mockStatus} />);

        // Check for responsive grid classes using a simpler approach
        const allGrids = document.querySelectorAll('.grid');

        // Find status grid (should have lg:grid-cols-4)
        const statusGrid = Array.from(allGrids).find(grid =>
            grid.classList.contains('lg:grid-cols-4')
        );
        expect(statusGrid).toBeInTheDocument();
        expect(statusGrid).toHaveClass('grid', 'gap-4', 'md:grid-cols-2', 'lg:grid-cols-4');

        // Find zone grid (should have md:grid-cols-2 but not lg:grid-cols-4)
        const zoneGrid = Array.from(allGrids).find(grid =>
            grid.classList.contains('md:grid-cols-2') && !grid.classList.contains('lg:grid-cols-4')
        );
        expect(zoneGrid).toBeInTheDocument();
        expect(zoneGrid).toHaveClass('grid', 'gap-4', 'md:grid-cols-2');

        // Find frequency grid (should have md:grid-cols-3)
        const frequencyGrid = Array.from(allGrids).find(grid =>
            grid.classList.contains('md:grid-cols-3')
        );
        expect(frequencyGrid).toBeInTheDocument();
        expect(frequencyGrid).toHaveClass('grid', 'gap-4', 'md:grid-cols-3');
    });
});
