<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BulkModeSearchableSelectTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Brand $appleBrand;
    private Brand $samsungBrand;
    private Brand $xiaomiBrand;
    private MobileModel $iphone15;
    private MobileModel $iphone14;
    private MobileModel $galaxyS24;
    private MobileModel $galaxyS23;
    private MobileModel $redmiNote13;
    private Part $displayPart;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        // Create test brands
        $this->appleBrand = Brand::factory()->create([
            'name' => 'Apple',
            'logo_url' => 'https://example.com/apple-logo.png',
            'is_active' => true,
        ]);

        $this->samsungBrand = Brand::factory()->create([
            'name' => 'Samsung',
            'logo_url' => 'https://example.com/samsung-logo.png',
            'is_active' => true,
        ]);

        $this->xiaomiBrand = Brand::factory()->create([
            'name' => 'Xiaomi',
            'logo_url' => 'https://example.com/xiaomi-logo.png',
            'is_active' => true,
        ]);

        // Create test models
        $this->iphone15 = MobileModel::factory()->create([
            'name' => 'iPhone 15 Pro',
            'model_number' => 'A3108',
            'release_year' => 2023,
            'brand_id' => $this->appleBrand->id,
            'is_active' => true,
        ]);

        $this->iphone14 = MobileModel::factory()->create([
            'name' => 'iPhone 14 Pro',
            'model_number' => 'A2894',
            'release_year' => 2022,
            'brand_id' => $this->appleBrand->id,
            'is_active' => true,
        ]);

        $this->galaxyS24 = MobileModel::factory()->create([
            'name' => 'Galaxy S24',
            'model_number' => 'SM-S921B',
            'release_year' => 2024,
            'brand_id' => $this->samsungBrand->id,
            'is_active' => true,
        ]);

        $this->galaxyS23 = MobileModel::factory()->create([
            'name' => 'Galaxy S23',
            'model_number' => 'SM-S911B',
            'release_year' => 2023,
            'brand_id' => $this->samsungBrand->id,
            'is_active' => true,
        ]);

        $this->redmiNote13 = MobileModel::factory()->create([
            'name' => 'Redmi Note 13',
            'model_number' => '23124RA7EO',
            'release_year' => 2024,
            'brand_id' => $this->xiaomiBrand->id,
            'is_active' => true,
        ]);

        // Create test part
        $this->displayPart = Part::factory()->create([
            'name' => 'OLED Display Assembly',
        ]);
    }

    public function test_bulk_brand_search_returns_available_brands(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=&limit=20');

        $response->assertStatus(200);
        
        $brands = $response->json();
        $this->assertIsArray($brands);
        $this->assertGreaterThanOrEqual(3, count($brands)); // Should have at least our 3 test brands
        
        // Check that brands have required fields for SearchableSelect
        foreach ($brands as $brand) {
            $this->assertArrayHasKey('value', $brand);
            $this->assertArrayHasKey('label', $brand);
            $this->assertArrayHasKey('image', $brand);
        }
    }

    public function test_bulk_brand_search_filters_by_query(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=apple&limit=20');

        $response->assertStatus(200);
        
        $brands = $response->json();
        $this->assertIsArray($brands);
        
        // Should only return Apple brand
        $this->assertCount(1, $brands);
        $this->assertEquals('Apple', $brands[0]['label']);
    }

    public function test_bulk_model_search_returns_models_for_brand(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=&brand_id=' . $this->appleBrand->id . '&limit=20');

        $response->assertStatus(200);
        
        $models = $response->json();
        $this->assertIsArray($models);
        $this->assertGreaterThanOrEqual(2, count($models)); // Should have iPhone 15 and iPhone 14
        
        // Check that models have required fields for SearchableSelect
        foreach ($models as $model) {
            $this->assertArrayHasKey('value', $model);
            $this->assertArrayHasKey('label', $model);
            $this->assertArrayHasKey('description', $model);
        }
    }

    public function test_bulk_model_search_filters_by_query(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=iphone%2015&brand_id=' . $this->appleBrand->id . '&limit=20');

        $response->assertStatus(200);
        
        $models = $response->json();
        $this->assertIsArray($models);
        
        // Should only return iPhone 15
        $this->assertCount(1, $models);
        $this->assertStringContainsString('iPhone 15', $models[0]['label']);
    }

    public function test_bulk_model_search_excludes_compatible_models(): void
    {
        // First, make iPhone 15 compatible with the display part
        $this->displayPart->models()->attach($this->iphone15->id, [
            'compatibility_notes' => 'Test compatibility',
            'is_verified' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=&brand_id=' . $this->appleBrand->id . '&exclude_compatible=1&part_id=' . $this->displayPart->id . '&limit=20');

        $response->assertStatus(200);
        
        $models = $response->json();
        $this->assertIsArray($models);
        
        // Should only return iPhone 14 (iPhone 15 should be excluded)
        $this->assertCount(1, $models);
        $this->assertStringContainsString('iPhone 14', $models[0]['label']);
    }

    public function test_bulk_search_performance_with_multiple_brands(): void
    {
        // Create additional brands and models for performance testing
        $additionalBrands = Brand::factory()->count(20)->create(['is_active' => true]);
        
        foreach ($additionalBrands as $brand) {
            MobileModel::factory()->count(5)->create([
                'brand_id' => $brand->id,
                'is_active' => true,
            ]);
        }

        $startTime = microtime(true);
        
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=&limit=50');
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;
        
        $response->assertStatus(200);
        $this->assertLessThan(1000, $executionTime, 'Bulk brand search should complete within 1 second');
        
        $brands = $response->json();
        $this->assertLessThanOrEqual(50, count($brands));
    }

    public function test_bulk_search_handles_special_characters(): void
    {
        // Test brand search with special characters
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=' . urlencode('test@#$%') . '&limit=10');
        
        $response->assertStatus(200);
        $this->assertIsArray($response->json());
        
        // Test model search with special characters
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=' . urlencode('test@#$%') . '&brand_id=' . $this->appleBrand->id . '&limit=10');
        
        $response->assertStatus(200);
        $this->assertIsArray($response->json());
    }

    public function test_bulk_search_respects_active_only_filter(): void
    {
        // Create inactive brand and model
        $inactiveBrand = Brand::factory()->create([
            'name' => 'Inactive Brand',
            'is_active' => false,
        ]);
        
        $inactiveModel = MobileModel::factory()->create([
            'name' => 'Inactive Model',
            'brand_id' => $this->appleBrand->id,
            'is_active' => false,
        ]);

        // Test brand search
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=inactive&limit=20');

        $response->assertStatus(200);
        $brands = $response->json();
        
        // Should not return inactive brand
        $this->assertEmpty($brands);

        // Test model search
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=inactive&brand_id=' . $this->appleBrand->id . '&limit=20');

        $response->assertStatus(200);
        $models = $response->json();
        
        // Should not return inactive model
        $this->assertEmpty($models);
    }

    public function test_bulk_search_concurrent_requests(): void
    {
        // Test concurrent brand searches
        $responses = [];
        for ($i = 0; $i < 3; $i++) {
            $responses[] = $this->actingAs($this->user)
                ->get('/api/search/brands?q=test' . $i . '&limit=10');
        }
        
        foreach ($responses as $response) {
            $response->assertStatus(200);
            $this->assertIsArray($response->json());
        }

        // Test concurrent model searches
        $responses = [];
        for ($i = 0; $i < 3; $i++) {
            $responses[] = $this->actingAs($this->user)
                ->get('/api/search/models?q=test' . $i . '&brand_id=' . $this->appleBrand->id . '&limit=10');
        }
        
        foreach ($responses as $response) {
            $response->assertStatus(200);
            $this->assertIsArray($response->json());
        }
    }
}
