<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchableSelectPerformanceTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Brand $appleBrand;
    private Brand $samsungBrand;
    private MobileModel $iphone15;
    private MobileModel $galaxyS24;
    private Part $displayPart;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        // Create test brands
        $this->appleBrand = Brand::factory()->create([
            'name' => 'Apple',
            'logo_url' => 'https://example.com/apple-logo.png',
            'is_active' => true,
        ]);

        $this->samsungBrand = Brand::factory()->create([
            'name' => 'Samsung',
            'logo_url' => 'https://example.com/samsung-logo.png',
            'is_active' => true,
        ]);

        // Create test models
        $this->iphone15 = MobileModel::factory()->create([
            'name' => 'iPhone 15 Pro',
            'model_number' => 'A3108',
            'release_year' => 2023,
            'brand_id' => $this->appleBrand->id,
            'is_active' => true,
        ]);

        $this->galaxyS24 = MobileModel::factory()->create([
            'name' => 'Galaxy S24',
            'model_number' => 'SM-S921B',
            'release_year' => 2024,
            'brand_id' => $this->samsungBrand->id,
            'is_active' => true,
        ]);

        // Create test part
        $this->displayPart = Part::factory()->create([
            'name' => 'OLED Display',
        ]);
    }

    public function test_brand_search_api_performance(): void
    {
        $startTime = microtime(true);
        
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=app&limit=20');

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

        $response->assertStatus(200);
        $this->assertLessThan(500, $executionTime, 'Brand search API should respond within 500ms');
        
        $brands = $response->json();
        $this->assertIsArray($brands);
        $this->assertNotEmpty($brands);
    }

    public function test_model_search_api_performance(): void
    {
        $startTime = microtime(true);
        
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=iphone&limit=20');

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

        $response->assertStatus(200);
        $this->assertLessThan(500, $executionTime, 'Model search API should respond within 500ms');
        
        $models = $response->json();
        $this->assertIsArray($models);
        $this->assertNotEmpty($models);
    }

    public function test_multiple_rapid_brand_searches_no_timeout(): void
    {
        // Simulate rapid searches that could cause infinite loops
        $searches = ['a', 'ap', 'app', 'appl', 'apple'];
        
        foreach ($searches as $query) {
            $startTime = microtime(true);
            
            $response = $this->actingAs($this->user)
                ->get('/api/search/brands?q=' . $query . '&limit=10');
            
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime) * 1000;
            
            $response->assertStatus(200);
            $this->assertLessThan(1000, $executionTime, "Search for '{$query}' should complete within 1 second");
        }
    }

    public function test_multiple_rapid_model_searches_no_timeout(): void
    {
        // Simulate rapid searches that could cause infinite loops
        $searches = ['i', 'ip', 'iph', 'ipho', 'iphon', 'iphone'];
        
        foreach ($searches as $query) {
            $startTime = microtime(true);
            
            $response = $this->actingAs($this->user)
                ->get('/api/search/models?q=' . $query . '&limit=10');
            
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime) * 1000;
            
            $response->assertStatus(200);
            $this->assertLessThan(1000, $executionTime, "Search for '{$query}' should complete within 1 second");
        }
    }

    public function test_empty_query_handling(): void
    {
        // Test that empty queries don't cause infinite loops
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=&limit=20');

        $response->assertStatus(200);
        
        $brands = $response->json();
        $this->assertIsArray($brands);
        $this->assertLessThanOrEqual(20, count($brands));
    }

    public function test_concurrent_search_requests(): void
    {
        // Test that concurrent requests don't interfere with each other
        $responses = [];
        
        // Make multiple concurrent requests
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->actingAs($this->user)
                ->get('/api/search/brands?q=test' . $i . '&limit=10');
        }
        
        // Verify all responses are successful
        foreach ($responses as $response) {
            $response->assertStatus(200);
            $this->assertIsArray($response->json());
        }
    }

    public function test_large_dataset_performance(): void
    {
        // Create a larger dataset to test performance
        Brand::factory()->count(100)->create(['is_active' => true]);
        MobileModel::factory()->count(500)->create(['is_active' => true]);
        
        $startTime = microtime(true);
        
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=&limit=50');
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;
        
        $response->assertStatus(200);
        $this->assertLessThan(2000, $executionTime, 'Large dataset search should complete within 2 seconds');
        
        $brands = $response->json();
        $this->assertLessThanOrEqual(50, count($brands));
    }

    public function test_search_with_special_characters(): void
    {
        // Test that special characters don't cause issues
        $specialQueries = ['test@', 'test#', 'test%', 'test&', 'test+'];
        
        foreach ($specialQueries as $query) {
            $response = $this->actingAs($this->user)
                ->get('/api/search/brands?q=' . urlencode($query) . '&limit=10');
            
            $response->assertStatus(200);
            $this->assertIsArray($response->json());
        }
    }
}
