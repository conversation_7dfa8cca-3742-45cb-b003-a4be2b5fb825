<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchConfigurationTabsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user using one of the valid admin emails
        $this->admin = $this->createAdminUser([
            'email' => '<EMAIL>',
        ]);

        // Initialize search configurations
        SearchConfiguration::initializeDefaults();
    }

    public function test_search_configuration_page_loads_successfully()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/search-config');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/SearchConfiguration/Index')
        );
    }

    public function test_configuration_tab_shows_only_search_related_categories()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/search-config');

        $response->assertInertia(fn ($page) =>
            $page->has('configurations')
                ->has('configurations.guest_limits')
                ->has('configurations.display')
                ->has('configurations.tracking')
                ->has('configurations.watermark')
                ->has('configurations.copy_protection')
        );
    }

    public function test_search_categories_have_correct_settings()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/search-config');

        $response->assertInertia(fn ($page) =>
            $page->where('configurations.guest_limits.guest_search_limit.key', 'guest_search_limit')
                ->where('configurations.guest_limits.guest_search_limit.category', 'guest_limits')
                ->where('configurations.display.enable_partial_results.key', 'enable_partial_results')
                ->where('configurations.display.enable_partial_results.category', 'display')
                ->where('configurations.tracking.track_guest_searches.key', 'track_guest_searches')
                ->where('configurations.tracking.track_guest_searches.category', 'tracking')
        );
    }

    public function test_watermark_tab_has_correct_settings()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/search-config');

        $response->assertInertia(fn ($page) => 
            $page->where('configurations.watermark.watermark_enabled.key', 'watermark_enabled')
                ->where('configurations.watermark.watermark_enabled.category', 'watermark')
                ->where('configurations.watermark.watermark_logo_url.key', 'watermark_logo_url')
                ->where('configurations.watermark.watermark_text.key', 'watermark_text')
        );
    }

    public function test_copy_protection_tab_has_correct_settings()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/search-config');

        $response->assertInertia(fn ($page) => 
            $page->where('configurations.copy_protection.copy_protection_enabled.key', 'copy_protection_enabled')
                ->where('configurations.copy_protection.copy_protection_enabled.category', 'copy_protection')
                ->where('configurations.copy_protection.copy_protection_level.key', 'copy_protection_level')
                ->where('configurations.copy_protection.copy_protection_compatible_models.key', 'copy_protection_compatible_models')
        );
    }

    public function test_configuration_update_works_correctly()
    {
        $response = $this->actingAs($this->admin)
            ->post('/admin/search-config/update', [
                'configurations' => [
                    [
                        'key' => 'guest_search_limit',
                        'value' => 15,
                        'type' => 'integer'
                    ],
                    [
                        'key' => 'watermark_enabled',
                        'value' => true,
                        'type' => 'boolean'
                    ],
                    [
                        'key' => 'copy_protection_enabled',
                        'value' => true,
                        'type' => 'boolean'
                    ]
                ]
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify the configurations were updated
        $this->assertEquals(15, SearchConfiguration::get('guest_search_limit'));
        $this->assertTrue(SearchConfiguration::get('watermark_enabled'));
        $this->assertTrue(SearchConfiguration::get('copy_protection_enabled'));
    }

    public function test_categories_are_properly_separated()
    {
        // Test that search categories don't include watermark/copy_protection in main config
        $searchCategories = ['guest_limits', 'display', 'tracking'];
        $dedicatedCategories = ['watermark', 'copy_protection'];
        
        $allConfigs = SearchConfiguration::where('is_active', true)->get()->groupBy('category');
        
        // Verify search categories exist
        foreach ($searchCategories as $category) {
            $this->assertTrue($allConfigs->has($category), "Search category {$category} should exist");
        }
        
        // Verify dedicated categories exist
        foreach ($dedicatedCategories as $category) {
            $this->assertTrue($allConfigs->has($category), "Dedicated category {$category} should exist");
        }
        
        // Verify categories are properly separated
        $this->assertNotEmpty($allConfigs['guest_limits']);
        $this->assertNotEmpty($allConfigs['watermark']);
        $this->assertNotEmpty($allConfigs['copy_protection']);
    }
}
