<?php

namespace Tests\Feature;

use App\Models\AdConfiguration;
use App\Models\AdPerformance;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdTrackingApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_track_impression_endpoint_works()
    {
        $response = $this->postJson('/api/ads/track-impression', [
            'zone' => 'header',
            'page' => '/home',
            'ad_slot' => '1234567890',
            'user_type' => 'free',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Impression tracked successfully',
                ]);

        $this->assertDatabaseHas('ad_performance', [
            'zone' => 'header',
            'page' => '/home',
            'impressions' => 1,
        ]);
    }

    public function test_track_impression_validates_required_fields()
    {
        $response = $this->postJson('/api/ads/track-impression', [
            'page' => '/home',
            // Missing required 'zone' field
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['zone']);
    }

    public function test_track_impression_validates_zone_values()
    {
        $response = $this->postJson('/api/ads/track-impression', [
            'zone' => 'invalid_zone',
            'page' => '/home',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['zone']);
    }

    public function test_track_click_endpoint_works()
    {
        $response = $this->postJson('/api/ads/track-click', [
            'zone' => 'sidebar',
            'page' => '/search',
            'ad_slot' => '1234567890',
            'user_type' => 'guest',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Click tracked successfully',
                ]);

        $this->assertDatabaseHas('ad_performance', [
            'zone' => 'sidebar',
            'page' => '/search',
            'clicks' => 1,
        ]);
    }

    public function test_should_show_ads_endpoint_for_guest()
    {
        $response = $this->getJson('/api/ads/should-show');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'should_show_ads' => true,
                        'user_type' => 'guest',
                    ],
                ]);
    }

    public function test_should_show_ads_endpoint_for_free_user()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free',
        ]);

        $response = $this->actingAs($user)->getJson('/api/ads/should-show');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'should_show_ads' => true,
                        'user_type' => 'free',
                    ],
                ]);
    }

    public function test_should_show_ads_endpoint_for_premium_user()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'premium',
            'subscription_status' => 'active',
            'subscription_ends_at' => now()->addMonth(),
        ]);

        // Create an active subscription for the user
        $user->subscriptions()->create([
            'plan_name' => 'Premium Plan',
            'status' => 'active',
            'current_period_start' => now()->subDay(),
            'current_period_end' => now()->addMonth(),
        ]);

        $response = $this->actingAs($user)->getJson('/api/ads/should-show');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'should_show_ads' => false,
                        'user_type' => 'premium',
                    ],
                ]);
    }

    public function test_should_show_ads_endpoint_for_admin_user()
    {
        $user = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($user)->getJson('/api/ads/should-show');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'should_show_ads' => false,
                        'user_type' => 'admin',
                    ],
                ]);
    }

    public function test_get_configuration_endpoint_returns_config()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'page_types' => [], // Empty means applies to all pages
            'user_types' => [], // Empty means applies to all user types
            'ad_slot_id' => '1234567890',
            'ad_format' => 'responsive',
            'frequency_rules' => ['max_ads_per_page' => 3],
            'targeting_rules' => ['device_type' => 'desktop'],
        ]);

        $response = $this->getJson('/api/ads/configuration?zone=header&page=home');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'zone' => 'header',
                        'enabled' => true,
                        'ad_slot_id' => '1234567890',
                        'ad_format' => 'responsive',
                    ],
                ]);
    }

    public function test_get_configuration_endpoint_returns_404_for_no_config()
    {
        $response = $this->getJson('/api/ads/configuration?zone=header&page=nonexistent');

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'No ad configuration found for this zone and page',
                ]);
    }

    public function test_get_page_configurations_endpoint_returns_all_zones()
    {
        // Create configurations for multiple zones
        $zones = ['header', 'sidebar', 'content'];
        foreach ($zones as $zone) {
            AdConfiguration::create([
                'zone' => $zone,
                'enabled' => true,
                'page_types' => [], // Empty means applies to all pages
                'user_types' => [], // Empty means applies to all user types
                'ad_format' => 'responsive',
            ]);
        }

        $response = $this->getJson('/api/ads/page-configurations?page=home');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'page' => 'home',
                        'should_show_ads' => true,
                    ],
                ]);

        $responseData = $response->json('data.zones');
        $this->assertArrayHasKey('header', $responseData);
        $this->assertArrayHasKey('sidebar', $responseData);
        $this->assertArrayHasKey('content', $responseData);
    }

    public function test_get_page_configurations_validates_required_fields()
    {
        $response = $this->getJson('/api/ads/page-configurations');

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['page']);
    }

    public function test_multiple_impressions_increment_counter()
    {
        // Track first impression
        $response1 = $this->postJson('/api/ads/track-impression', [
            'zone' => 'header',
            'page' => '/home',
        ]);
        $response1->assertStatus(200);

        // Track second impression
        $response2 = $this->postJson('/api/ads/track-impression', [
            'zone' => 'header',
            'page' => '/home',
        ]);
        $response2->assertStatus(200);

        // Check that impressions were incremented
        $this->assertDatabaseHas('ad_performance', [
            'zone' => 'header',
            'page' => '/home',
            'user_type' => 'guest',
            'device_type' => 'desktop',
            'impressions' => 2,
        ]);

        // Ensure only one record exists
        $count = \App\Models\AdPerformance::where('zone', 'header')
            ->where('page', '/home')
            ->where('user_type', 'guest')
            ->where('device_type', 'desktop')
            ->count();
        $this->assertEquals(1, $count);
    }

    public function test_multiple_clicks_increment_counter()
    {
        // Track first click
        $this->postJson('/api/ads/track-click', [
            'zone' => 'header',
            'page' => '/home',
        ]);

        // Track second click
        $this->postJson('/api/ads/track-click', [
            'zone' => 'header',
            'page' => '/home',
        ]);

        // Since these are guest requests, user_type should be 'guest'
        $this->assertDatabaseHas('ad_performance', [
            'zone' => 'header',
            'page' => '/home',
            'user_type' => 'guest',
            'device_type' => 'desktop',
            'clicks' => 2,
        ]);
    }

    public function test_ctr_calculation_updates_correctly()
    {
        // Track impressions
        for ($i = 0; $i < 100; $i++) {
            $this->postJson('/api/ads/track-impression', [
                'zone' => 'header',
                'page' => '/home',
            ]);
        }

        // Track clicks
        for ($i = 0; $i < 5; $i++) {
            $this->postJson('/api/ads/track-click', [
                'zone' => 'header',
                'page' => '/home',
            ]);
        }

        $performance = AdPerformance::where('zone', 'header')
                                   ->where('page', '/home')
                                   ->where('user_type', 'guest')
                                   ->where('device_type', 'desktop')
                                   ->first();

        $this->assertEquals(100, $performance->impressions);
        $this->assertEquals(5, $performance->clicks);
        $this->assertEquals(5.0, $performance->ctr); // 5/100 * 100 = 5%
    }
}
