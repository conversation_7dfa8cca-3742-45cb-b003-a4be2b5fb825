<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AdminAuthenticationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that admin users can authenticate with correct credentials.
     */
    public function test_admin_users_can_authenticate_with_correct_credentials()
    {
        // Create admin users with known credentials
        $adminUsers = [
            [
                'email' => '<EMAIL>',
                'password' => 'R1451212',
                'name' => 'Nezam Uddin',
                'is_admin' => 1,
            ],
            [
                'email' => '<EMAIL>', 
                'password' => 'content123',
                'name' => 'Content Manager',
                'is_admin' => 1,
            ],
            [
                'email' => '<EMAIL>',
                'password' => 'admin123', 
                'name' => 'Admin User',
                'is_admin' => 1,
            ],
        ];

        foreach ($adminUsers as $adminData) {
            $user = User::factory()->create([
                'email' => $adminData['email'],
                'name' => $adminData['name'],
                'password' => Hash::make($adminData['password']),
                'is_admin' => $adminData['is_admin'],
                'status' => 'active',
                'approval_status' => 'approved',
                'email_verified_at' => now(),
            ]);

            // Test authentication
            $response = $this->post('/login', [
                'email' => $adminData['email'],
                'password' => $adminData['password'],
            ]);

            $response->assertRedirect('/admin/dashboard');
            $this->assertAuthenticatedAs($user);

            // Test admin status
            $this->assertTrue($user->isAdmin());

            // Logout for next test
            $this->post('/logout');
        }
    }

    /**
     * Test that admin users cannot authenticate with incorrect passwords.
     */
    public function test_admin_users_cannot_authenticate_with_incorrect_passwords()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('R1451212'),
            'is_admin' => 1,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertSessionHasErrors();
        $this->assertGuest();
    }

    /**
     * Test that admin users can access admin routes after authentication.
     */
    public function test_authenticated_admin_users_can_access_admin_routes()
    {
        $adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'is_admin' => 1,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($adminUser);

        $adminRoutes = [
            '/admin/dashboard',
            '/admin/users',
            '/admin/brands',
            '/admin/models',
        ];

        foreach ($adminRoutes as $route) {
            $response = $this->get($route);
            $this->assertNotEquals(403, $response->getStatusCode(), 
                "Admin should have access to {$route}");
        }
    }

    /**
     * Test that non-admin users cannot access admin routes.
     */
    public function test_non_admin_users_cannot_access_admin_routes()
    {
        $regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($regularUser);

        $adminRoutes = [
            '/admin/dashboard',
            '/admin/users',
            '/admin/brands',
            '/admin/models',
        ];

        foreach ($adminRoutes as $route) {
            $response = $this->get($route);
            $response->assertStatus(403);
        }
    }

    /**
     * Test User::isAdmin() method with database field.
     */
    public function test_user_is_admin_method_with_database_field()
    {
        // Test user with is_admin = 1
        $adminUser = User::factory()->create(['is_admin' => 1]);
        $this->assertTrue($adminUser->isAdmin());

        // Test user with is_admin = 0
        $regularUser = User::factory()->create(['is_admin' => 0]);
        $this->assertFalse($regularUser->isAdmin());
    }

    /**
     * Test User::isAdmin() method with database fields only.
     */
    public function test_user_is_admin_method_with_database_fields_only()
    {
        // Test that emails alone don't make users admin
        $previouslyAdminEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($previouslyAdminEmails as $email) {
            $user = User::factory()->create(['email' => $email]);
            $this->assertFalse($user->isAdmin(), "User with email {$email} should NOT be admin without is_admin field set");
        }

        // Test that is_admin field makes users admin regardless of email
        $adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
            'role' => 'admin',
        ]);
        $this->assertTrue($adminUser->isAdmin());

        // Test non-admin email
        $regularUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->assertFalse($regularUser->isAdmin());
    }

    /**
     * Test that admin status changes are reflected immediately.
     */
    public function test_admin_status_changes_are_reflected_immediately()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => 0,
        ]);

        // Initially not admin
        $this->assertFalse($user->isAdmin());

        // Promote to admin
        $user->update(['is_admin' => 1]);
        $user->refresh();
        $this->assertTrue($user->isAdmin());

        // Demote from admin
        $user->update(['is_admin' => 0]);
        $user->refresh();
        $this->assertFalse($user->isAdmin());
    }

    /**
     * Test admin management command functionality.
     */
    public function test_admin_management_command_list()
    {
        // Create some admin users
        User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => 1,
            'name' => 'Admin One',
        ]);

        User::factory()->create([
            'email' => '<EMAIL>', 
            'is_admin' => 1,
            'name' => 'Admin Two',
        ]);

        $this->artisan('admin:manage list')
            ->expectsOutput('Admin Users:')
            ->assertExitCode(0);
    }

    /**
     * Test admin management command promote functionality.
     */
    public function test_admin_management_command_promote()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => 0,
        ]);

        $this->artisan('admin:manage promote --email=<EMAIL>')
            ->expectsOutput("User '<EMAIL>' has been promoted to admin.")
            ->assertExitCode(0);

        $user->refresh();
        $this->assertTrue($user->isAdmin());
    }

    /**
     * Test admin management command demote functionality.
     */
    public function test_admin_management_command_demote()
    {
        // Create multiple admin users so we can demote one
        User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => 1,
        ]);

        $adminToDemote = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => 1,
        ]);

        $this->artisan('admin:manage demote --email=<EMAIL>')
            ->expectsOutput("User '<EMAIL>' has been demoted from admin.")
            ->assertExitCode(0);

        $adminToDemote->refresh();
        $this->assertFalse($adminToDemote->isAdmin());
    }

    /**
     * Test that the last admin cannot be demoted.
     */
    public function test_last_admin_cannot_be_demoted()
    {
        $lastAdmin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => 1,
        ]);

        $this->artisan('admin:manage demote --email=<EMAIL>')
            ->expectsOutput("Cannot demote the last admin user.")
            ->assertExitCode(1);

        $lastAdmin->refresh();
        $this->assertTrue($lastAdmin->isAdmin());
    }
}
