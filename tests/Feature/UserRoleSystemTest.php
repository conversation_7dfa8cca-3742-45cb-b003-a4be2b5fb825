<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class UserRoleSystemTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test user role assignment and methods.
     */
    public function test_user_role_assignment_and_methods()
    {
        // Create users with different roles
        $admin = User::factory()->create(['role' => 'admin', 'is_admin' => 1]);
        $contentManager = User::factory()->create(['role' => 'content_manager', 'is_admin' => 0]);
        $regularUser = User::factory()->create(['role' => 'user', 'is_admin' => 0]);

        // Test admin user
        $this->assertEquals('admin', $admin->getRole());
        $this->assertTrue($admin->hasRole('admin'));
        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($admin->isContentManager());
        $this->assertTrue($admin->canManageContent());
        $this->assertTrue($admin->canManageUsers());
        $this->assertTrue($admin->canAccessAdminDashboard());

        // Test content manager
        $this->assertEquals('content_manager', $contentManager->getRole());
        $this->assertTrue($contentManager->hasRole('content_manager'));
        $this->assertFalse($contentManager->isAdmin());
        $this->assertTrue($contentManager->isContentManager());
        $this->assertTrue($contentManager->canManageContent());
        $this->assertFalse($contentManager->canManageUsers());
        $this->assertTrue($contentManager->canAccessAdminDashboard());

        // Test regular user
        $this->assertEquals('user', $regularUser->getRole());
        $this->assertTrue($regularUser->hasRole('user'));
        $this->assertFalse($regularUser->isAdmin());
        $this->assertFalse($regularUser->isContentManager());
        $this->assertFalse($regularUser->canManageContent());
        $this->assertFalse($regularUser->canManageUsers());
        $this->assertFalse($regularUser->canAccessAdminDashboard());
    }

    /**
     * Test backward compatibility with is_admin field.
     */
    public function test_backward_compatibility_with_is_admin_field()
    {
        // Create user with is_admin = 1 and role = admin
        $admin = User::factory()->create(['is_admin' => 1, 'role' => 'admin']);

        // Should work as admin
        $this->assertTrue($admin->isAdmin());
        $this->assertTrue($admin->canManageContent());
        $this->assertTrue($admin->canManageUsers());

        // Create user with is_admin = 0 and role = user
        $user = User::factory()->create(['is_admin' => 0, 'role' => 'user']);

        // Should work as regular user
        $this->assertFalse($user->isAdmin());
        $this->assertFalse($user->canManageContent());
        $this->assertFalse($user->canManageUsers());

        // Test that role field takes precedence over is_admin field
        $contentManager = User::factory()->create(['is_admin' => 0, 'role' => 'content_manager']);
        $this->assertFalse($contentManager->isAdmin()); // is_admin = 0
        $this->assertTrue($contentManager->canManageContent()); // but role = content_manager
    }

    /**
     * Test admin user creation with role assignment.
     */
    public function test_admin_user_creation_with_role_assignment()
    {
        $admin = User::factory()->create(['is_admin' => 1]);

        $response = $this->actingAs($admin)->post('/admin/users', [
            'name' => 'Content Manager User',
            'email' => '<EMAIL>',
            'role' => 'content_manager',
            'generate_password' => true,
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
            'send_welcome_email' => false,
        ]);

        $response->assertRedirect();

        $createdUser = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($createdUser);
        $this->assertEquals('content_manager', $createdUser->role);
        $this->assertEquals(0, $createdUser->is_admin); // is_admin should be 0 for content manager
        $this->assertTrue($createdUser->isContentManager());
        $this->assertTrue($createdUser->canManageContent());
        $this->assertFalse($createdUser->canManageUsers());
    }

    /**
     * Test content manager access to content routes.
     */
    public function test_content_manager_access_to_content_routes()
    {
        $contentManager = User::factory()->create([
            'role' => 'content_manager',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($contentManager);

        // Should have access to content management routes
        $contentRoutes = [
            '/admin/dashboard',
            '/admin/parts',
            '/admin/brands',
            '/admin/models',
            '/admin/categories',
        ];

        foreach ($contentRoutes as $route) {
            $response = $this->get($route);
            $this->assertNotEquals(403, $response->getStatusCode(), 
                "Content Manager should have access to {$route}");
        }
    }

    /**
     * Test content manager restriction from admin-only routes.
     */
    public function test_content_manager_restriction_from_admin_only_routes()
    {
        $contentManager = User::factory()->create([
            'role' => 'content_manager',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($contentManager);

        // Should NOT have access to admin-only routes
        $adminOnlyRoutes = [
            '/admin/users',
            '/admin/payment-requests',
        ];

        foreach ($adminOnlyRoutes as $route) {
            $response = $this->get($route);
            $response->assertStatus(403);
        }
    }

    /**
     * Test admin login functionality.
     */
    public function test_admin_login_functionality()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('R1451212'),
            'role' => 'admin',
            'is_admin' => 1,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'R1451212',
        ]);

        $response->assertRedirect('/admin/dashboard');
        $this->assertAuthenticatedAs($admin);
    }

    /**
     * Test content manager login functionality - should redirect to admin dashboard.
     */
    public function test_content_manager_login_functionality()
    {
        $contentManager = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'content_manager',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Content managers should be redirected to admin dashboard
        $response->assertRedirect('/admin/dashboard');
        $this->assertAuthenticatedAs($contentManager);
    }

    /**
     * Test regular user login functionality - should redirect to user dashboard.
     */
    public function test_regular_user_login_functionality()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'user',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Regular users should be redirected to user dashboard
        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }
}
