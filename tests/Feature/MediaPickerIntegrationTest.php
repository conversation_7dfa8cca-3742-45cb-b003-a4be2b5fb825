<?php

namespace Tests\Feature;

use App\Models\Media;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class MediaPickerIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved',
            'approved_at' => now(),
            'is_admin' => true,
            'role' => 'admin',
        ]);
        Storage::fake('public');
    }

    public function test_media_picker_can_upload_files()
    {
        $file = UploadedFile::fake()->image('test-logo.png', 200, 200);

        $response = $this->actingAs($this->admin)
                        ->post('/admin/media', [
                            'files' => [$file]
                        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => '1 file(s) uploaded successfully.'
        ]);

        $this->assertDatabaseHas('media', [
            'original_filename' => 'test-logo.png',
            'mime_type' => 'image/png',
            'uploaded_by' => $this->admin->id
        ]);
    }

    public function test_media_picker_select_endpoint()
    {
        $media = Media::factory()->create([
            'original_filename' => 'test-image.jpg',
            'mime_type' => 'image/jpeg',
            'uploaded_by' => $this->admin->id
        ]);

        $response = $this->actingAs($this->admin)
                        ->get('/admin/media/select');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'original_filename',
                    'mime_type',
                    'url',
                    'thumbnail_url'
                ]
            ]
        ]);
    }

    public function test_media_picker_filters_by_type()
    {
        Media::factory()->create([
            'original_filename' => 'image.jpg',
            'mime_type' => 'image/jpeg',
            'uploaded_by' => $this->admin->id
        ]);

        Media::factory()->create([
            'original_filename' => 'document.pdf',
            'mime_type' => 'application/pdf',
            'uploaded_by' => $this->admin->id
        ]);

        $response = $this->actingAs($this->admin)
                        ->get('/admin/media/select?type=images');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertEquals('image/jpeg', $data[0]['mime_type']);
    }

    public function test_media_picker_search_functionality()
    {
        Media::factory()->create([
            'original_filename' => 'logo.png',
            'title' => 'Company Logo',
            'uploaded_by' => $this->admin->id
        ]);

        Media::factory()->create([
            'original_filename' => 'banner.jpg',
            'title' => 'Website Banner',
            'uploaded_by' => $this->admin->id
        ]);

        $response = $this->actingAs($this->admin)
                        ->get('/admin/media/select?search=logo');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertEquals('logo.png', $data[0]['original_filename']);
    }

    public function test_media_picker_pagination()
    {
        // Create 30 media items
        Media::factory()->count(30)->create([
            'uploaded_by' => $this->admin->id
        ]);

        $response = $this->actingAs($this->admin)
                        ->get('/admin/media/select');

        $response->assertStatus(200);
        $responseData = $response->json();
        
        $this->assertEquals(24, count($responseData['data'])); // Default page size
        $this->assertArrayHasKey('current_page', $responseData);
        $this->assertArrayHasKey('last_page', $responseData);
    }

    public function test_media_model_url_generation()
    {
        $media = Media::factory()->create([
            'filename' => 'test-file.jpg',
            'path' => 'media/test-file.jpg',
            'disk' => 'public'
        ]);

        $url = $media->url;
        $this->assertStringContainsString('storage/media/test-file.jpg', $url);
    }

    public function test_media_model_image_detection()
    {
        $imageMedia = Media::factory()->create([
            'mime_type' => 'image/jpeg'
        ]);

        $documentMedia = Media::factory()->create([
            'mime_type' => 'application/pdf'
        ]);

        $this->assertTrue($imageMedia->isImage());
        $this->assertFalse($documentMedia->isImage());
    }

    public function test_media_model_formatted_size()
    {
        $media = Media::factory()->create([
            'size' => 1024 * 1024 // 1MB
        ]);

        $this->assertEquals('1024 KB', $media->formatted_size);
    }

    public function test_media_scopes()
    {
        Media::factory()->create(['mime_type' => 'image/jpeg']);
        Media::factory()->create(['mime_type' => 'image/png']);
        Media::factory()->create(['mime_type' => 'application/pdf']);

        $images = Media::images()->get();
        $this->assertCount(2, $images);

        $pdfs = Media::byType('application')->get();
        $this->assertCount(1, $pdfs);
    }

    public function test_media_deletion_removes_file()
    {
        $file = UploadedFile::fake()->image('test-delete.jpg');
        
        $response = $this->actingAs($this->admin)
                        ->post('/admin/media', [
                            'files' => [$file]
                        ]);

        $media = Media::first();
        Storage::disk('public')->assertExists($media->path);

        $deleteResponse = $this->actingAs($this->admin)
                              ->delete("/admin/media/{$media->id}");

        $deleteResponse->assertStatus(200);
        $deleteResponse->assertJson([
            'message' => 'Media deleted successfully.'
        ]);
        Storage::disk('public')->assertMissing($media->path);
        $this->assertDatabaseMissing('media', ['id' => $media->id]);
    }

    public function test_media_update_functionality()
    {
        $media = Media::factory()->create([
            'title' => 'Original Title',
            'alt_text' => 'Original Alt',
            'description' => 'Original Description'
        ]);

        $response = $this->actingAs($this->admin)
                        ->put("/admin/media/{$media->id}", [
                            'title' => 'Updated Title',
                            'alt_text' => 'Updated Alt',
                            'description' => 'Updated Description'
                        ]);

        $response->assertStatus(200);
        
        $media->refresh();
        $this->assertEquals('Updated Title', $media->title);
        $this->assertEquals('Updated Alt', $media->alt_text);
        $this->assertEquals('Updated Description', $media->description);
    }

    public function test_media_picker_file_type_validation()
    {
        $invalidFile = UploadedFile::fake()->create('malicious.exe', 1000);

        $response = $this->actingAs($this->admin)
                        ->withHeaders(['Accept' => 'application/json'])
                        ->post('/admin/media', [
                            'files' => [$invalidFile]
                        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'message' => 'The given data was invalid.',
            'errors' => [
                'files.0' => ['The file type is not allowed.']
            ]
        ]);
    }

    public function test_media_picker_file_size_validation()
    {
        $largeFile = UploadedFile::fake()->create('large-file.jpg', 15000); // 15MB

        $response = $this->actingAs($this->admin)
                        ->withHeaders(['Accept' => 'application/json'])
                        ->post('/admin/media', [
                            'files' => [$largeFile]
                        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['files.0']);
    }
}
