<?php

namespace Tests\Feature;

use App\Models\AdConfiguration;
use App\Models\AdPerformance;
use App\Models\User;
use App\Services\AdSenseService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class AdSenseServiceTest extends TestCase
{
    use RefreshDatabase;

    protected AdSenseService $adSenseService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->adSenseService = app(AdSenseService::class);
    }

    public function test_should_show_ads_returns_false_for_admin_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $this->assertFalse($this->adSenseService->shouldShowAds($admin));
    }

    public function test_should_show_ads_returns_false_for_premium_users()
    {
        $premium = User::factory()->create([
            'subscription_plan' => 'premium',
            'subscription_status' => 'active',
            'subscription_ends_at' => now()->addMonth(),
        ]);

        // Create an active subscription for the user
        $premium->subscriptions()->create([
            'plan_name' => 'Premium Plan',
            'status' => 'active',
            'current_period_start' => now()->subDay(),
            'current_period_end' => now()->addMonth(),
        ]);

        $this->assertFalse($this->adSenseService->shouldShowAds($premium));
    }

    public function test_should_show_ads_returns_true_for_free_users()
    {
        $free = User::factory()->create([
            'subscription_plan' => 'free',
            'subscription_status' => null,
        ]);

        $this->assertTrue($this->adSenseService->shouldShowAds($free));
    }

    public function test_should_show_ads_returns_true_for_guest_users()
    {
        $this->assertTrue($this->adSenseService->shouldShowAds(null));
    }

    public function test_get_ad_configuration_returns_correct_config()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'page_types' => ['home'],
            'user_types' => ['free'],
            'ad_format' => 'responsive',
            'priority' => 1,
        ]);

        $free = User::factory()->create();
        
        $result = $this->adSenseService->getAdConfiguration('header', 'home', $free);
        
        $this->assertNotNull($result);
        $this->assertEquals('header', $result->zone);
        $this->assertTrue($result->enabled);
    }

    public function test_get_ad_configuration_returns_null_for_disabled_config()
    {
        AdConfiguration::create([
            'zone' => 'header',
            'enabled' => false,
            'page_types' => ['home'],
            'user_types' => ['free'],
            'ad_format' => 'responsive',
            'priority' => 1,
        ]);

        $free = User::factory()->create();
        
        $result = $this->adSenseService->getAdConfiguration('header', 'home', $free);
        
        $this->assertNull($result);
    }

    public function test_record_impression_creates_performance_record()
    {
        $request = Request::create('/test', 'POST', [
            'zone' => 'header',
            'page' => '/home',
        ]);
        $request->setUserResolver(function () {
            return User::factory()->create();
        });

        $this->adSenseService->recordImpression('header', '/home', $request);

        $this->assertDatabaseHas('ad_performance', [
            'zone' => 'header',
            'page' => '/home',
            'impressions' => 1,
            'clicks' => 0,
        ]);
    }

    public function test_record_click_creates_performance_record()
    {
        $request = Request::create('/test', 'POST', [
            'zone' => 'header',
            'page' => '/home',
        ]);
        $request->setUserResolver(function () {
            return User::factory()->create();
        });

        $this->adSenseService->recordClick('header', '/home', $request);

        $this->assertDatabaseHas('ad_performance', [
            'zone' => 'header',
            'page' => '/home',
            'impressions' => 0,
            'clicks' => 1,
        ]);
    }

    public function test_get_analytics_returns_correct_structure()
    {
        $analytics = $this->adSenseService->getAnalytics();

        $this->assertArrayHasKey('summary', $analytics);
        $this->assertArrayHasKey('trends', $analytics);
        $this->assertArrayHasKey('period', $analytics);

        // Check summary structure
        $this->assertArrayHasKey('total_impressions', $analytics['summary']);
        $this->assertArrayHasKey('total_clicks', $analytics['summary']);
        $this->assertArrayHasKey('total_revenue', $analytics['summary']);
        $this->assertArrayHasKey('avg_ctr', $analytics['summary']);
        $this->assertArrayHasKey('avg_cpm', $analytics['summary']);
        $this->assertArrayHasKey('days_active', $analytics['summary']);
    }

    public function test_get_zone_comparison_returns_correct_structure()
    {
        $comparison = $this->adSenseService->getZoneComparison();

        $this->assertIsArray($comparison);
        $this->assertCount(5, $comparison);

        $expectedZones = ['header', 'sidebar', 'content', 'footer', 'mobile'];
        foreach ($expectedZones as $zone) {
            $this->assertArrayHasKey($zone, $comparison);
            $this->assertArrayHasKey('total_impressions', $comparison[$zone]);
            $this->assertArrayHasKey('total_clicks', $comparison[$zone]);
            $this->assertArrayHasKey('total_revenue', $comparison[$zone]);
        }
    }

    public function test_initialize_defaults_creates_default_configurations()
    {
        $this->assertEquals(0, AdConfiguration::count());

        $this->adSenseService->initializeDefaults();

        $this->assertGreaterThan(0, AdConfiguration::count());
        
        // Check that all default zones are created
        $zones = ['header', 'sidebar', 'content', 'footer', 'mobile'];
        foreach ($zones as $zone) {
            $this->assertDatabaseHas('ad_configurations', ['zone' => $zone]);
        }
    }

    public function test_update_configuration_updates_existing_config()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'ad_format' => 'responsive',
        ]);

        $updatedConfig = $this->adSenseService->updateConfiguration('header', [
            'enabled' => false,
            'ad_format' => 'fixed',
        ]);

        $this->assertFalse($updatedConfig->enabled);
        $this->assertEquals('fixed', $updatedConfig->ad_format);
    }

    public function test_toggle_zone_changes_enabled_status()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
        ]);

        $this->adSenseService->toggleZone('header', false);

        $config->refresh();
        $this->assertFalse($config->enabled);

        $this->adSenseService->toggleZone('header', true);

        $config->refresh();
        $this->assertTrue($config->enabled);
    }
}
