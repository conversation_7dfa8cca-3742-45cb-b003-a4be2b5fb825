<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchableSelectApiTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Brand $appleBrand;
    private Brand $samsungBrand;
    private MobileModel $iphone15;
    private MobileModel $galaxyS24;
    private Part $displayPart;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        // Create test brands
        $this->appleBrand = Brand::factory()->create([
            'name' => 'Apple',
            'logo_url' => 'https://example.com/apple-logo.png',
            'is_active' => true,
        ]);

        $this->samsungBrand = Brand::factory()->create([
            'name' => 'Samsung',
            'logo_url' => 'https://example.com/samsung-logo.png',
            'is_active' => true,
        ]);

        // Create test models
        $this->iphone15 = MobileModel::factory()->create([
            'name' => 'iPhone 15 Pro',
            'model_number' => 'A3108',
            'release_year' => 2023,
            'brand_id' => $this->appleBrand->id,
            'is_active' => true,
        ]);

        $this->galaxyS24 = MobileModel::factory()->create([
            'name' => 'Galaxy S24',
            'model_number' => 'SM-S921B',
            'release_year' => 2024,
            'brand_id' => $this->samsungBrand->id,
            'is_active' => true,
        ]);

        // Create test part
        $this->displayPart = Part::factory()->create([
            'name' => 'OLED Display',
        ]);
    }

    public function test_search_brands_returns_all_brands_when_no_query(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands');

        $response->assertStatus(200);
        $response->assertJsonCount(2);
        
        $brands = $response->json();
        $this->assertEquals('Apple', $brands[0]['label']);
        $this->assertEquals('Samsung', $brands[1]['label']);
    }

    public function test_search_brands_filters_by_query(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=app');

        $response->assertStatus(200);
        $response->assertJsonCount(1);
        
        $brands = $response->json();
        $this->assertEquals('Apple', $brands[0]['label']);
        $this->assertEquals($this->appleBrand->id, (int) $brands[0]['value']);
    }

    public function test_search_brands_respects_limit_parameter(): void
    {
        // Create additional brands
        Brand::factory()->count(25)->create(['is_active' => true]);

        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?limit=5');

        $response->assertStatus(200);
        $response->assertJsonCount(5);
    }

    public function test_search_brands_includes_logo_url(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=apple');

        $response->assertStatus(200);
        
        $brands = $response->json();
        $this->assertEquals('https://example.com/apple-logo.png', $brands[0]['image']);
    }

    public function test_search_models_returns_all_models_when_no_query(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models');

        $response->assertStatus(200);
        $response->assertJsonCount(2);
    }

    public function test_search_models_filters_by_query(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=iphone');

        $response->assertStatus(200);
        $response->assertJsonCount(1);
        
        $models = $response->json();
        $this->assertEquals('iPhone 15 Pro', $models[0]['label']);
        $this->assertEquals($this->iphone15->id, (int) $models[0]['value']);
    }

    public function test_search_models_filters_by_brand_id(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?brand_id=' . $this->appleBrand->id);

        $response->assertStatus(200);
        $response->assertJsonCount(1);
        
        $models = $response->json();
        $this->assertEquals('iPhone 15 Pro', $models[0]['label']);
    }

    public function test_search_models_excludes_compatible_models(): void
    {
        // Make iPhone 15 compatible with the display part
        $this->displayPart->models()->attach($this->iphone15->id);

        $response = $this->actingAs($this->user)
            ->get('/api/search/models?exclude_compatible=1&part_id=' . $this->displayPart->id);

        $response->assertStatus(200);
        $response->assertJsonCount(1);
        
        $models = $response->json();
        $this->assertEquals('Galaxy S24', $models[0]['label']);
    }

    public function test_search_models_includes_model_description(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=iphone');

        $response->assertStatus(200);
        
        $models = $response->json();
        $this->assertStringContainsString('A3108', $models[0]['description']);
        $this->assertStringContainsString('2023', $models[0]['description']);
    }

    public function test_search_models_includes_brand_logo(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=iphone');

        $response->assertStatus(200);
        
        $models = $response->json();
        $this->assertEquals('https://example.com/apple-logo.png', $models[0]['image']);
    }

    public function test_search_models_searches_by_model_number(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=A3108');

        $response->assertStatus(200);
        $response->assertJsonCount(1);
        
        $models = $response->json();
        $this->assertEquals('iPhone 15 Pro', $models[0]['label']);
    }

    public function test_search_models_searches_by_brand_name(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=apple');

        $response->assertStatus(200);
        $response->assertJsonCount(1);
        
        $models = $response->json();
        $this->assertEquals('iPhone 15 Pro', $models[0]['label']);
    }

    public function test_search_brands_requires_authentication(): void
    {
        $response = $this->get('/api/search/brands');
        $response->assertRedirect('/login');
    }

    public function test_search_models_requires_authentication(): void
    {
        $response = $this->get('/api/search/models');
        $response->assertRedirect('/login');
    }

    public function test_search_brands_only_returns_active_brands(): void
    {
        // Create inactive brand
        Brand::factory()->create([
            'name' => 'Inactive Brand',
            'is_active' => false,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/api/search/brands');

        $response->assertStatus(200);
        $response->assertJsonCount(2); // Only Apple and Samsung (active brands)
    }

    public function test_search_models_only_returns_active_models(): void
    {
        // Create inactive model
        MobileModel::factory()->create([
            'name' => 'Inactive Model',
            'brand_id' => $this->appleBrand->id,
            'is_active' => false,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/api/search/models');

        $response->assertStatus(200);
        $response->assertJsonCount(2); // Only iPhone 15 and Galaxy S24 (active models)
    }

    public function test_search_brands_respects_max_limit(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?limit=100');

        $response->assertStatus(200);
        // Should be limited to 50 even though we requested 100
        $this->assertLessThanOrEqual(50, count($response->json()));
    }

    public function test_search_models_respects_max_limit(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?limit=100');

        $response->assertStatus(200);
        // Should be limited to 50 even though we requested 100
        $this->assertLessThanOrEqual(50, count($response->json()));
    }
}
