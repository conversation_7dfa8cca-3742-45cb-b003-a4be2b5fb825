<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class NoHardCodedEmailDependencyTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that users with previously hard-coded admin emails are not automatically admin.
     */
    public function test_hard_coded_admin_emails_are_not_automatically_admin()
    {
        $hardCodedEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($hardCodedEmails as $email) {
            $user = User::factory()->create([
                'email' => $email,
                'role' => 'user', // Explicitly set as regular user
                'is_admin' => false,
            ]);

            // Should not be admin despite having a previously hard-coded email
            $this->assertFalse($user->isAdmin(), "User with email {$email} should not be admin");
            $this->assertFalse($user->canManageContent(), "User with email {$email} should not be able to manage content");
            $this->assertFalse($user->canAccessAdminDashboard(), "User with email {$email} should not be able to access admin dashboard");
            $this->assertEquals('user', $user->getRole(), "User with email {$email} should have 'user' role");
        }
    }

    /**
     * Test that new user registration does not create admin users based on email.
     */
    public function test_new_user_registration_does_not_create_admin_based_on_email()
    {
        $hardCodedEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($hardCodedEmails as $email) {
            // Logout any existing user and clear session
            $this->post('/logout');

            $response = $this->postWithCsrf('/register', [
                'name' => 'Test User',
                'email' => $email,
                'password' => 'password123',
                'password_confirmation' => 'password123',
            ]);



            $user = User::where('email', $email)->first();
            $this->assertNotNull($user, "User should be created for email: {$email}");

            // Should be regular user regardless of email
            $this->assertFalse($user->isAdmin(), "Registered user with email {$email} should not be admin");
            $this->assertEquals('user', $user->getRole(), "Registered user with email {$email} should have 'user' role");
            $this->assertFalse($user->is_admin, "Registered user with email {$email} should have is_admin = false");

            // Logout after each test to clean up
            $this->post('/logout');
        }
    }

    /**
     * Test that admin status is only determined by database fields.
     */
    public function test_admin_status_determined_by_database_fields_only()
    {
        // Create user with hard-coded admin email but regular user role
        $regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'user',
            'is_admin' => false,
        ]);

        // Should not be admin
        $this->assertFalse($regularUser->isAdmin());
        $this->assertEquals('user', $regularUser->getRole());

        // Create user with non-admin email but admin role
        $adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'admin',
            'is_admin' => true,
        ]);

        // Should be admin
        $this->assertTrue($adminUser->isAdmin());
        $this->assertEquals('admin', $adminUser->getRole());

        // Create content manager with hard-coded email
        $contentManager = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'content_manager',
            'is_admin' => false,
        ]);

        // Should be content manager, not admin
        $this->assertFalse($contentManager->isAdmin());
        $this->assertTrue($contentManager->isContentManager());
        $this->assertTrue($contentManager->canManageContent());
        $this->assertEquals('content_manager', $contentManager->getRole());
    }

    /**
     * Test that login redirects are based on role, not email.
     */
    public function test_login_redirects_based_on_role_not_email()
    {
        // User with admin email but regular role
        $regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'user',
            'is_admin' => false,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Should redirect to regular dashboard, not admin dashboard
        $response->assertRedirect('/dashboard');

        // Logout the first user
        $this->post('/logout');

        // User with regular email but admin role
        $adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Should redirect to admin dashboard
        $response->assertRedirect('/admin/dashboard');
    }

    /**
     * Test that content manager with hard-coded email gets proper access.
     */
    public function test_content_manager_with_hard_coded_email_gets_proper_access()
    {
        $contentManager = User::factory()->create([
            'email' => '<EMAIL>', // Previously hard-coded admin email
            'password' => Hash::make('password123'),
            'role' => 'content_manager',
            'is_admin' => false,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Login should redirect to admin dashboard
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertRedirect('/admin/dashboard');

        // Should have content management access
        $this->actingAs($contentManager);
        $response = $this->get('/admin/dashboard');
        $response->assertStatus(200);

        // Should not have admin-only access
        $response = $this->get('/admin/users');
        $response->assertStatus(403);
    }
}
