<?php

namespace Tests\Feature\Admin;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MenuToastDuplicationTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private Menu $menu;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user with proper privileges
        $this->admin = User::factory()->admin()->create();
        
        // Create a test menu
        $this->menu = Menu::factory()->create([
            'name' => 'Test Menu',
            'location' => 'header',
        ]);
    }

    /** @test */
    public function menu_item_creation_does_not_return_flash_message()
    {
        $this->actingAs($this->admin);

        $response = $this->post("/admin/menus/{$this->menu->id}/items", [
            'title' => 'Test Item',
            'type' => 'custom',
            'url' => 'https://example.com',
            'target' => '_self',
            'is_active' => true,
        ]);

        $response->assertRedirect();
        
        // Verify no flash message is set to prevent duplicate toasts
        $this->assertNull(session('success'));
        $this->assertNull(session('error'));
        $this->assertNull(session('message'));
    }

    /** @test */
    public function menu_item_update_does_not_return_flash_message()
    {
        $this->actingAs($this->admin);

        // Create a menu item first
        $menuItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Original Title',
            'type' => 'custom',
            'url' => 'https://example.com',
        ]);

        $response = $this->put("/admin/menus/{$this->menu->id}/items/{$menuItem->id}", [
            'title' => 'Updated Title',
            'type' => 'custom',
            'url' => 'https://updated.com',
            'target' => '_self',
            'is_active' => true,
        ]);

        $response->assertRedirect();
        
        // Verify no flash message is set to prevent duplicate toasts
        $this->assertNull(session('success'));
        $this->assertNull(session('error'));
        $this->assertNull(session('message'));
    }

    /** @test */
    public function menu_item_deletion_does_not_return_flash_message()
    {
        $this->actingAs($this->admin);

        // Create a menu item first
        $menuItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Test Item',
            'type' => 'custom',
            'url' => 'https://example.com',
        ]);

        $response = $this->delete("/admin/menus/{$this->menu->id}/items/{$menuItem->id}");

        $response->assertRedirect();
        
        // Verify no flash message is set to prevent duplicate toasts
        $this->assertNull(session('success'));
        $this->assertNull(session('error'));
        $this->assertNull(session('message'));
    }

    /** @test */
    public function menu_order_update_does_not_return_flash_message_for_redirects()
    {
        $this->actingAs($this->admin);

        // Create menu items
        $item1 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'order' => 1,
        ]);
        $item2 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'order' => 2,
        ]);

        // Test non-AJAX request (should not return flash message)
        $response = $this->post("/admin/menus/{$this->menu->id}/order", [
            'items' => [
                ['id' => $item2->id, 'order' => 1, 'parent_id' => null],
                ['id' => $item1->id, 'order' => 2, 'parent_id' => null],
            ],
        ]);

        $response->assertRedirect();
        
        // Verify no flash message is set to prevent duplicate toasts
        $this->assertNull(session('success'));
        $this->assertNull(session('error'));
        $this->assertNull(session('message'));
    }

    /** @test */
    public function menu_order_update_returns_json_for_ajax_requests()
    {
        $this->actingAs($this->admin);

        // Create menu items
        $item1 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'order' => 1,
        ]);
        $item2 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'order' => 2,
        ]);

        // Test AJAX request (should return JSON with message)
        $response = $this->postJson("/admin/menus/{$this->menu->id}/order", [
            'items' => [
                ['id' => $item2->id, 'order' => 1, 'parent_id' => null],
                ['id' => $item1->id, 'order' => 2, 'parent_id' => null],
            ],
        ]);

        $response->assertOk()
                ->assertJson([
                    'message' => 'Menu order updated successfully.',
                    'success' => true,
                ]);
    }
}
