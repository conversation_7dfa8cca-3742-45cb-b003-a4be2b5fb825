<?php

namespace Tests\Feature\Admin;

use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SearchConfigurationControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved',
            'is_admin' => true,
            'role' => 'admin',
        ]);
        
        // Clear cache before each test
        Cache::flush();
        
        // Initialize default configurations
        SearchConfiguration::initializeDefaults();
    }

    public function test_admin_can_access_search_configuration_index(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->get('/admin/search-config');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/SearchConfiguration/Index')
                ->has('configurations')
                ->has('statistics')
        );
    }

    public function test_non_admin_cannot_access_search_configuration(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>', // Not an admin email
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($user)
            ->get('/admin/search-config');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_search_configuration(): void
    {
        $response = $this->get('/admin/search-config');

        $response->assertRedirect('/login');
    }

    public function test_admin_can_update_search_configurations(): void
    {
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 5,
                'type' => 'integer'
            ],
            [
                'key' => 'guest_search_reset_hours',
                'value' => 12,
                'type' => 'integer'
            ],
            [
                'key' => 'enable_partial_results',
                'value' => false,
                'type' => 'boolean'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify configurations were updated
        $this->assertEquals(5, SearchConfiguration::get('guest_search_limit'));
        $this->assertEquals(12, SearchConfiguration::get('guest_search_reset_hours'));
        $this->assertFalse(SearchConfiguration::get('enable_partial_results'));
    }

    public function test_update_validates_required_configurations_array(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', []);

        $response->assertSessionHasErrors(['configurations']);
    }

    public function test_update_validates_configuration_structure(): void
    {
        $invalidConfigurations = [
            [
                'key' => 'guest_search_limit',
                // Missing value and type
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $invalidConfigurations
            ]);

        $response->assertSessionHasErrors();
    }

    public function test_update_validates_guest_search_limit_minimum(): void
    {
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 0,
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Guest search limit must be at least 1');
    }

    public function test_update_validates_guest_max_visible_results_minimum(): void
    {
        $configurations = [
            [
                'key' => 'guest_max_visible_results',
                'value' => 0,
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Maximum visible results must be at least 1');
    }

    public function test_update_validates_search_reset_hours_range(): void
    {
        $configurations = [
            [
                'key' => 'search_reset_hours',
                'value' => 200, // Over 168 hours (1 week)
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Search reset hours must be between 1 and 168 (1 week)');
    }

    public function test_update_validates_blur_intensity_values(): void
    {
        $configurations = [
            [
                'key' => 'blur_intensity',
                'value' => 'invalid',
                'type' => 'string'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Blur intensity must be light, medium, or heavy');
    }

    public function test_admin_can_reset_configurations_to_defaults(): void
    {
        // First, modify some configurations
        SearchConfiguration::set('guest_search_limit', 10, 'integer');
        SearchConfiguration::set('enable_partial_results', false, 'boolean');

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/reset');

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify configurations were reset to defaults
        $this->assertEquals(3, SearchConfiguration::get('guest_search_limit'));
        $this->assertTrue(SearchConfiguration::get('enable_partial_results'));
    }

    public function test_admin_can_get_configuration_status(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->get('/admin/search-config/status');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'configurations',
            'statistics'
        ]);
    }

    public function test_admin_can_test_configuration_changes(): void
    {
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 5,
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/test', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('test_results');
    }

    public function test_configuration_changes_clear_cache(): void
    {
        // Set a configuration to populate cache
        $initialValue = SearchConfiguration::get('guest_search_limit');
        $this->assertEquals(3, $initialValue);

        // Update configuration
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 7,
                'type' => 'integer'
            ]
        ];

        $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        // Verify cache was cleared and new value is returned
        $newValue = SearchConfiguration::get('guest_search_limit');
        $this->assertEquals(7, $newValue);
    }

    public function test_configuration_index_returns_grouped_configurations(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->get('/admin/search-config');

        $response->assertInertia(fn ($page) =>
            $page->has('configurations.guest_limits')
                ->has('configurations.display')
                ->has('configurations.tracking')
        );
    }

    public function test_configuration_statistics_are_included(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->get('/admin/search-config');

        $response->assertInertia(fn ($page) => 
            $page->has('statistics.guest_searches')
                ->has('statistics.current_configs')
                ->has('statistics.impact_metrics')
        );
    }

    public function test_rate_limiting_is_applied_to_update_endpoint(): void
    {
        // Enable rate limiting for testing
        Cache::put('admin_rate_limiting_enabled', true);

        // Set very low rate limits for testing
        Cache::put('admin_rate_limit_configs', [
            'default' => [
                'max_attempts' => 60,
                'decay_seconds' => 60,
                'description' => 'General admin actions'
            ],
            'system_config' => [
                'max_attempts' => 3, // Very low limit for testing
                'decay_seconds' => 60,
                'description' => 'System configuration changes'
            ]
        ]);

        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 5,
                'type' => 'integer'
            ]
        ];

        $responses = [];

        // Make multiple rapid requests to trigger rate limiting
        for ($i = 0; $i < 5; $i++) {
            $response = $this->actingAs($this->adminUser)
                ->post('/admin/search-config/update', [
                    'configurations' => $configurations
                ]);
            $responses[] = $response;
        }

        // At least one of the later requests should be rate limited
        $rateLimitedCount = collect($responses)->filter(function ($response) {
            return $response->getStatusCode() === 429;
        })->count();

        $this->assertGreaterThan(0, $rateLimitedCount, 'Expected at least one request to be rate limited');

        // Clean up
        Cache::forget('admin_rate_limiting_enabled');
        Cache::forget('admin_rate_limit_configs');
    }

    public function test_boolean_configurations_are_properly_handled(): void
    {
        $configurations = [
            [
                'key' => 'enable_partial_results',
                'value' => true,
                'type' => 'boolean'
            ],
            [
                'key' => 'track_guest_searches',
                'value' => false,
                'type' => 'boolean'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertTrue(SearchConfiguration::get('enable_partial_results'));
        $this->assertFalse(SearchConfiguration::get('track_guest_searches'));
    }

    public function test_string_configurations_are_properly_handled(): void
    {
        $configurations = [
            [
                'key' => 'blur_intensity',
                'value' => 'heavy',
                'type' => 'string'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertEquals('heavy', SearchConfiguration::get('blur_intensity'));
    }
}
