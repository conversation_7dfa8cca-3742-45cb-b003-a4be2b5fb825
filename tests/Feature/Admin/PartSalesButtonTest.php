<?php

namespace Tests\Feature\Admin;

use App\Models\Category;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartSalesButtonTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected Category $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);

        // Create test category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
        ]);
    }

    public function test_can_create_part_with_sales_button_fields()
    {
        $this->actingAs($this->admin);

        $partData = [
            'category_id' => $this->category->id,
            'name' => 'Test Part with Sales Button',
            'part_number' => 'TSB-001',
            'manufacturer' => 'Test Manufacturer',
            'description' => 'A test part with sales button configuration',
            'specifications' => ['color' => 'black', 'size' => 'medium'],
            'images' => [],
            'is_active' => true,
            'sales_button_name' => 'Buy Now',
            'sales_button_url' => 'https://example.com/buy-now',
        ];

        $response = $this->post('/admin/parts', $partData);

        $response->assertRedirect('/admin/parts');
        $response->assertSessionHas('success', 'Part created successfully.');

        $this->assertDatabaseHas('parts', [
            'name' => 'Test Part with Sales Button',
            'sales_button_name' => 'Buy Now',
            'sales_button_url' => 'https://example.com/buy-now',
        ]);
    }

    public function test_can_create_part_without_sales_button_fields()
    {
        $this->actingAs($this->admin);

        $partData = [
            'category_id' => $this->category->id,
            'name' => 'Test Part without Sales Button',
            'part_number' => 'TSB-002',
            'manufacturer' => 'Test Manufacturer',
            'description' => 'A test part without sales button configuration',
            'specifications' => ['color' => 'white'],
            'images' => [],
            'is_active' => true,
            'sales_button_name' => '',
            'sales_button_url' => '',
        ];

        $response = $this->post('/admin/parts', $partData);

        $response->assertRedirect('/admin/parts');
        $response->assertSessionHas('success', 'Part created successfully.');

        $this->assertDatabaseHas('parts', [
            'name' => 'Test Part without Sales Button',
            'sales_button_name' => null,
            'sales_button_url' => null,
        ]);
    }

    public function test_sales_button_url_validation_requires_valid_url()
    {
        $this->actingAs($this->admin);

        $partData = [
            'category_id' => $this->category->id,
            'name' => 'Test Part',
            'sales_button_name' => 'Buy Now',
            'sales_button_url' => 'invalid-url',
        ];

        $response = $this->post('/admin/parts', $partData);

        $response->assertSessionHasErrors(['sales_button_url']);
    }

    public function test_sales_button_name_validation_respects_max_length()
    {
        $this->actingAs($this->admin);

        $longName = str_repeat('a', 256); // Exceeds 255 character limit

        $partData = [
            'category_id' => $this->category->id,
            'name' => 'Test Part',
            'sales_button_name' => $longName,
            'sales_button_url' => 'https://example.com',
        ];

        $response = $this->post('/admin/parts', $partData);

        $response->assertSessionHasErrors(['sales_button_name']);
    }

    public function test_sales_button_url_validation_respects_max_length()
    {
        $this->actingAs($this->admin);

        $longUrl = 'https://example.com/' . str_repeat('a', 500); // Exceeds 500 character limit

        $partData = [
            'category_id' => $this->category->id,
            'name' => 'Test Part',
            'sales_button_name' => 'Buy Now',
            'sales_button_url' => $longUrl,
        ];

        $response = $this->post('/admin/parts', $partData);

        $response->assertSessionHasErrors(['sales_button_url']);
    }

    public function test_can_update_part_with_sales_button_fields()
    {
        $this->actingAs($this->admin);

        $part = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Original Part',
            'sales_button_name' => null,
            'sales_button_url' => null,
        ]);

        $updateData = [
            'category_id' => $this->category->id,
            'name' => 'Updated Part',
            'part_number' => $part->part_number,
            'manufacturer' => $part->manufacturer,
            'description' => $part->description,
            'specifications' => $part->specifications ?? [],
            'images' => $part->images ?? [],
            'is_active' => $part->is_active,
            'sales_button_name' => 'Shop Here',
            'sales_button_url' => 'https://shop.example.com/product',
        ];

        $response = $this->put("/admin/parts/{$part->id}", $updateData);

        $response->assertRedirect('/admin/parts');
        $response->assertSessionHas('success', 'Part updated successfully.');

        $this->assertDatabaseHas('parts', [
            'id' => $part->id,
            'name' => 'Updated Part',
            'sales_button_name' => 'Shop Here',
            'sales_button_url' => 'https://shop.example.com/product',
        ]);
    }

    public function test_can_remove_sales_button_fields_from_existing_part()
    {
        $this->actingAs($this->admin);

        $part = Part::factory()->create([
            'category_id' => $this->category->id,
            'sales_button_name' => 'Old Button',
            'sales_button_url' => 'https://old.example.com',
        ]);

        $updateData = [
            'category_id' => $this->category->id,
            'name' => $part->name,
            'part_number' => $part->part_number,
            'manufacturer' => $part->manufacturer,
            'description' => $part->description,
            'specifications' => $part->specifications ?? [],
            'images' => $part->images ?? [],
            'is_active' => $part->is_active,
            'sales_button_name' => '',
            'sales_button_url' => '',
        ];

        $response = $this->put("/admin/parts/{$part->id}", $updateData);

        $response->assertRedirect('/admin/parts');

        $this->assertDatabaseHas('parts', [
            'id' => $part->id,
            'sales_button_name' => null,
            'sales_button_url' => null,
        ]);
    }

    public function test_sales_button_fields_are_nullable()
    {
        $this->actingAs($this->admin);

        $partData = [
            'category_id' => $this->category->id,
            'name' => 'Test Part',
            'is_active' => true,
            // Omitting sales_button_name and sales_button_url
        ];

        $response = $this->post('/admin/parts', $partData);

        $response->assertRedirect('/admin/parts');
        $response->assertSessionHas('success', 'Part created successfully.');
    }
}
