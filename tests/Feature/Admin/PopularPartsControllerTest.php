<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\Part;
use App\Models\Category;
use App\Models\UserFavorite;
use App\Models\UserSearch;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PopularPartsControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $user;
    private Category $category;
    private Part $part1;
    private Part $part2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'is_admin' => true,
        ]);

        // Create regular user
        $this->user = User::factory()->create();

        // Create category
        $this->category = Category::factory()->create([
            'name' => 'Display Components',
        ]);

        // Create parts
        $this->part1 = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'iPhone 14 LCD Screen',
            'manufacturer' => 'Apple',
            'part_number' => 'IP14-LCD-001',
        ]);

        $this->part2 = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Samsung Galaxy S23 OLED',
            'manufacturer' => 'Samsung',
            'part_number' => 'SGS23-OLED-001',
        ]);
    }

    /** @test */
    public function admin_can_access_popular_parts_analytics()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/PopularParts/Index')
                ->has('analytics')
                ->has('days')
        );
    }

    /** @test */
    public function non_admin_cannot_access_popular_parts_analytics()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('admin.popular-parts.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function guest_cannot_access_popular_parts_analytics()
    {
        $response = $this->get(route('admin.popular-parts.index'));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function analytics_includes_overview_statistics()
    {
        // Create some favorites with different users to avoid unique constraint violation
        UserFavorite::factory()->count(5)->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
        ]);

        UserFavorite::factory()->count(3)->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part2->id,
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.overview.total_favorited_parts')
                ->has('analytics.overview.total_favorites')
                ->has('analytics.overview.unique_users')
                ->has('analytics.overview.avg_favorites_per_part')
        );
    }

    /** @test */
    public function analytics_includes_top_parts_data()
    {
        // Create favorites for part1 (more popular)
        UserFavorite::factory()->count(5)->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
        ]);

        // Create favorites for part2 (less popular)
        UserFavorite::factory()->count(2)->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part2->id,
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.top_parts')
                ->where('analytics.top_parts.0.name', $this->part1->name)
                ->where('analytics.top_parts.0.total_favorites', 5)
        );
    }

    /** @test */
    public function analytics_includes_category_breakdown()
    {
        // Create favorites
        UserFavorite::factory()->count(3)->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.category_breakdown')
                ->where('analytics.category_breakdown.0.name', $this->category->name)
        );
    }

    /** @test */
    public function analytics_respects_days_parameter()
    {
        // Create old favorites (outside the time range)
        UserFavorite::factory()->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
            'created_at' => now()->subDays(40),
        ]);

        // Create recent favorites (within the time range)
        UserFavorite::factory()->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
            'created_at' => now()->subDays(10),
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.index', ['days' => 30]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->where('days', 30)
                ->has('analytics.overview.recent_favorites')
        );
    }

    /** @test */
    public function admin_can_export_popular_parts_data()
    {
        // Create some test data
        UserFavorite::factory()->count(3)->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.export'));

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
        $response->assertHeader('Content-Disposition', 'attachment; filename="popular_parts_analytics_' . now()->format('Y-m-d') . '.csv"');
    }

    /** @test */
    public function export_includes_correct_csv_headers()
    {
        UserFavorite::factory()->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.export'));

        $content = $response->getContent();
        $this->assertStringContainsString('Part Name', $content);
        $this->assertStringContainsString('Category', $content);
        $this->assertStringContainsString('Total Favorites', $content);
        $this->assertStringContainsString($this->part1->name, $content);
    }

    /** @test */
    public function trending_parts_only_includes_parts_with_recent_activity()
    {
        // Create old favorites (should not be trending)
        UserFavorite::factory()->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
            'created_at' => now()->subDays(40),
        ]);

        // Create recent favorites (should be trending)
        UserFavorite::factory()->count(3)->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part2->id,
            'created_at' => now()->subDays(5),
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.index', ['days' => 30]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.trending_parts')
                ->where('analytics.trending_parts.0.name', $this->part2->name)
        );
    }

    /** @test */
    public function search_correlation_includes_search_data()
    {
        // Create favorites
        UserFavorite::factory()->count(5)->create([
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
        ]);

        // Create search data
        UserSearch::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'search_query' => $this->part1->name,
            'search_type' => 'all',
            'results_count' => 1,
            'created_at' => now()->subDays(5),
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.popular-parts.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.search_correlation')
        );
    }
}
