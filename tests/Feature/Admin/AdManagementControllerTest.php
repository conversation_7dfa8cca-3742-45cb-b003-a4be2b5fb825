<?php

namespace Tests\Feature\Admin;

use App\Models\AdConfiguration;
use App\Models\AdPerformance;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdManagementControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
            'role' => 'admin',
        ]);

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => false,
        ]);
    }

    public function test_admin_can_access_ad_management_index()
    {
        $response = $this->actingAs($this->admin)->get('/admin/ads');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/AdManagement/index')
                ->has('configurations')
                ->has('analytics')
                ->has('filters')
        );
    }

    public function test_non_admin_cannot_access_ad_management_index()
    {
        $response = $this->actingAs($this->user)->get('/admin/ads');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_ad_management_index()
    {
        $response = $this->get('/admin/ads');

        $response->assertRedirect(route('login'));
    }

    public function test_admin_can_access_create_ad_configuration()
    {
        $response = $this->actingAs($this->admin)->get('/admin/ads/create');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/AdManagement/create')
                ->has('zones')
                ->has('pageTypes')
                ->has('userTypes')
                ->has('adFormats')
        );
    }

    public function test_admin_can_create_ad_configuration()
    {
        $data = [
            'zone' => 'header',
            'enabled' => true,
            'page_types' => ['home', 'search'],
            'user_types' => ['free', 'guest'],
            'ad_slot_id' => '1234567890',
            'ad_format' => 'responsive',
            'priority' => 1,
            'frequency_rules' => [
                'max_ads_per_page' => 3,
                'delay_seconds' => 2,
            ],
            'targeting_rules' => [
                'device_type' => 'desktop',
            ],
        ];

        $response = $this->actingAs($this->admin)->postWithCsrf('/admin/ads', $data);

        $response->assertRedirect('/admin/ads');
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('ad_configurations', [
            'zone' => 'header',
            'enabled' => true,
            'ad_slot_id' => '1234567890',
            'ad_format' => 'responsive',
        ]);
    }

    public function test_admin_can_access_edit_ad_configuration()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'ad_format' => 'responsive',
            'page_types' => ['home'],
            'user_types' => ['free'],
        ]);

        $response = $this->actingAs($this->admin)->get("/admin/ads/{$config->id}/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/AdManagement/edit')
                ->has('configuration')
                ->has('zones')
                ->has('pageTypes')
                ->has('userTypes')
                ->has('adFormats')
        );
    }

    public function test_admin_can_update_ad_configuration()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'ad_format' => 'responsive',
        ]);

        $updateData = [
            'zone' => 'header',
            'enabled' => false,
            'ad_format' => 'fixed',
            'page_types' => ['home'],
            'user_types' => ['free'],
        ];

        $response = $this->actingAs($this->admin)->putWithCsrf("/admin/ads/{$config->id}", $updateData);

        $response->assertRedirect('/admin/ads');
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('ad_configurations', [
            'id' => $config->id,
            'enabled' => false,
            'ad_format' => 'fixed',
        ]);
    }

    public function test_admin_can_delete_ad_configuration()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'ad_format' => 'responsive',
        ]);

        $response = $this->actingAs($this->admin)->deleteWithCsrf("/admin/ads/{$config->id}");

        $response->assertRedirect('/admin/ads');
        $response->assertSessionHas('success');

        $this->assertDatabaseMissing('ad_configurations', [
            'id' => $config->id,
        ]);
    }

    public function test_admin_can_toggle_ad_configuration()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'ad_format' => 'responsive',
        ]);

        $response = $this->actingAs($this->admin)->postWithCsrf("/admin/ads/{$config->id}/toggle");

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'enabled' => false,
            'message' => 'Ad zone disabled successfully.',
        ]);

        $config->refresh();
        $this->assertFalse($config->enabled);
    }

    public function test_admin_can_initialize_default_configurations()
    {
        $this->assertEquals(0, AdConfiguration::count());

        $response = $this->actingAs($this->admin)->postWithCsrf('/admin/ads/initialize-defaults');

        $response->assertRedirect('/admin/ads');
        $response->assertSessionHas('success');

        $this->assertGreaterThan(0, AdConfiguration::count());

        // Check that default zones are created
        $zones = ['header', 'sidebar', 'content', 'footer', 'mobile'];
        foreach ($zones as $zone) {
            $this->assertDatabaseHas('ad_configurations', ['zone' => $zone]);
        }
    }

    public function test_create_ad_configuration_validates_required_fields()
    {
        $response = $this->actingAs($this->admin)->postWithCsrf('/admin/ads', []);

        $response->assertSessionHasErrors(['zone', 'ad_format']);
    }

    public function test_create_ad_configuration_validates_zone_values()
    {
        $data = [
            'zone' => 'invalid_zone',
            'ad_format' => 'responsive',
        ];

        $response = $this->actingAs($this->admin)->postWithCsrf('/admin/ads', $data);

        $response->assertSessionHasErrors(['zone']);
    }

    public function test_create_ad_configuration_validates_ad_format_values()
    {
        $data = [
            'zone' => 'header',
            'ad_format' => 'invalid_format',
        ];

        $response = $this->actingAs($this->admin)->postWithCsrf('/admin/ads', $data);

        $response->assertSessionHasErrors(['ad_format']);
    }

    public function test_non_admin_cannot_create_ad_configuration()
    {
        $data = [
            'zone' => 'header',
            'ad_format' => 'responsive',
        ];

        $response = $this->actingAs($this->user)->postWithCsrf('/admin/ads', $data);

        $response->assertStatus(403);
    }

    public function test_non_admin_cannot_update_ad_configuration()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'ad_format' => 'responsive',
        ]);

        $response = $this->actingAs($this->user)->putWithCsrf("/admin/ads/{$config->id}", [
            'zone' => 'header',
            'ad_format' => 'fixed',
        ]);

        $response->assertStatus(403);
    }

    public function test_non_admin_cannot_delete_ad_configuration()
    {
        $config = AdConfiguration::create([
            'zone' => 'header',
            'enabled' => true,
            'ad_format' => 'responsive',
        ]);

        $response = $this->actingAs($this->user)->deleteWithCsrf("/admin/ads/{$config->id}");

        $response->assertStatus(403);
    }

    public function test_index_includes_analytics_data()
    {
        // Create test performance data
        AdPerformance::create([
            'zone' => 'header',
            'page' => '/home',
            'user_type' => 'free',
            'device_type' => 'desktop',
            'impressions' => 100,
            'clicks' => 5,
            'revenue' => 2.50,
            'date' => now()->toDateString(),
        ]);

        $response = $this->actingAs($this->admin)->get('/admin/ads');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.summary')
                ->has('analytics.trends')
        );
    }
}
