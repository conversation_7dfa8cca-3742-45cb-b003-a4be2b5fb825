<?php

namespace Tests\Feature\Admin;

use App\Models\Page;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PageManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>', // Admin email from User::isAdmin() method
            'email_verified_at' => now(),
        ]);

        // Create regular user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
        ]);
    }

    public function test_admin_can_access_pages_index()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/admin/pages');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Pages/Index')
                ->has('pages')
                ->has('filters')
                ->has('layouts')
        );
    }

    public function test_non_admin_cannot_access_pages_index()
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/pages');

        $response->assertStatus(403);
    }

    public function test_admin_can_access_create_page()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/admin/pages/create');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Pages/Create')
                ->has('layouts')
        );
    }

    public function test_admin_can_create_page()
    {
        $this->actingAs($this->admin);

        $pageData = [
            'title' => 'Test Page',
            'slug' => 'test-page',
            'content' => '<p>This is test content</p>',
            'meta_description' => 'Test page description',
            'meta_keywords' => 'test, page, keywords',
            'layout' => 'default',
            'is_published' => true,
            'published_at' => now()->format('Y-m-d\TH:i'),
        ];

        $response = $this->post('/admin/pages', $pageData);

        $response->assertRedirect('/admin/pages');
        $response->assertSessionHas('success', 'Page created successfully.');

        $this->assertDatabaseHas('pages', [
            'title' => 'Test Page',
            'slug' => 'test-page',
            'content' => '<p>This is test content</p>',
            'meta_description' => 'Test page description',
            'meta_keywords' => 'test, page, keywords',
            'layout' => 'default',
            'is_published' => true,
            'author_id' => $this->admin->id,
        ]);
    }

    public function test_admin_can_create_page_without_slug()
    {
        $this->actingAs($this->admin);

        $pageData = [
            'title' => 'Auto Slug Page',
            'content' => '<p>This page should auto-generate slug</p>',
            'layout' => 'default',
            'is_published' => false,
        ];

        $response = $this->post('/admin/pages', $pageData);

        $response->assertRedirect('/admin/pages');

        $this->assertDatabaseHas('pages', [
            'title' => 'Auto Slug Page',
            'slug' => 'auto-slug-page',
            'author_id' => $this->admin->id,
        ]);
    }

    public function test_page_creation_validates_required_fields()
    {
        $this->actingAs($this->admin);

        $response = $this->post('/admin/pages', []);

        $response->assertSessionHasErrors(['title', 'layout']);
    }

    public function test_page_creation_validates_unique_slug()
    {
        $this->actingAs($this->admin);

        // Create first page
        Page::factory()->create(['slug' => 'duplicate-slug']);

        $pageData = [
            'title' => 'Duplicate Slug Test',
            'slug' => 'duplicate-slug',
            'layout' => 'default',
        ];

        $response = $this->post('/admin/pages', $pageData);

        $response->assertSessionHasErrors(['slug']);
    }

    public function test_admin_can_view_page_edit_form()
    {
        $this->actingAs($this->admin);

        $page = Page::factory()->create(['author_id' => $this->admin->id]);

        $response = $this->get("/admin/pages/{$page->id}/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Pages/Edit')
                ->has('page')
                ->has('layouts')
        );
    }

    public function test_admin_can_update_page()
    {
        $this->actingAs($this->admin);

        $page = Page::factory()->create(['author_id' => $this->admin->id]);

        $updateData = [
            'title' => 'Updated Page Title',
            'slug' => 'updated-page-slug',
            'content' => '<p>Updated content</p>',
            'meta_description' => 'Updated description',
            'layout' => 'full-width',
            'is_published' => true,
        ];

        $response = $this->put("/admin/pages/{$page->id}", $updateData);

        $response->assertRedirect('/admin/pages');
        $response->assertSessionHas('success', 'Page updated successfully.');

        $this->assertDatabaseHas('pages', [
            'id' => $page->id,
            'title' => 'Updated Page Title',
            'slug' => 'updated-page-slug',
            'content' => '<p>Updated content</p>',
            'meta_description' => 'Updated description',
            'layout' => 'full-width',
            'is_published' => true,
        ]);
    }

    public function test_admin_can_delete_page()
    {
        $this->actingAs($this->admin);

        $page = Page::factory()->create(['author_id' => $this->admin->id]);

        $response = $this->delete("/admin/pages/{$page->id}");

        $response->assertRedirect('/admin/pages');
        $response->assertSessionHas('success', 'Page deleted successfully.');

        $this->assertDatabaseMissing('pages', ['id' => $page->id]);
    }

    public function test_published_page_is_publicly_accessible()
    {
        $page = Page::factory()->create([
            'is_published' => true,
            'published_at' => now(),
        ]);

        $response = $this->get("/{$page->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
                ->has('page')
                ->has('seo')
        );
    }

    public function test_unpublished_page_is_not_publicly_accessible()
    {
        $page = Page::factory()->create([
            'is_published' => false,
            'published_at' => null,
        ]);

        $response = $this->get("/page/{$page->slug}");

        $response->assertStatus(404);
    }

    public function test_pages_index_shows_published_pages()
    {
        // Create published pages
        Page::factory()->count(3)->create([
            'is_published' => true,
            'published_at' => now(),
        ]);

        // Create unpublished page
        Page::factory()->create([
            'is_published' => false,
            'published_at' => null,
        ]);

        $response = $this->get('/pages');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/index')
                ->has('pages')
                ->where('pages.data', fn ($pages) => count($pages) === 3)
        );
    }

    public function test_page_filtering_works()
    {
        $this->actingAs($this->admin);

        // Create pages with different statuses
        Page::factory()->create([
            'title' => 'Published Page',
            'is_published' => true,
            'published_at' => now(),
        ]);

        Page::factory()->create([
            'title' => 'Draft Page',
            'is_published' => false,
            'published_at' => null,
        ]);

        // Test published filter
        $response = $this->get('/admin/pages?status=published');
        $response->assertStatus(200);

        // Test draft filter
        $response = $this->get('/admin/pages?status=draft');
        $response->assertStatus(200);

        // Test search filter
        $response = $this->get('/admin/pages?search=Published');
        $response->assertStatus(200);
    }

    public function test_page_slug_generation()
    {
        $this->assertEquals('hello-world', Page::generateSlug('Hello World'));
        $this->assertEquals('test-page-123', Page::generateSlug('Test Page 123'));
        $this->assertEquals('special-chars', Page::generateSlug('Special @#$% Chars!'));
        $this->assertEquals('multiple-spaces', Page::generateSlug('Multiple   Spaces'));
    }

    public function test_page_caching()
    {
        $page = Page::factory()->create([
            'is_published' => true,
            'published_at' => now(),
        ]);

        // First call should cache the page
        $cachedPage = Page::getCachedBySlug($page->slug);
        $this->assertNotNull($cachedPage);
        $this->assertEquals($page->id, $cachedPage->id);

        // Second call should return cached version
        $cachedPageAgain = Page::getCachedBySlug($page->slug);
        $this->assertNotNull($cachedPageAgain);
        $this->assertEquals($page->id, $cachedPageAgain->id);
    }

    public function test_page_url_attribute_is_appended()
    {
        $page = Page::factory()->create([
            'slug' => 'test-page-url',
            'is_published' => true,
            'published_at' => now(),
        ]);

        // Test that the url attribute is included when the model is serialized
        $pageArray = $page->toArray();
        $this->assertArrayHasKey('url', $pageArray);
        $this->assertEquals(route('pages.show', $page->slug), $pageArray['url']);
        $this->assertEquals('http://localhost/test-page-url', $pageArray['url']);
    }

    public function test_page_excerpt_attribute_is_appended()
    {
        // Test with meta_description
        $page = Page::factory()->create([
            'meta_description' => 'This is a test meta description',
            'content' => '<p>This is some content</p>',
        ]);

        $pageArray = $page->toArray();
        $this->assertArrayHasKey('excerpt', $pageArray);
        $this->assertEquals('This is a test meta description', $pageArray['excerpt']);

        // Test without meta_description (should use content)
        $pageWithoutMeta = Page::factory()->create([
            'meta_description' => null,
            'content' => '<p>This is some very long content that should be truncated to 160 characters when used as an excerpt. This content is definitely longer than 160 characters and should be cut off at the appropriate length.</p>',
        ]);

        $pageWithoutMetaArray = $pageWithoutMeta->toArray();
        $this->assertArrayHasKey('excerpt', $pageWithoutMetaArray);
        $this->assertLessThanOrEqual(163, strlen($pageWithoutMetaArray['excerpt'])); // Allow for "..." appended by Str::limit
        $this->assertStringContainsString('This is some very long content', $pageWithoutMetaArray['excerpt']);
    }

    public function test_admin_pages_index_includes_url_attribute()
    {
        $this->actingAs($this->admin);

        $page = Page::factory()->create([
            'slug' => 'test-page-with-url',
            'is_published' => true,
            'published_at' => now(),
        ]);

        $response = $this->get('/admin/pages');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Pages/Index')
                ->has('pages.data.0.url')
                ->where('pages.data.0.url', route('pages.show', 'test-page-with-url'))
        );
    }

    public function test_page_view_button_functionality()
    {
        $page = Page::factory()->create([
            'slug' => 'viewable-test-page',
            'is_published' => true,
            'published_at' => now(),
            'title' => 'Viewable Test Page',
            'content' => '<p>This page should be viewable</p>',
        ]);

        // Test that the public page route works
        $response = $this->get("/{$page->slug}");
        $response->assertStatus(200);
        $response->assertInertia(fn ($inertiaPage) =>
            $inertiaPage->component('pages/show')
                ->where('page.title', 'Viewable Test Page')
                ->where('page.slug', 'viewable-test-page')
        );
    }
}
