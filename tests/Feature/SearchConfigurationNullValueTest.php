<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchConfigurationNullValueTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = $this->createAdminUser([
            'email' => '<EMAIL>',
        ]);
    }

    public function test_update_handles_empty_string_values(): void
    {
        $configurations = [
            [
                'key' => 'watermark_logo_url',
                'value' => '',
                'type' => 'string'
            ],
            [
                'key' => 'watermark_text',
                'value' => 'Test Watermark',
                'type' => 'string'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify the empty string was saved correctly
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'watermark_logo_url',
            'value' => json_encode(''),
        ]);
    }

    public function test_update_handles_null_values_with_defaults(): void
    {
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => null,
                'type' => 'integer'
            ],
            [
                'key' => 'enable_partial_results',
                'value' => null,
                'type' => 'boolean'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();

        $response->assertSessionHas('success');

        // Verify defaults were applied
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'guest_search_limit',
            'value' => json_encode(1), // Default for guest_search_limit (must be at least 1)
        ]);
        
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'enable_partial_results',
            'value' => json_encode(false), // Default for boolean
        ]);
    }

    public function test_update_handles_premium_user_daily_limit_special_case(): void
    {
        $configurations = [
            [
                'key' => 'premium_user_daily_limit',
                'value' => null,
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify special default (-1 for unlimited) was applied
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'premium_user_daily_limit',
            'value' => json_encode(-1),
        ]);
    }

    public function test_update_handles_mixed_valid_and_null_values(): void
    {
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 5,
                'type' => 'integer'
            ],
            [
                'key' => 'watermark_logo_url',
                'value' => '',
                'type' => 'string'
            ],
            [
                'key' => 'enable_partial_results',
                'value' => true,
                'type' => 'boolean'
            ],
            [
                'key' => 'watermark_opacity',
                'value' => null,
                'type' => 'float'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify all configurations were saved with appropriate values
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'guest_search_limit',
            'value' => json_encode(5),
        ]);
        
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'watermark_logo_url',
            'value' => json_encode(''),
        ]);
        
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'enable_partial_results',
            'value' => json_encode(true),
        ]);
        
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'watermark_opacity',
            'value' => json_encode(0.5), // Default for watermark_opacity (must be between 0.1 and 1.0)
        ]);
    }

    public function test_update_processes_configurations_without_errors(): void
    {
        $configurations = [
            [
                'key' => 'watermark_enabled',
                'value' => null,
                'type' => 'boolean'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify the configuration was saved with default value
        $this->assertDatabaseHas('search_configurations', [
            'key' => 'watermark_enabled',
            'value' => json_encode(false), // Default for boolean
        ]);
    }

    public function test_validation_provides_detailed_error_messages(): void
    {
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 'invalid_integer',
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error');
        
        $errorMessage = session('error');
        $this->assertStringContainsString('Invalid integer value for guest_search_limit', $errorMessage);
        $this->assertStringContainsString('Expected numeric value', $errorMessage);
    }
}
