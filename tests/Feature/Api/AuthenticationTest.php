<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test that unauthenticated requests to impersonation status return 401
     */
    public function test_impersonation_status_requires_authentication(): void
    {
        $response = $this->getJson('/api/impersonation/status');

        $response->assertStatus(401);
    }

    /**
     * Test that authenticated requests to impersonation status work
     */
    public function test_impersonation_status_works_when_authenticated(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->getJson('/api/impersonation/status');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'is_impersonating',
                'impersonating_user_id',
                'original_admin_id',
                'expires_at',
                'remaining_minutes'
            ])
            ->assertJson([
                'is_impersonating' => false
            ]);
    }

    /**
     * Test that unauthenticated requests to notification endpoints return 401
     */
    public function test_notification_endpoints_require_authentication(): void
    {
        $unreadCountResponse = $this->getJson('/api/notifications/unread-count');
        $recentResponse = $this->getJson('/api/notifications/recent');

        $unreadCountResponse->assertStatus(401);
        $recentResponse->assertStatus(401);
    }

    /**
     * Test that authenticated requests to notification endpoints work
     */
    public function test_notification_endpoints_work_when_authenticated(): void
    {
        $user = User::factory()->create();

        $unreadCountResponse = $this->actingAs($user)->getJson('/api/notifications/unread-count');
        $recentResponse = $this->actingAs($user)->getJson('/api/notifications/recent');

        $unreadCountResponse->assertStatus(200)
            ->assertJsonStructure(['count']);

        $recentResponse->assertStatus(200)
            ->assertJsonStructure(['notifications']);
    }

    /**
     * Test CSRF protection on API endpoints
     */
    public function test_api_endpoints_handle_csrf_correctly(): void
    {
        $user = User::factory()->create();

        // Test with proper headers
        $response = $this->actingAs($user)
            ->withHeaders([
                'Accept' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest',
            ])
            ->getJson('/api/impersonation/status');

        $response->assertStatus(200);

        // Test notification endpoints with proper headers
        $notificationResponse = $this->actingAs($user)
            ->withHeaders([
                'Accept' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest',
            ])
            ->getJson('/api/notifications/unread-count');

        $notificationResponse->assertStatus(200);
    }

    /**
     * Test that API responses have proper cache headers
     */
    public function test_impersonation_status_has_no_cache_headers(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->getJson('/api/impersonation/status');

        $response->assertStatus(200)
            ->assertHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->assertHeader('Pragma', 'no-cache')
            ->assertHeader('Expires', '0');
    }

    /**
     * Test session-based authentication works correctly
     */
    public function test_session_authentication_works(): void
    {
        $user = User::factory()->create();

        // Simulate login
        $this->post('/login', [
            'email' => $user->email,
            'password' => 'password'
        ]);

        // Test that subsequent API calls work with session
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-Requested-With' => 'XMLHttpRequest',
        ])->getJson('/api/impersonation/status');

        $response->assertStatus(200);
    }

    /**
     * Test that expired sessions are handled gracefully
     */
    public function test_expired_session_handling(): void
    {
        // Test without any authentication
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-Requested-With' => 'XMLHttpRequest',
        ])->getJson('/api/impersonation/status');

        $response->assertStatus(401);
    }

    /**
     * Test concurrent API requests don't interfere with each other
     */
    public function test_concurrent_api_requests(): void
    {
        $user = User::factory()->create();

        // Simulate multiple concurrent requests
        $responses = [];
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->actingAs($user)->getJson('/api/impersonation/status');
        }

        foreach ($responses as $response) {
            $response->assertStatus(200);
        }
    }
}
