<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\PricingPlan;
use App\Models\SearchConfiguration;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PublicModelViewTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected User $subscribedUser;
    protected Brand $brand;
    protected MobileModel $model;
    protected Category $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();
        $this->subscribedUser = User::factory()->create();
        
        $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
            'model_number' => 'TM-001',
            'release_year' => 2023,
        ]);

        // Create parts for the model
        $parts = Part::factory()->count(10)->create([
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        // Associate parts with the model
        $this->model->parts()->attach($parts->pluck('id'));

        // Create subscription for subscribed user
        $plan = PricingPlan::factory()->create(['search_limit' => -1]);
        Subscription::factory()->create([
            'user_id' => $this->subscribedUser->id,
            'pricing_plan_id' => $plan->id,
            'status' => 'active',
            'current_period_end' => now()->addMonth(),
        ]);

        // Set search configuration
        SearchConfiguration::set('guest_max_visible_results', 5);
    }

    /** @test */
    public function guest_can_view_public_model_with_limited_access()
    {
        $response = $this->get("/models/{$this->model->id}");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('public/model-view')
                    ->has('model')
                    ->where('model.name', 'Test Model')
                    ->where('isSubscribed', false)
                    ->where('hasUnlimitedAccess', false)
                    ->where('requiresSignup', true)
                    ->where('maxVisibleParts', 5)
                    ->where('hiddenPartsCount', 5) // 10 total - 5 visible = 5 hidden
                );
    }

    /** @test */
    public function authenticated_user_can_view_public_model_with_limited_access()
    {
        $response = $this->actingAs($this->user)
                        ->get("/models/{$this->model->id}");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('models/show')
                    ->where('isSubscribed', false)
                    ->where('hasUnlimitedAccess', false)
                    ->where('requiresSignup', false)
                    ->where('hiddenPartsCount', 5)
                );
    }

    /** @test */
    public function subscribed_user_can_view_public_model_with_full_access()
    {
        $response = $this->actingAs($this->subscribedUser)
                        ->get("/models/{$this->model->id}");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('models/show')
                    ->where('isSubscribed', true)
                    ->where('hasUnlimitedAccess', true)
                    ->where('hiddenPartsCount', 0)
                );
    }

    /** @test */
    public function admin_user_can_view_public_model_with_full_access()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
            'role' => 'admin',
        ]);

        $response = $this->actingAs($admin)
                        ->get("/models/{$this->model->id}");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('models/show')
                    ->where('hasUnlimitedAccess', true)
                    ->where('hiddenPartsCount', 0)
                );
    }

    /** @test */
    public function guest_cannot_access_brand_search()
    {
        $response = $this->get("/brands/{$this->brand->id}/search");

        $response->assertRedirect('/login');
    }

    /** @test */
    public function authenticated_user_can_access_brand_search()
    {
        $response = $this->actingAs($this->user)
                        ->get("/brands/{$this->brand->id}/search");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('public/brand-search')
                    ->has('brand')
                    ->where('brand.name', 'Test Brand')
                    ->where('isSubscribed', false)
                    ->where('hasUnlimitedAccess', false)
                );
    }

    /** @test */
    public function subscribed_user_can_access_brand_search_with_full_results()
    {
        // Create more models for testing
        MobileModel::factory()->count(15)->create(['brand_id' => $this->brand->id]);

        $response = $this->actingAs($this->subscribedUser)
                        ->get("/brands/{$this->brand->id}/search");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('public/brand-search')
                    ->where('isSubscribed', true)
                    ->where('hasUnlimitedAccess', true)
                    ->where('hiddenModelsCount', 0)
                );
    }

    /** @test */
    public function brand_search_filters_work_correctly()
    {
        // Create models with different years
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Model 2022',
            'release_year' => 2022,
        ]);
        
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Model 2024',
            'release_year' => 2024,
        ]);

        $response = $this->actingAs($this->user)
                        ->get("/brands/{$this->brand->id}/search?release_year=2022");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('public/brand-search')
                    ->where('filters.release_year', '2022')
                );
    }

    /** @test */
    public function brand_search_search_term_works_correctly()
    {
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Special Model',
            'model_number' => 'SP-001',
        ]);

        $response = $this->actingAs($this->user)
                        ->get("/brands/{$this->brand->id}/search?search=Special");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('public/brand-search')
                    ->where('filters.search', 'Special')
                );
    }

    /** @test */
    public function guest_cannot_access_brands_list()
    {
        $response = $this->get('/brands');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function authenticated_user_can_access_brands_list()
    {
        $response = $this->actingAs($this->user)
                        ->get('/brands');

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->component('public/brands-list')
                    ->has('brands')
                    ->where('isSubscribed', false)
                );
    }

    /** @test */
    public function model_view_logs_activity_for_authenticated_users()
    {
        $this->actingAs($this->user)
            ->get("/models/{$this->model->id}");

        $this->assertDatabaseHas('user_activity_logs', [
            'user_id' => $this->user->id,
            'activity_type' => 'public_model_viewed',
            'description' => "User viewed public model: {$this->model->name}",
        ]);
    }

    /** @test */
    public function brand_search_logs_activity_for_authenticated_users()
    {
        $this->actingAs($this->user)
            ->get("/brands/{$this->brand->id}/search");

        $this->assertDatabaseHas('user_activity_logs', [
            'user_id' => $this->user->id,
            'activity_type' => 'brand_search_accessed',
            'description' => "User accessed brand search: {$this->brand->name}",
        ]);
    }

    /** @test */
    public function brands_list_logs_activity_for_authenticated_users()
    {
        $this->actingAs($this->user)
            ->get('/brands');

        $this->assertDatabaseHas('user_activity_logs', [
            'user_id' => $this->user->id,
            'activity_type' => 'brands_list_accessed',
            'description' => 'User accessed brands list for search',
        ]);
    }

    /** @test */
    public function model_view_respects_search_configuration_limits()
    {
        // Change the limit
        SearchConfiguration::set('guest_max_visible_results', 3);

        $response = $this->get("/models/{$this->model->id}");

        $response->assertStatus(200)
                ->assertInertia(fn ($page) => $page
                    ->where('maxVisibleParts', 3)
                    ->where('hiddenPartsCount', 7) // 10 total - 3 visible = 7 hidden
                );
    }
}
