<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InfiniteLoopPreventionTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Brand $appleBrand;
    private Brand $samsungBrand;
    private MobileModel $iphone15;
    private MobileModel $galaxyS24;
    private Part $displayPart;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        // Create test brands
        $this->appleBrand = Brand::factory()->create([
            'name' => 'Apple',
            'logo_url' => 'https://example.com/apple-logo.png',
            'is_active' => true,
        ]);

        $this->samsungBrand = Brand::factory()->create([
            'name' => 'Samsung',
            'logo_url' => 'https://example.com/samsung-logo.png',
            'is_active' => true,
        ]);

        // Create test models
        $this->iphone15 = MobileModel::factory()->create([
            'name' => 'iPhone 15 Pro',
            'model_number' => 'A3108',
            'release_year' => 2023,
            'brand_id' => $this->appleBrand->id,
            'is_active' => true,
        ]);

        $this->galaxyS24 = MobileModel::factory()->create([
            'name' => 'Galaxy S24',
            'model_number' => 'SM-S921B',
            'release_year' => 2024,
            'brand_id' => $this->samsungBrand->id,
            'is_active' => true,
        ]);

        // Create test part
        $this->displayPart = Part::factory()->create([
            'name' => 'OLED Display Assembly',
        ]);
    }

    public function test_rapid_sequential_brand_searches_no_timeout(): void
    {
        // Test rapid sequential searches that could trigger infinite loops
        $queries = ['', 'a', 'ap', 'app', 'appl', 'apple', 'samsung', 'test', ''];
        
        foreach ($queries as $query) {
            $startTime = microtime(true);
            
            $response = $this->actingAs($this->user)
                ->get('/api/search/brands?q=' . urlencode($query) . '&limit=10');
            
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime) * 1000;
            
            $response->assertStatus(200);
            $this->assertLessThan(2000, $executionTime, "Brand search for '{$query}' should complete within 2 seconds");
            
            // Small delay to prevent overwhelming the server
            usleep(10000); // 10ms
        }
    }

    public function test_rapid_sequential_model_searches_no_timeout(): void
    {
        // Test rapid sequential model searches
        $queries = ['', 'i', 'ip', 'iph', 'ipho', 'iphon', 'iphone', 'galaxy', 'test', ''];
        
        foreach ($queries as $query) {
            $startTime = microtime(true);
            
            $response = $this->actingAs($this->user)
                ->get('/api/search/models?q=' . urlencode($query) . '&brand_id=' . $this->appleBrand->id . '&limit=10');
            
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime) * 1000;
            
            $response->assertStatus(200);
            $this->assertLessThan(2000, $executionTime, "Model search for '{$query}' should complete within 2 seconds");
            
            // Small delay to prevent overwhelming the server
            usleep(10000); // 10ms
        }
    }

    public function test_bulk_operations_with_state_changes(): void
    {
        // Simulate bulk operations that involve state changes
        // This tests the scenario that was causing infinite loops
        
        // First, search for brands
        $brandResponse = $this->actingAs($this->user)
            ->get('/api/search/brands?q=&limit=20');
        
        $brandResponse->assertStatus(200);
        $brands = $brandResponse->json();
        $this->assertNotEmpty($brands);
        
        // Then search for models for each brand
        foreach (array_slice($brands, 0, 3) as $brand) { // Limit to 3 brands for performance
            $modelResponse = $this->actingAs($this->user)
                ->get('/api/search/models?q=&brand_id=' . $brand['value'] . '&limit=10');
            
            $modelResponse->assertStatus(200);
            $models = $modelResponse->json();
            $this->assertIsArray($models);
        }
    }

    public function test_concurrent_bulk_searches_stability(): void
    {
        // Test concurrent requests that could cause race conditions
        $promises = [];
        
        // Create multiple concurrent brand searches
        for ($i = 0; $i < 5; $i++) {
            $response = $this->actingAs($this->user)
                ->get('/api/search/brands?q=test' . $i . '&limit=10');
            
            $response->assertStatus(200);
            $this->assertIsArray($response->json());
        }
        
        // Create multiple concurrent model searches
        for ($i = 0; $i < 5; $i++) {
            $response = $this->actingAs($this->user)
                ->get('/api/search/models?q=test' . $i . '&brand_id=' . $this->appleBrand->id . '&limit=10');
            
            $response->assertStatus(200);
            $this->assertIsArray($response->json());
        }
    }

    public function test_memory_usage_stability(): void
    {
        // Test that repeated operations don't cause memory leaks
        $initialMemory = memory_get_usage();
        
        // Perform many search operations
        for ($i = 0; $i < 50; $i++) {
            $response = $this->actingAs($this->user)
                ->get('/api/search/brands?q=test&limit=10');
            
            $response->assertStatus(200);
            
            // Check memory usage every 10 iterations
            if ($i % 10 === 0) {
                $currentMemory = memory_get_usage();
                $memoryIncrease = $currentMemory - $initialMemory;
                
                // Memory increase should be reasonable (less than 10MB)
                $this->assertLessThan(10 * 1024 * 1024, $memoryIncrease, 
                    "Memory usage should not increase excessively during repeated operations");
            }
        }
    }

    public function test_error_handling_prevents_loops(): void
    {
        // Test that error conditions don't cause infinite loops
        
        // Test with invalid brand ID
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=test&brand_id=99999&limit=10');
        
        $response->assertStatus(200);
        $models = $response->json();
        $this->assertIsArray($models);
        $this->assertEmpty($models); // Should return empty array, not error
        
        // Test with invalid parameters
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=&limit=invalid');
        
        $response->assertStatus(200); // Should handle gracefully
        
        // Test with very long query
        $longQuery = str_repeat('a', 1000);
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=' . urlencode($longQuery) . '&limit=10');
        
        $response->assertStatus(200);
        $this->assertIsArray($response->json());
    }

    public function test_database_query_efficiency(): void
    {
        // Test that database queries are efficient and don't cause N+1 problems
        
        // Create additional test data
        $additionalBrands = Brand::factory()->count(10)->create(['is_active' => true]);
        foreach ($additionalBrands as $brand) {
            MobileModel::factory()->count(5)->create([
                'brand_id' => $brand->id,
                'is_active' => true,
            ]);
        }
        
        // Enable query logging
        \DB::enableQueryLog();
        
        // Perform brand search
        $response = $this->actingAs($this->user)
            ->get('/api/search/brands?q=&limit=20');
        
        $response->assertStatus(200);
        
        // Check query count (should be minimal)
        $queries = \DB::getQueryLog();
        $this->assertLessThan(10, count($queries), 'Brand search should use minimal database queries');
        
        // Reset query log
        \DB::flushQueryLog();
        
        // Perform model search
        $response = $this->actingAs($this->user)
            ->get('/api/search/models?q=&brand_id=' . $this->appleBrand->id . '&limit=20');
        
        $response->assertStatus(200);
        
        // Check query count for model search
        $queries = \DB::getQueryLog();
        $this->assertLessThan(10, count($queries), 'Model search should use minimal database queries');
        
        \DB::disableQueryLog();
    }
}
