<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Inertia\Testing\AssertableInertia as Assert;
use Tests\TestCase;

class ContentManagerDashboardAccessTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that HandleInertiaRequests middleware passes correct user data for content manager.
     */
    public function test_handle_inertia_requests_passes_content_manager_data()
    {
        $contentManager = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'content_manager',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($contentManager);

        $response = $this->get('/admin/dashboard');

        $response->assertInertia(fn (Assert $page) => $page
            ->has('auth.user')
            ->where('auth.user.id', $contentManager->id)
            ->where('auth.user.email', '<EMAIL>')
            ->where('auth.user.role', 'content_manager')
            ->where('auth.user.isAdmin', false)
            ->where('auth.user.isContentManager', true)
            ->where('auth.user.canManageContent', true)
            ->where('auth.user.canAccessAdminDashboard', true)
        );
    }

    /**
     * Test that HandleInertiaRequests middleware passes correct user data for admin.
     */
    public function test_handle_inertia_requests_passes_admin_data()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'admin',
            'is_admin' => 1,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($admin);

        $response = $this->get('/admin/dashboard');

        $response->assertInertia(fn (Assert $page) => $page
            ->has('auth.user')
            ->where('auth.user.id', $admin->id)
            ->where('auth.user.email', '<EMAIL>')
            ->where('auth.user.role', 'admin')
            ->where('auth.user.isAdmin', true)
            ->where('auth.user.isContentManager', false)
            ->where('auth.user.canManageContent', true)
            ->where('auth.user.canAccessAdminDashboard', true)
        );
    }

    /**
     * Test that HandleInertiaRequests middleware passes correct user data for regular user.
     */
    public function test_handle_inertia_requests_passes_regular_user_data()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'user',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($user);

        $response = $this->get('/dashboard');

        $response->assertInertia(fn (Assert $page) => $page
            ->has('auth.user')
            ->where('auth.user.id', $user->id)
            ->where('auth.user.email', '<EMAIL>')
            ->where('auth.user.role', 'user')
            ->where('auth.user.isAdmin', false)
            ->where('auth.user.isContentManager', false)
            ->where('auth.user.canManageContent', false)
            ->where('auth.user.canAccessAdminDashboard', false)
        );
    }

    /**
     * Test content manager can access admin dashboard after login.
     */
    public function test_content_manager_can_access_admin_dashboard_after_login()
    {
        $contentManager = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'content_manager',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Login as content manager
        $loginResponse = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Should redirect to admin dashboard
        $loginResponse->assertRedirect('/admin/dashboard');

        // Follow the redirect and verify access
        $dashboardResponse = $this->get('/admin/dashboard');
        $dashboardResponse->assertStatus(200);

        // Verify the page is the admin dashboard
        $dashboardResponse->assertInertia(fn (Assert $page) => $page
            ->component('admin/dashboard')
            ->has('stats')
            ->has('charts')
        );
    }

    /**
     * Test content manager cannot access admin-only routes.
     */
    public function test_content_manager_cannot_access_admin_only_routes()
    {
        $contentManager = User::factory()->create([
            'role' => 'content_manager',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($contentManager);

        // Test admin-only routes
        $adminOnlyRoutes = [
            '/admin/users',
            '/admin/payment-requests',
            '/admin/subscriptions',
        ];

        foreach ($adminOnlyRoutes as $route) {
            $response = $this->get($route);
            $response->assertStatus(403);
        }
    }

    /**
     * Test regular user cannot access admin dashboard.
     */
    public function test_regular_user_cannot_access_admin_dashboard()
    {
        $user = User::factory()->create([
            'role' => 'user',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($user);

        $response = $this->get('/admin/dashboard');
        $response->assertStatus(403);
    }

    /**
     * Test that content manager gets redirected to admin dashboard on login even with intended URL.
     */
    public function test_content_manager_login_with_intended_url()
    {
        $contentManager = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'content_manager',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Try to access a protected route first (this will set the intended URL)
        $this->get('/admin/parts');

        // Now login
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Should still redirect to admin dashboard (not the intended URL)
        $response->assertRedirect('/admin/dashboard');
    }
}
