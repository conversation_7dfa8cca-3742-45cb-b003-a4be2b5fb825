<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PricingPlan;
use App\Services\CountryDetectionService;
use App\Services\LocalizedPricingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;

class LocalizationFunctionalityTest extends TestCase
{
    use RefreshDatabase;

    private CountryDetectionService $countryDetectionService;
    private LocalizedPricingService $localizedPricingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->countryDetectionService = app(CountryDetectionService::class);
        $this->localizedPricingService = app(LocalizedPricingService::class);

        // Create test pricing plans
        $this->createTestPlans();
    }

    private function createTestPlans(): void
    {
        // Create USD plans
        PricingPlan::create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'description' => 'Perfect for getting started with basic mobile parts search',
            'price' => 0.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['20 searches per day', 'Basic part information', 'Standard resolution images', 'Email support'],
            'search_limit' => 20,
            'model_view_limit' => 10,
            'parts_per_model_limit' => 5,
            'is_active' => true,
            'is_public' => true,
            'sort_order' => 1,
        ]);

        PricingPlan::create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'description' => 'Unlimited access with advanced features for professionals',
            'price' => 19.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Unlimited searches', 'Detailed specifications', 'High-resolution images', 'Priority support', 'Advanced filters', 'Export functionality', 'API access'],
            'search_limit' => 100,
            'model_view_limit' => 10,
            'parts_per_model_limit' => 5,
            'is_active' => true,
            'is_public' => true,
            'is_popular' => true,
            'sort_order' => 2,
        ]);

        // Create BDT plans (localized for Bangladesh)
        PricingPlan::create([
            'name' => 'free_bd',
            'display_name' => 'Free Plan (Bangladesh)',
            'description' => 'Perfect for getting started with basic mobile parts search - Localized for Bangladesh',
            'price' => 0.00,
            'currency' => 'BDT',
            'interval' => 'month',
            'features' => ['20 searches per day', 'Basic part information', 'Standard resolution images', 'Email support'],
            'search_limit' => 20,
            'model_view_limit' => 10,
            'parts_per_model_limit' => 5,
            'is_active' => true,
            'is_public' => true,
            'sort_order' => 1,
            'metadata' => [
                'localized_for' => 'BD',
                'base_plan_id' => 1,
                'conversion_rate' => 110,
            ],
        ]);

        PricingPlan::create([
            'name' => 'premium_bd',
            'display_name' => 'Premium Plan (Bangladesh)',
            'description' => 'Unlimited access with advanced features for professionals - Localized for Bangladesh',
            'price' => 2090.00,
            'currency' => 'BDT',
            'interval' => 'month',
            'features' => ['Unlimited searches', 'Detailed specifications', 'High-resolution images', 'Priority support', 'Advanced filters', 'Export functionality', 'API access'],
            'search_limit' => 100,
            'model_view_limit' => 10,
            'parts_per_model_limit' => 5,
            'is_active' => true,
            'is_public' => true,
            'is_popular' => true,
            'sort_order' => 2,
            'metadata' => [
                'localized_for' => 'BD',
                'base_plan_id' => 2,
                'conversion_rate' => 110,
            ],
        ]);
    }

    /** @test */
    public function localization_api_returns_country_specific_plans_for_bangladesh()
    {
        // Simulate a request from Bangladesh
        $request = Request::create('/api/localization/pricing-plans', 'GET');
        $request->headers->set('X-Forwarded-For', '*************'); // Bangladesh IP

        $response = $this->get('/api/localization/pricing-plans', [
            'X-Forwarded-For' => '*************'
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertTrue($data['success']);
        $this->assertEquals('BD', $data['data']['country_code']);
        $this->assertEquals('BDT', $data['data']['currency']);

        // Should return only BDT plans
        $plans = $data['data']['plans'];
        $this->assertCount(2, $plans);

        foreach ($plans as $plan) {
            $this->assertEquals('BDT', $plan['currency']);
            $this->assertStringContainsString('Bangladesh', $plan['display_name']);
        }
    }

    /** @test */
    public function localization_api_returns_usd_plans_for_international_users()
    {
        // Simulate a request from US
        $response = $this->get('/api/localization/pricing-plans', [
            'X-Forwarded-For' => '*******' // Google DNS (US)
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertTrue($data['success']);
        $this->assertEquals('US', $data['data']['country_code']);
        $this->assertEquals('USD', $data['data']['currency']);

        // Should return only USD plans
        $plans = $data['data']['plans'];
        $this->assertCount(2, $plans);

        foreach ($plans as $plan) {
            $this->assertEquals('USD', $plan['currency']);
            $this->assertStringNotContainsString('Bangladesh', $plan['display_name']);
        }
    }

    /** @test */
    public function currency_formatting_is_correct_for_bdt()
    {
        $response = $this->get('/api/localization/pricing-plans', [
            'X-Forwarded-For' => '*************' // Bangladesh IP
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        $plans = $data['data']['plans'];
        
        foreach ($plans as $plan) {
            $this->assertEquals('BDT', $plan['currency']);
            $this->assertEquals('৳', $plan['currency_symbol']);
            $this->assertStringStartsWith('৳', $plan['formatted_price']);
        }
    }

    /** @test */
    public function currency_formatting_is_correct_for_usd()
    {
        $response = $this->get('/api/localization/pricing-plans', [
            'X-Forwarded-For' => '*******' // US IP
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        $plans = $data['data']['plans'];
        
        foreach ($plans as $plan) {
            $this->assertEquals('USD', $plan['currency']);
            $this->assertEquals('$', $plan['currency_symbol']);
            $this->assertStringStartsWith('$', $plan['formatted_price']);
        }
    }

    /** @test */
    public function localization_data_endpoint_provides_complete_information()
    {
        $response = $this->get('/api/localization/data', [
            'X-Forwarded-For' => '*************' // Bangladesh IP
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertTrue($data['success']);
        
        // Check country data
        $this->assertArrayHasKey('country', $data['data']);
        $this->assertEquals('BD', $data['data']['country']['country_code']);
        $this->assertEquals('Bangladesh', $data['data']['country']['country_name']);
        $this->assertEquals('BDT', $data['data']['country']['currency']);
        $this->assertTrue($data['data']['country']['is_bangladesh']);

        // Check payment gateways
        $this->assertArrayHasKey('payment_gateways', $data['data']);
        $this->assertEquals('BDT', $data['data']['payment_gateways']['currency']);

        // Check pricing data
        $this->assertArrayHasKey('pricing', $data['data']);
        $this->assertEquals('BDT', $data['data']['pricing']['currency']);
        $this->assertEquals('BD', $data['data']['pricing']['country_code']);
    }

    /** @test */
    public function pricing_page_is_accessible_and_uses_localization()
    {
        $response = $this->get('/pricing');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('pricing'));
    }

    /** @test */
    public function old_pricing_api_still_returns_all_plans()
    {
        // The old API should still work for backward compatibility
        $response = $this->get('/api/pricing-plans/all');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertTrue($data['success']);
        $this->assertCount(4, $data['data']['plans']); // Should return all plans (USD + BDT)
    }

    /** @test */
    public function localized_pricing_service_returns_correct_plans_for_country()
    {
        // Test Bangladesh
        $bdPlans = $this->localizedPricingService->getLocalizedPlansForCountry('BD');
        $this->assertNotEmpty($bdPlans);
        foreach ($bdPlans as $plan) {
            $this->assertEquals('BDT', $plan->currency);
        }

        // Test US
        $usPlans = $this->localizedPricingService->getLocalizedPlansForCountry('US');
        $this->assertNotEmpty($usPlans);
        foreach ($usPlans as $plan) {
            $this->assertEquals('USD', $plan->currency);
        }
    }

    /** @test */
    public function country_detection_service_works_correctly()
    {
        // Test direct country detection with known country codes
        $bdCountryData = $this->countryDetectionService->getCountryFromIp('*************');

        // The service should return some country data (might default to US in test environment)
        $this->assertArrayHasKey('country_code', $bdCountryData);
        $this->assertArrayHasKey('country_name', $bdCountryData);
        $this->assertArrayHasKey('currency', $bdCountryData);
        $this->assertArrayHasKey('is_bangladesh', $bdCountryData);

        // Test that the service returns consistent data structure
        $this->assertIsString($bdCountryData['country_code']);
        $this->assertIsString($bdCountryData['country_name']);
        $this->assertIsString($bdCountryData['currency']);
        $this->assertIsBool($bdCountryData['is_bangladesh']);
    }
}
