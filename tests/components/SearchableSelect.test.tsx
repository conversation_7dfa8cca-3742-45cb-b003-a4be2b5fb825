import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { vi, describe, it, expect, beforeEach } from 'vitest';

const mockOptions: SearchableSelectOption[] = [
  { value: '1', label: 'Apple', image: '/apple-logo.png' },
  { value: '2', label: 'Samsung', description: 'South Korean brand' },
  { value: '3', label: 'Google', description: 'Pixel phones' },
  { value: '4', label: 'OnePlus', description: 'Never Settle' },
];

describe('SearchableSelect', () => {
  const defaultProps = {
    options: mockOptions,
    placeholder: 'Select a brand...',
    searchPlaceholder: 'Search brands...',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with placeholder when no value is selected', () => {
    render(<SearchableSelect {...defaultProps} />);
    expect(screen.getByText('Select a brand...')).toBeInTheDocument();
  });

  it('displays selected option when value is provided', () => {
    render(<SearchableSelect {...defaultProps} value="1" />);
    expect(screen.getByText('Apple')).toBeInTheDocument();
  });

  it('opens dropdown when clicked', async () => {
    const user = userEvent.setup();
    render(<SearchableSelect {...defaultProps} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    expect(screen.getByPlaceholder('Search brands...')).toBeInTheDocument();
  });

  it('filters options based on search query', async () => {
    const user = userEvent.setup();
    render(<SearchableSelect {...defaultProps} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    const searchInput = screen.getByPlaceholder('Search brands...');
    await user.type(searchInput, 'sam');
    
    await waitFor(() => {
      expect(screen.getByText('Samsung')).toBeInTheDocument();
      expect(screen.queryByText('Apple')).not.toBeInTheDocument();
    });
  });

  it('calls onValueChange when option is selected', async () => {
    const user = userEvent.setup();
    const onValueChange = vi.fn();
    render(<SearchableSelect {...defaultProps} onValueChange={onValueChange} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    const option = screen.getByText('Apple');
    await user.click(option);
    
    expect(onValueChange).toHaveBeenCalledWith('1');
  });

  it('calls onSearch when search query changes and onSearch is provided', async () => {
    const user = userEvent.setup();
    const onSearch = vi.fn();
    render(<SearchableSelect {...defaultProps} onSearch={onSearch} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    const searchInput = screen.getByPlaceholder('Search brands...');
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      expect(onSearch).toHaveBeenCalledWith('test');
    }, { timeout: 500 });
  });

  it('shows loading state when loading prop is true', async () => {
    const user = userEvent.setup();
    render(<SearchableSelect {...defaultProps} loading={true} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows empty state when no options match', async () => {
    const user = userEvent.setup();
    render(<SearchableSelect {...defaultProps} emptyText="No brands found" />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    const searchInput = screen.getByPlaceholder('Search brands...');
    await user.type(searchInput, 'nonexistent');
    
    await waitFor(() => {
      expect(screen.getByText('No brands found')).toBeInTheDocument();
    });
  });

  it('shows clear button when allowClear is true and value is selected', () => {
    render(<SearchableSelect {...defaultProps} value="1" allowClear={true} />);
    
    const clearButton = screen.getByRole('button').querySelector('svg[class*="lucide-x"]');
    expect(clearButton).toBeInTheDocument();
  });

  it('clears value when clear button is clicked', async () => {
    const user = userEvent.setup();
    const onValueChange = vi.fn();
    render(<SearchableSelect {...defaultProps} value="1" allowClear={true} onValueChange={onValueChange} />);
    
    const clearButton = screen.getByRole('button').querySelector('svg[class*="lucide-x"]');
    await user.click(clearButton!);
    
    expect(onValueChange).toHaveBeenCalledWith('');
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<SearchableSelect {...defaultProps} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    // Test Escape key closes dropdown
    await user.keyboard('{Escape}');
    await waitFor(() => {
      expect(screen.queryByPlaceholder('Search brands...')).not.toBeInTheDocument();
    });
  });

  it('renders custom option content when renderOption is provided', async () => {
    const user = userEvent.setup();
    const renderOption = (option: SearchableSelectOption) => (
      <div data-testid={`custom-${option.value}`}>Custom: {option.label}</div>
    );
    
    render(<SearchableSelect {...defaultProps} renderOption={renderOption} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    expect(screen.getByTestId('custom-1')).toBeInTheDocument();
    expect(screen.getByText('Custom: Apple')).toBeInTheDocument();
  });

  it('renders custom selected content when renderSelected is provided', () => {
    const renderSelected = (option: SearchableSelectOption) => (
      <div data-testid="custom-selected">Selected: {option.label}</div>
    );
    
    render(<SearchableSelect {...defaultProps} value="1" renderSelected={renderSelected} />);
    
    expect(screen.getByTestId('custom-selected')).toBeInTheDocument();
    expect(screen.getByText('Selected: Apple')).toBeInTheDocument();
  });

  it('is disabled when disabled prop is true', () => {
    render(<SearchableSelect {...defaultProps} disabled={true} />);
    
    const trigger = screen.getByRole('combobox');
    expect(trigger).toBeDisabled();
  });

  it('displays option images when provided', async () => {
    const user = userEvent.setup();
    render(<SearchableSelect {...defaultProps} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    const appleImage = screen.getByAltText('Apple');
    expect(appleImage).toBeInTheDocument();
    expect(appleImage).toHaveAttribute('src', '/apple-logo.png');
  });

  it('displays option descriptions when provided', async () => {
    const user = userEvent.setup();
    render(<SearchableSelect {...defaultProps} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    expect(screen.getByText('South Korean brand')).toBeInTheDocument();
    expect(screen.getByText('Pixel phones')).toBeInTheDocument();
  });

  it('debounces search calls', async () => {
    const user = userEvent.setup();
    const onSearch = vi.fn();
    render(<SearchableSelect {...defaultProps} onSearch={onSearch} />);
    
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);
    
    const searchInput = screen.getByPlaceholder('Search brands...');
    
    // Type multiple characters quickly
    await user.type(searchInput, 'test', { delay: 50 });
    
    // Should only call onSearch once after debounce delay
    await waitFor(() => {
      expect(onSearch).toHaveBeenCalledTimes(1);
      expect(onSearch).toHaveBeenCalledWith('test');
    }, { timeout: 500 });
  });
});
