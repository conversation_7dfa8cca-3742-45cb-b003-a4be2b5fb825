import { PaymentMethodSelector } from '@/components/PaymentMethodSelector';
import { PaddleProvider } from '@/contexts/PaddleContext';
import { toast } from 'sonner';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { fireEvent, render, screen, waitFor } from '../utils/test-utils';

// Mock dependencies
vi.mock('sonner', () => ({
    toast: {
        error: vi.fn(),
        info: vi.fn(),
        success: vi.fn(),
    },
}));

vi.mock('@/contexts/PaddleContext', () => ({
    PaddleProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    usePaddle: () => ({
        isLoaded: true,
        isConfigured: false,
        config: { development_mode: true, mock_mode: false },
        error: null,
        paddle: null,
    }),
}));

vi.mock('@/components/PaddleCheckout', () => ({
    PaddleCheckout: ({ onError }: { onError: (error: string) => void }) => (
        <div>
            <button
                onClick={() => onError('Development Mode: Test error message')}
                data-testid="paddle-checkout-button-dev"
            >
                Pay with Paddle (Dev Mode)
            </button>
            <button
                onClick={() => onError('Network connection failed')}
                data-testid="paddle-checkout-button-prod"
            >
                Pay with Paddle (Prod Mode)
            </button>
        </div>
    ),
}));

vi.mock('@/components/ShurjoPayCheckout', () => ({
    ShurjoPayCheckout: () => <div>ShurjoPay Checkout</div>,
}));

vi.mock('@/components/CoinbaseCommerceCheckout', () => ({
    CoinbaseCommerceCheckout: () => <div>Coinbase Commerce Checkout</div>,
}));

describe('PaymentMethodSelector Toast Fix', () => {
    const mockToast = toast as any;
    const mockPlan = {
        id: 1,
        name: 'premium',
        display_name: 'Premium Plan',
        price: 19.99,
        interval: 'month',
        features: ['Unlimited searches', 'Priority support'],
        has_paddle_integration: true,
        has_shurjopay_integration: true,
        has_coinbase_commerce_integration: true,
        has_online_payment_enabled: true,
        has_offline_payment_enabled: true,
        crypto_payment_enabled: true,
        has_any_payment_method: true,
        supports_monthly: true,
        supports_yearly: true,
        currency: 'USD',
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('should not show toast for development mode errors from Paddle', async () => {
        render(
            <PaddleProvider>
                <PaymentMethodSelector
                    plan={mockPlan}
                    billingCycle="month"
                    onBillingCycleChange={vi.fn()}
                />
            </PaddleProvider>
        );

        // Select Paddle payment method
        const paddleOption = screen.getByText('Pay with Card');
        fireEvent.click(paddleOption);

        // Wait for Paddle checkout component to render
        await waitFor(() => {
            expect(screen.getByTestId('paddle-checkout-button-dev')).toBeInTheDocument();
        });

        // Simulate Paddle error with development mode message
        const paddleButton = screen.getByTestId('paddle-checkout-button-dev');
        fireEvent.click(paddleButton);

        await waitFor(() => {
            // Should not show any toast for development mode errors
            expect(mockToast.error).not.toHaveBeenCalled();
        });
    });

    it('should show toast for non-development mode errors from Paddle', async () => {
        // Mock PaddleCheckout to trigger non-development error
        vi.doMock('@/components/PaddleCheckout', () => ({
            default: ({ onError }: { onError: (error: string) => void }) => (
                <button 
                    onClick={() => onError('Network connection failed')}
                    data-testid="paddle-checkout-button"
                >
                    Pay with Paddle
                </button>
            ),
        }));

        render(
            <PaddleProvider>
                <PaymentMethodSelector
                    plan={mockPlan}
                    billingCycle="month"
                    onBillingCycleChange={vi.fn()}
                />
            </PaddleProvider>
        );

        // Select Paddle payment method
        const paddleOption = screen.getByText('Pay with Card');
        fireEvent.click(paddleOption);

        // Wait for Paddle checkout component to render
        await waitFor(() => {
            expect(screen.getByTestId('paddle-checkout-button-prod')).toBeInTheDocument();
        });

        // Simulate Paddle error without development mode message
        const paddleButton = screen.getByTestId('paddle-checkout-button-prod');
        fireEvent.click(paddleButton);

        await waitFor(() => {
            // Should show toast for non-development mode errors
            expect(mockToast.error).toHaveBeenCalledTimes(1);
            expect(mockToast.error).toHaveBeenCalledWith('Network connection failed');
        });
    });

    it('should switch to offline payment method when Paddle error occurs', async () => {
        render(
            <PaddleProvider>
                <PaymentMethodSelector
                    plan={mockPlan}
                    billingCycle="month"
                    onBillingCycleChange={vi.fn()}
                />
            </PaddleProvider>
        );

        // Select Paddle payment method
        const paddleOption = screen.getByText('Pay with Card');
        fireEvent.click(paddleOption);

        // Wait for Paddle checkout component to render
        await waitFor(() => {
            expect(screen.getByTestId('paddle-checkout-button-prod')).toBeInTheDocument();
        });

        // Simulate Paddle error
        const paddleButton = screen.getByTestId('paddle-checkout-button-prod');
        fireEvent.click(paddleButton);

        await waitFor(() => {
            // Should switch to offline payment method
            const offlineOption = screen.getByText('Offline Payment');
            expect(offlineOption).toBeInTheDocument();
        });
    });

    it('should handle billing cycle changes without duplicate toasts', () => {
        const onBillingCycleChange = vi.fn();

        render(
            <PaddleProvider>
                <PaymentMethodSelector
                    plan={mockPlan}
                    billingCycle="month"
                    onBillingCycleChange={onBillingCycleChange}
                />
            </PaddleProvider>
        );

        // Switch to yearly billing
        const yearlyOption = screen.getByText('Yearly');
        fireEvent.click(yearlyOption);

        expect(onBillingCycleChange).toHaveBeenCalledWith('year');
        expect(mockToast.error).not.toHaveBeenCalled();
        expect(mockToast.info).not.toHaveBeenCalled();
    });
});
