import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import * as InertiaReact from '@inertiajs/react';
import * as ApiUtils from '@/utils/api';

// Mock Inertia
const mockUsePage = vi.fn();
const mockRouter = {
    post: vi.fn()
};

vi.mock('@inertiajs/react', () => ({
    usePage: () => mockUsePage(),
    router: mockRouter
}));

// Mock API utils
const mockApiGet = vi.fn();
const mockHandleApiError = vi.fn();

vi.mock('@/utils/api', () => ({
    api: {
        get: mockApiGet
    },
    handleApiError: mockHandleApiError
}));

// Mock UI components
vi.mock('@/components/ui/alert', () => ({
    Alert: ({ children, className }: any) => <div className={className}>{children}</div>,
    AlertDescription: ({ children }: any) => <div>{children}</div>
}));

vi.mock('@/components/ui/button', () => ({
    Button: ({ children, onClick, className }: any) => (
        <button onClick={onClick} className={className}>{children}</button>
    )
}));

describe('ImpersonationBanner', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it('renders nothing when user is not authenticated', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: null
                }
            }
        });

        const { container } = render(<ImpersonationBanner />);
        
        await waitFor(() => {
            expect(container.firstChild).toBeNull();
        });
    });

    it('renders nothing when not impersonating', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet.mockResolvedValue({
            ok: true,
            data: {
                is_impersonating: false
            },
            status: 200
        });

        const { container } = render(<ImpersonationBanner />);
        
        await waitFor(() => {
            expect(container.firstChild).toBeNull();
        });
    });

    it('renders banner when impersonating', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Admin User' }
                }
            }
        });

        mockApiGet.mockResolvedValue({
            ok: true,
            data: {
                is_impersonating: true,
                impersonating_user_id: 123,
                remaining_minutes: 15
            },
            status: 200
        });

        render(<ImpersonationBanner />);
        
        await waitFor(() => {
            expect(screen.getByText('You are currently impersonating a user')).toBeInTheDocument();
            expect(screen.getByText('(User ID: 123)')).toBeInTheDocument();
            expect(screen.getByText('• 15 min remaining')).toBeInTheDocument();
            expect(screen.getByText('Return to Admin')).toBeInTheDocument();
        });
    });

    it('handles 401 authentication errors gracefully', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet.mockResolvedValue({
            ok: false,
            status: 401,
            error: 'Unauthorized'
        });

        const { container } = render(<ImpersonationBanner />);
        
        await waitFor(() => {
            expect(container.firstChild).toBeNull();
        });

        expect(mockHandleApiError).not.toHaveBeenCalled();
    });

    it('handles network errors properly', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet.mockRejectedValue(new Error('Network error'));

        const { container } = render(<ImpersonationBanner />);
        
        await waitFor(() => {
            expect(container.firstChild).toBeNull();
        });

        expect(mockHandleApiError).toHaveBeenCalledWith('Network error', 'Failed to check impersonation status');
    });

    it('calls end impersonation when button is clicked', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Admin User' }
                }
            }
        });

        mockApiGet.mockResolvedValue({
            ok: true,
            data: {
                is_impersonating: true,
                impersonating_user_id: 123
            },
            status: 200
        });

        render(<ImpersonationBanner />);
        
        await waitFor(() => {
            expect(screen.getByText('Return to Admin')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('Return to Admin'));

        expect(mockRouter.post).toHaveBeenCalledWith(
            '/admin/impersonate/end',
            {},
            expect.objectContaining({
                onSuccess: expect.any(Function),
                onError: expect.any(Function)
            })
        );
    });

    it('uses correct API endpoint', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet.mockResolvedValue({
            ok: true,
            data: { is_impersonating: false },
            status: 200
        });

        render(<ImpersonationBanner />);
        
        await waitFor(() => {
            expect(mockApiGet).toHaveBeenCalledWith(
                '/api/impersonation/status',
                { requireAuth: true }
            );
        });
    });

    it('handles server errors appropriately', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet.mockResolvedValue({
            ok: false,
            status: 500,
            error: 'Internal Server Error'
        });

        const { container } = render(<ImpersonationBanner />);
        
        await waitFor(() => {
            expect(container.firstChild).toBeNull();
        });

        expect(mockHandleApiError).toHaveBeenCalled();
    });
});
