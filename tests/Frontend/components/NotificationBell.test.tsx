import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import NotificationBell from '@/components/user/NotificationBell';
import * as InertiaReact from '@inertiajs/react';
import * as ApiUtils from '@/utils/api';

// Mock Inertia
const mockUsePage = vi.fn();
const mockRouter = {
    post: vi.fn(),
    get: vi.fn()
};
const mockRoute = vi.fn();

vi.mock('@inertiajs/react', () => ({
    usePage: () => mockUsePage(),
    router: mockRouter,
    Link: ({ children, href }: any) => <a href={href}>{children}</a>
}));

// Mock route helper
global.route = mockRoute;

// Mock API utils
const mockApiGet = vi.fn();
const mockHandleApiError = vi.fn();

vi.mock('@/utils/api', () => ({
    api: {
        get: mockApiGet
    },
    handleApiError: mockHandleApiError
}));

// Mock UI components
vi.mock('@/components/ui/badge', () => ({
    Badge: ({ children, className }: any) => <span className={className}>{children}</span>
}));

vi.mock('@/components/ui/button', () => ({
    Button: ({ children, onClick, className }: any) => (
        <button onClick={onClick} className={className}>{children}</button>
    )
}));

vi.mock('@/components/ui/dropdown-menu', () => ({
    DropdownMenu: ({ children }: any) => <div>{children}</div>,
    DropdownMenuContent: ({ children }: any) => <div>{children}</div>,
    DropdownMenuItem: ({ children, onClick }: any) => (
        <div onClick={onClick}>{children}</div>
    ),
    DropdownMenuLabel: ({ children }: any) => <div>{children}</div>,
    DropdownMenuSeparator: () => <hr />,
    DropdownMenuTrigger: ({ children }: any) => <div>{children}</div>
}));

describe('NotificationBell', () => {
    const mockNotifications = [
        {
            id: 1,
            title: 'Test Notification',
            message: 'This is a test notification',
            type: 'info' as const,
            read_at: null,
            created_at: '2024-01-01T10:00:00Z'
        },
        {
            id: 2,
            title: 'Read Notification',
            message: 'This notification has been read',
            type: 'success' as const,
            read_at: '2024-01-01T11:00:00Z',
            created_at: '2024-01-01T10:30:00Z'
        }
    ];

    beforeEach(() => {
        vi.clearAllMocks();
        mockRoute.mockImplementation((name: string) => {
            const routes: Record<string, string> = {
                'api.notifications.unread-count': '/api/notifications/unread-count',
                'api.notifications.recent': '/api/notifications/recent',
                'notifications.mark-read': '/notifications/mark-read',
                'notifications.index': '/notifications'
            };
            return routes[name] || name;
        });
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it('renders loading state initially', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet.mockImplementation(() => new Promise(() => {})); // Never resolves

        render(<NotificationBell />);
        
        // Component should render but in loading state
        expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('does not fetch data when user is not authenticated', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: null
                }
            }
        });

        render(<NotificationBell />);
        
        await waitFor(() => {
            expect(mockApiGet).not.toHaveBeenCalled();
        });
    });

    it('fetches notification data when user is authenticated', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet
            .mockResolvedValueOnce({
                ok: true,
                data: { count: 3 },
                status: 200
            })
            .mockResolvedValueOnce({
                ok: true,
                data: { notifications: mockNotifications },
                status: 200
            });

        render(<NotificationBell />);
        
        await waitFor(() => {
            expect(mockApiGet).toHaveBeenCalledWith('/api/notifications/unread-count', { requireAuth: true });
            expect(mockApiGet).toHaveBeenCalledWith('/api/notifications/recent', { requireAuth: true });
        });
    });

    it('displays unread count badge', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet
            .mockResolvedValueOnce({
                ok: true,
                data: { count: 5 },
                status: 200
            })
            .mockResolvedValueOnce({
                ok: true,
                data: { notifications: [] },
                status: 200
            });

        render(<NotificationBell />);
        
        await waitFor(() => {
            expect(screen.getByText('5')).toBeInTheDocument();
        });
    });

    it('handles 401 authentication errors gracefully', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet
            .mockResolvedValueOnce({
                ok: false,
                status: 401,
                error: 'Unauthorized'
            })
            .mockResolvedValueOnce({
                ok: false,
                status: 401,
                error: 'Unauthorized'
            });

        render(<NotificationBell />);
        
        await waitFor(() => {
            expect(mockHandleApiError).not.toHaveBeenCalled();
        });
    });

    it('handles network errors properly', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet.mockRejectedValue(new Error('Network error'));

        render(<NotificationBell />);
        
        await waitFor(() => {
            expect(mockHandleApiError).toHaveBeenCalledWith('Network error', 'Failed to fetch notification data');
        });
    });

    it('displays notifications in dropdown', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet
            .mockResolvedValueOnce({
                ok: true,
                data: { count: 2 },
                status: 200
            })
            .mockResolvedValueOnce({
                ok: true,
                data: { notifications: mockNotifications },
                status: 200
            });

        render(<NotificationBell />);
        
        await waitFor(() => {
            expect(screen.getByText('Test Notification')).toBeInTheDocument();
            expect(screen.getByText('Read Notification')).toBeInTheDocument();
        });
    });

    it('marks notification as read when clicked', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet
            .mockResolvedValueOnce({
                ok: true,
                data: { count: 1 },
                status: 200
            })
            .mockResolvedValueOnce({
                ok: true,
                data: { notifications: [mockNotifications[0]] },
                status: 200
            });

        render(<NotificationBell />);
        
        await waitFor(() => {
            expect(screen.getByText('Test Notification')).toBeInTheDocument();
        });

        // Find and click the mark as read button
        const markAsReadButton = screen.getByRole('button', { name: /eye/i });
        fireEvent.click(markAsReadButton);

        expect(mockRouter.post).toHaveBeenCalledWith(
            '/notifications/mark-read',
            {},
            expect.objectContaining({
                preserveState: true,
                preserveScroll: true,
                onSuccess: expect.any(Function),
                onError: expect.any(Function)
            })
        );
    });

    it('sets up polling for real-time updates', async () => {
        vi.useFakeTimers();
        
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet
            .mockResolvedValue({
                ok: true,
                data: { count: 0 },
                status: 200
            })
            .mockResolvedValue({
                ok: true,
                data: { notifications: [] },
                status: 200
            });

        render(<NotificationBell />);
        
        // Wait for initial fetch
        await waitFor(() => {
            expect(mockApiGet).toHaveBeenCalledTimes(2);
        });

        // Clear the mock calls
        mockApiGet.mockClear();

        // Fast-forward 30 seconds
        vi.advanceTimersByTime(30000);

        await waitFor(() => {
            expect(mockApiGet).toHaveBeenCalledTimes(2); // Should fetch again
        });

        vi.useRealTimers();
    });

    it('cleans up polling interval on unmount', async () => {
        const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
        
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        mockApiGet.mockResolvedValue({
            ok: true,
            data: { count: 0 },
            status: 200
        });

        const { unmount } = render(<NotificationBell />);
        
        unmount();

        expect(clearIntervalSpy).toHaveBeenCalled();
    });

    it('handles concurrent API requests properly', async () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: { id: 1, name: 'Test User' }
                }
            }
        });

        // Mock Promise.allSettled behavior
        mockApiGet
            .mockResolvedValueOnce({
                ok: true,
                data: { count: 2 },
                status: 200
            })
            .mockResolvedValueOnce({
                ok: true,
                data: { notifications: mockNotifications },
                status: 200
            });

        render(<NotificationBell />);
        
        await waitFor(() => {
            expect(mockApiGet).toHaveBeenCalledTimes(2);
        });
    });
});
