import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import PartDetails from '@/pages/search/part-details';
import { type Part, type SharedData } from '@/types';

// Mock Inertia
vi.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <div data-testid="head">{children}</div>,
    Link: ({ children, href, ...props }: any) => <a href={href} {...props}>{children}</a>,
    router: {
        post: vi.fn(),
    },
    usePage: () => ({
        props: {
            auth: { user: null },
        } as SharedData,
    }),
}));

// Mock components
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

vi.mock('@/components/Watermark', () => ({
    AutoWatermark: () => <div data-testid="watermark" />,
}));

vi.mock('@/components/security/CompatibleModelsProtection', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="protection">{children}</div>,
}));

describe('Part Sales Button', () => {
    const basePart: Part = {
        id: 1,
        name: 'Test Part',
        slug: 'test-part',
        part_number: 'TP-001',
        manufacturer: 'Test Manufacturer',
        description: 'A test part description',
        specifications: { color: 'black' },
        images: [],
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        category: {
            id: 1,
            name: 'Test Category',
            slug: 'test-category',
            description: 'Test category description',
            parent_id: null,
            is_active: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
        },
        models: [],
    };

    it('renders sales button when both name and URL are provided', () => {
        const partWithSalesButton: Part = {
            ...basePart,
            sales_button_name: 'Buy Now',
            sales_button_url: 'https://example.com/buy',
        };

        render(
            <PartDetails 
                part={partWithSalesButton} 
                guestSearchConfig={{
                    enable_partial_results: false,
                    guest_max_visible_results: 5,
                    guest_search_limit: 10,
                    guest_search_enabled: true,
                }}
            />
        );

        const salesButton = screen.getByRole('link', { name: /buy now/i });
        expect(salesButton).toBeInTheDocument();
        expect(salesButton).toHaveAttribute('href', 'https://example.com/buy');
        expect(salesButton).toHaveAttribute('target', '_blank');
        expect(salesButton).toHaveAttribute('rel', 'noopener noreferrer');
    });

    it('does not render sales button when name is missing', () => {
        const partWithoutName: Part = {
            ...basePart,
            sales_button_name: null,
            sales_button_url: 'https://example.com/buy',
        };

        render(
            <PartDetails 
                part={partWithoutName} 
                guestSearchConfig={{
                    enable_partial_results: false,
                    guest_max_visible_results: 5,
                    guest_search_limit: 10,
                    guest_search_enabled: true,
                }}
            />
        );

        expect(screen.queryByRole('link', { name: /buy/i })).not.toBeInTheDocument();
    });

    it('does not render sales button when URL is missing', () => {
        const partWithoutUrl: Part = {
            ...basePart,
            sales_button_name: 'Buy Now',
            sales_button_url: null,
        };

        render(
            <PartDetails 
                part={partWithoutUrl} 
                guestSearchConfig={{
                    enable_partial_results: false,
                    guest_max_visible_results: 5,
                    guest_search_limit: 10,
                    guest_search_enabled: true,
                }}
            />
        );

        expect(screen.queryByRole('link', { name: /buy now/i })).not.toBeInTheDocument();
    });

    it('does not render sales button when both name and URL are missing', () => {
        const partWithoutSalesButton: Part = {
            ...basePart,
            sales_button_name: null,
            sales_button_url: null,
        };

        render(
            <PartDetails 
                part={partWithoutSalesButton} 
                guestSearchConfig={{
                    enable_partial_results: false,
                    guest_max_visible_results: 5,
                    guest_search_limit: 10,
                    guest_search_enabled: true,
                }}
            />
        );

        expect(screen.queryByRole('link', { name: /buy/i })).not.toBeInTheDocument();
    });

    it('renders sales button with custom text', () => {
        const partWithCustomButton: Part = {
            ...basePart,
            sales_button_name: 'Shop Here',
            sales_button_url: 'https://shop.example.com',
        };

        render(
            <PartDetails 
                part={partWithCustomButton} 
                guestSearchConfig={{
                    enable_partial_results: false,
                    guest_max_visible_results: 5,
                    guest_search_limit: 10,
                    guest_search_enabled: true,
                }}
            />
        );

        const salesButton = screen.getByRole('link', { name: /shop here/i });
        expect(salesButton).toBeInTheDocument();
        expect(salesButton).toHaveAttribute('href', 'https://shop.example.com');
    });

    it('renders sales button with proper styling classes', () => {
        const partWithSalesButton: Part = {
            ...basePart,
            sales_button_name: 'Buy Now',
            sales_button_url: 'https://example.com/buy',
        };

        render(
            <PartDetails 
                part={partWithSalesButton} 
                guestSearchConfig={{
                    enable_partial_results: false,
                    guest_max_visible_results: 5,
                    guest_search_limit: 10,
                    guest_search_enabled: true,
                }}
            />
        );

        const salesButton = screen.getByRole('link', { name: /buy now/i });
        expect(salesButton).toHaveClass('inline-block');
        
        // Check if the button inside has the correct styling
        const buttonElement = salesButton.querySelector('button');
        expect(buttonElement).toHaveClass('bg-orange-600', 'hover:bg-orange-700', 'text-white');
    });

    it('renders external link icon in sales button', () => {
        const partWithSalesButton: Part = {
            ...basePart,
            sales_button_name: 'Buy Now',
            sales_button_url: 'https://example.com/buy',
        };

        render(
            <PartDetails 
                part={partWithSalesButton} 
                guestSearchConfig={{
                    enable_partial_results: false,
                    guest_max_visible_results: 5,
                    guest_search_limit: 10,
                    guest_search_enabled: true,
                }}
            />
        );

        // Check if the external link icon is present
        const salesButton = screen.getByRole('link', { name: /buy now/i });
        const iconElement = salesButton.querySelector('svg');
        expect(iconElement).toBeInTheDocument();
    });
});
