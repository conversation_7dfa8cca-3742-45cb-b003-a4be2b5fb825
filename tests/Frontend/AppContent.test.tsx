import { AppContent } from '@/components/app-content';
import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';

describe('AppContent', () => {
  it('should include mx-auto class for centering when not fullWidth', () => {
    const { container } = render(
      <AppContent>
        <div>Test content</div>
      </AppContent>
    );

    const mainElement = container.querySelector('main');
    expect(mainElement).toHaveClass('mx-auto');
    expect(mainElement).toHaveClass('max-w-7xl');
    expect(mainElement).toHaveClass('flex');
    expect(mainElement).toHaveClass('h-full');
    expect(mainElement).toHaveClass('w-full');
    expect(mainElement).toHaveClass('flex-1');
    expect(mainElement).toHaveClass('flex-col');
    expect(mainElement).toHaveClass('gap-4');
  });

  it('should not include mx-auto class when fullWidth is true', () => {
    const { container } = render(
      <AppContent fullWidth={true}>
        <div>Test content</div>
      </AppContent>
    );

    const mainElement = container.querySelector('main');
    expect(mainElement).not.toHaveClass('mx-auto');
    expect(mainElement).not.toHaveClass('max-w-7xl');
    expect(mainElement).toHaveClass('flex');
    expect(mainElement).toHaveClass('h-full');
    expect(mainElement).toHaveClass('w-full');
    expect(mainElement).toHaveClass('flex-1');
    expect(mainElement).toHaveClass('flex-col');
    expect(mainElement).toHaveClass('gap-4');
  });

  it('should render SidebarInset when variant is sidebar', () => {
    const { container } = render(
      <AppContent variant="sidebar">
        <div>Test content</div>
      </AppContent>
    );

    // SidebarInset renders a main element with different classes
    const mainElement = container.querySelector('main');
    expect(mainElement).toHaveAttribute('data-slot', 'sidebar-inset');

    // Should contain the test content
    expect(container.textContent).toContain('Test content');
  });

  it('should pass through additional props to main element', () => {
    const { container } = render(
      <AppContent data-testid="app-content" className="custom-class">
        <div>Test content</div>
      </AppContent>
    );

    const mainElement = container.querySelector('main');
    expect(mainElement).toHaveAttribute('data-testid', 'app-content');
    expect(mainElement).toHaveClass('custom-class');
  });

  it('should render children correctly', () => {
    const { container } = render(
      <AppContent>
        <div data-testid="child-content">Test child content</div>
      </AppContent>
    );

    expect(container.querySelector('[data-testid="child-content"]')).toBeInTheDocument();
    expect(container.textContent).toContain('Test child content');
  });
});
