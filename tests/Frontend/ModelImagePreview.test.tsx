import ModelImagePreview from '@/components/model-image-preview';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the Dialog component
vi.mock('@/components/ui/dialog', () => ({
    Dialog: ({ children, open }: { children: React.ReactNode; open: boolean }) =>
        open ? <div data-testid="dialog">{children}</div> : null,
    DialogContent: ({ children }: { children: React.ReactNode }) =>
        <div data-testid="dialog-content">{children}</div>,
    DialogTitle: ({ children }: { children: React.ReactNode }) =>
        <div data-testid="dialog-title">{children}</div>,
}));

// Mock the Card components
vi.mock('@/components/ui/card', () => ({
    Card: ({ children, className }: { children: React.ReactNode; className?: string }) =>
        <div data-testid="card" className={className}>{children}</div>,
    CardContent: ({ children }: { children: React.ReactNode }) =>
        <div data-testid="card-content">{children}</div>,
}));

// Mock the Button component
vi.mock('@/components/ui/button', () => ({
    Button: ({ children, onClick, className }: { children: React.ReactNode; onClick?: () => void; className?: string }) =>
        <button data-testid="button" onClick={onClick} className={className}>{children}</button>,
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
    ZoomIn: () => <div data-testid="zoom-in-icon">ZoomIn</div>,
    X: () => <div data-testid="x-icon">X</div>,
    ChevronLeft: () => <div data-testid="chevron-left-icon">ChevronLeft</div>,
    ChevronRight: () => <div data-testid="chevron-right-icon">ChevronRight</div>,
    Smartphone: () => <div data-testid="smartphone-icon">Smartphone</div>,
    Image: () => <div data-testid="image-icon">Image</div>,
}));

// Mock utils
vi.mock('@/lib/utils', () => ({
    cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

const mockModel = {
    id: 1,
    name: 'iPhone 14',
    image_url: 'https://example.com/iphone14.jpg',
    images: ['https://example.com/iphone14.jpg', 'https://example.com/iphone14-2.jpg'],
    brand: {
        id: 1,
        name: 'Apple',
        logo_url: 'https://example.com/apple-logo.jpg',
    },
};

const mockModelWithoutImage = {
    id: 2,
    name: 'Samsung Galaxy',
    image_url: undefined,
    images: undefined,
    brand: {
        id: 2,
        name: 'Samsung',
        logo_url: 'https://example.com/samsung-logo.jpg',
    },
};

const mockModelWithoutBrandLogo = {
    id: 3,
    name: 'Generic Phone',
    image_url: undefined,
    images: undefined,
    brand: {
        id: 3,
        name: 'Generic',
        logo_url: undefined,
    },
};

describe('ModelImagePreview', () => {
    beforeEach(() => {
        // Mock document.body.style with all necessary methods
        Object.defineProperty(document.body, 'style', {
            value: {
                overflow: '',
                removeProperty: vi.fn(),
                setProperty: vi.fn(),
            },
            writable: true,
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('renders with model image', () => {
        render(<ModelImagePreview model={mockModel} />);
        
        expect(screen.getByTestId('card')).toBeInTheDocument();
        expect(screen.getByTestId('card-content')).toBeInTheDocument();
        
        const image = screen.getByAltText('Apple iPhone 14');
        expect(image).toBeInTheDocument();
        expect(image).toHaveAttribute('src', 'https://example.com/iphone14.jpg');
    });

    it('renders fallback when no image is available', () => {
        render(<ModelImagePreview model={mockModelWithoutImage} />);

        // Should show brand logo when available
        const brandLogo = screen.getByAltText('Samsung');
        expect(brandLogo).toBeInTheDocument();
        expect(brandLogo).toHaveAttribute('src', 'https://example.com/samsung-logo.jpg');

        expect(screen.getByText('No image available')).toBeInTheDocument();
        // Check for the text in the fallback area specifically
        const fallbackText = screen.getAllByText('Samsung Samsung Galaxy')[0];
        expect(fallbackText).toBeInTheDocument();
    });

    it('shows image counter for multiple images', () => {
        render(<ModelImagePreview model={mockModel} />);
        
        expect(screen.getByText('1/2')).toBeInTheDocument();
    });

    it('opens modal when image is clicked', async () => {
        render(<ModelImagePreview model={mockModel} />);
        
        const imageContainer = screen.getByAltText('Apple iPhone 14').closest('div');
        fireEvent.click(imageContainer!);
        
        await waitFor(() => {
            expect(screen.getByTestId('dialog')).toBeInTheDocument();
        });
    });

    it('shows navigation arrows for multiple images', () => {
        render(<ModelImagePreview model={mockModel} />);
        
        const buttons = screen.getAllByTestId('button');
        const navigationButtons = buttons.filter(button => 
            button.querySelector('[data-testid="chevron-left-icon"]') || 
            button.querySelector('[data-testid="chevron-right-icon"]')
        );
        
        expect(navigationButtons).toHaveLength(2);
    });

    it('navigates between images', async () => {
        render(<ModelImagePreview model={mockModel} />);
        
        // Click next button
        const nextButton = screen.getByTestId('chevron-right-icon').closest('button');
        fireEvent.click(nextButton!);
        
        await waitFor(() => {
            expect(screen.getByText('2/2')).toBeInTheDocument();
        });
    });

    it('shows click instruction for images', () => {
        render(<ModelImagePreview model={mockModel} />);
        
        expect(screen.getByText('Click to view full size')).toBeInTheDocument();
    });

    it('does not show click instruction when no image', () => {
        render(<ModelImagePreview model={mockModelWithoutImage} />);
        
        expect(screen.queryByText('Click to view full size')).not.toBeInTheDocument();
    });

    it('handles image error gracefully', () => {
        render(<ModelImagePreview model={mockModel} />);

        const image = screen.getByAltText('Apple iPhone 14');
        fireEvent.error(image);

        // Should show fallback content with brand logo
        const brandLogo = screen.getByAltText('Apple');
        expect(brandLogo).toBeInTheDocument();
        expect(brandLogo).toHaveAttribute('src', 'https://example.com/apple-logo.jpg');
    });

    it('closes modal when close button is clicked', async () => {
        render(<ModelImagePreview model={mockModel} />);
        
        // Open modal
        const imageContainer = screen.getByAltText('Apple iPhone 14').closest('div');
        fireEvent.click(imageContainer!);
        
        await waitFor(() => {
            expect(screen.getByTestId('dialog')).toBeInTheDocument();
        });
        
        // Close modal
        const closeButton = screen.getByTestId('x-icon').closest('button');
        fireEvent.click(closeButton!);
        
        await waitFor(() => {
            expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
        });
    });

    it('handles keyboard navigation in modal', async () => {
        render(<ModelImagePreview model={mockModel} />);
        
        // Open modal
        const imageContainer = screen.getByAltText('Apple iPhone 14').closest('div');
        fireEvent.click(imageContainer!);
        
        await waitFor(() => {
            expect(screen.getByTestId('dialog')).toBeInTheDocument();
        });
        
        // Test arrow key navigation
        fireEvent.keyDown(document, { key: 'ArrowRight' });
        fireEvent.keyDown(document, { key: 'ArrowLeft' });
        fireEvent.keyDown(document, { key: 'Escape' });
        
        // Modal should close on Escape
        await waitFor(() => {
            expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
        });
    });

    it('prevents body scroll when modal is open', async () => {
        render(<ModelImagePreview model={mockModel} />);
        
        // Open modal
        const imageContainer = screen.getByAltText('Apple iPhone 14').closest('div');
        fireEvent.click(imageContainer!);
        
        await waitFor(() => {
            expect(document.body.style.overflow).toBe('hidden');
        });
    });

    it('restores body scroll when modal is closed', async () => {
        render(<ModelImagePreview model={mockModel} />);
        
        // Open modal
        const imageContainer = screen.getByAltText('Apple iPhone 14').closest('div');
        fireEvent.click(imageContainer!);
        
        await waitFor(() => {
            expect(document.body.style.overflow).toBe('hidden');
        });
        
        // Close modal
        const closeButton = screen.getByTestId('x-icon').closest('button');
        fireEvent.click(closeButton!);
        
        await waitFor(() => {
            expect(document.body.style.overflow).toBe('unset');
        });
    });

    it('applies custom className', () => {
        render(<ModelImagePreview model={mockModel} className="custom-class" />);
        
        const card = screen.getByTestId('card');
        expect(card).toHaveClass('custom-class');
    });

    it('shows brand logo in fallback when available', () => {
        render(<ModelImagePreview model={mockModelWithoutImage} />);

        const brandLogo = screen.getByAltText('Samsung');
        expect(brandLogo).toBeInTheDocument();
        expect(brandLogo).toHaveAttribute('src', 'https://example.com/samsung-logo.jpg');
    });

    it('shows smartphone icon when no brand logo available', () => {
        render(<ModelImagePreview model={mockModelWithoutBrandLogo} />);

        expect(screen.getByTestId('smartphone-icon')).toBeInTheDocument();
        expect(screen.getByText('No image available')).toBeInTheDocument();
        // Check for the text in the fallback area specifically
        const fallbackText = screen.getAllByText('Generic Generic Phone')[0];
        expect(fallbackText).toBeInTheDocument();
    });
});
