import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { api, apiRequest, handleApiError, hasApiData } from '@/utils/api';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock document and window
Object.defineProperty(global, 'document', {
    value: {
        querySelector: vi.fn(),
        cookie: ''
    },
    writable: true
});

Object.defineProperty(global, 'window', {
    value: {
        page: {
            props: {
                auth: {
                    user: { id: 1 }
                }
            }
        }
    },
    writable: true
});

describe('API Utility', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        document.cookie = '';
        (document.querySelector as any).mockReturnValue(null);
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('apiRequest', () => {
        it('makes successful GET request', async () => {
            const mockResponse = { data: 'test' };
            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Map([['content-type', 'application/json']]),
                json: () => Promise.resolve(mockResponse)
            });

            const result = await apiRequest('/test-endpoint');

            expect(mockFetch).toHaveBeenCalledWith('/test-endpoint', {
                credentials: 'same-origin',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            expect(result).toEqual({
                data: mockResponse,
                error: undefined,
                status: 200,
                ok: true
            });
        });

        it('handles authentication check', async () => {
            // Mock unauthenticated user
            (global as any).window.page.props.auth.user = null;

            const result = await apiRequest('/test-endpoint', { requireAuth: true });

            expect(result).toEqual({
                data: undefined,
                error: 'User not authenticated',
                status: 401,
                ok: false
            });

            expect(mockFetch).not.toHaveBeenCalled();
        });

        it('adds CSRF token for POST requests', async () => {
            const mockToken = 'test-csrf-token';
            (document.querySelector as any).mockReturnValue({
                content: mockToken
            });

            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Map([['content-type', 'application/json']]),
                json: () => Promise.resolve({})
            });

            await apiRequest('/test-endpoint', { method: 'POST' });

            expect(mockFetch).toHaveBeenCalledWith('/test-endpoint', {
                credentials: 'same-origin',
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': mockToken
                }
            });
        });

        it('gets CSRF token from cookie when meta tag is not available', async () => {
            document.cookie = 'XSRF-TOKEN=cookie-csrf-token; other=value';
            (document.querySelector as any).mockReturnValue(null);

            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Map([['content-type', 'application/json']]),
                json: () => Promise.resolve({})
            });

            await apiRequest('/test-endpoint', { method: 'POST' });

            expect(mockFetch).toHaveBeenCalledWith('/test-endpoint', {
                credentials: 'same-origin',
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': 'cookie-csrf-token'
                }
            });
        });

        it('handles HTTP errors', async () => {
            mockFetch.mockResolvedValue({
                ok: false,
                status: 404,
                statusText: 'Not Found',
                headers: new Map(),
                json: () => Promise.resolve({ error: 'Not found' })
            });

            const result = await apiRequest('/test-endpoint');

            expect(result).toEqual({
                data: { error: 'Not found' },
                error: 'HTTP 404: Not Found',
                status: 404,
                ok: false
            });
        });

        it('handles network errors', async () => {
            mockFetch.mockRejectedValue(new Error('Network error'));

            const result = await apiRequest('/test-endpoint');

            expect(result).toEqual({
                data: undefined,
                error: 'Network error',
                status: 0,
                ok: false
            });
        });

        it('handles non-JSON responses', async () => {
            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Map([['content-type', 'text/plain']]),
                json: () => Promise.reject(new Error('Not JSON'))
            });

            const result = await apiRequest('/test-endpoint');

            expect(result).toEqual({
                data: undefined,
                error: undefined,
                status: 200,
                ok: true
            });
        });

        it('skips CSRF token when requested', async () => {
            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Map(),
                json: () => Promise.resolve({})
            });

            await apiRequest('/test-endpoint', { method: 'POST', skipCsrf: true });

            expect(mockFetch).toHaveBeenCalledWith('/test-endpoint', {
                credentials: 'same-origin',
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
        });
    });

    describe('convenience methods', () => {
        beforeEach(() => {
            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Map([['content-type', 'application/json']]),
                json: () => Promise.resolve({ success: true })
            });
        });

        it('api.get works correctly', async () => {
            await api.get('/test');

            expect(mockFetch).toHaveBeenCalledWith('/test', expect.objectContaining({
                method: 'GET'
            }));
        });

        it('api.post works correctly', async () => {
            const data = { test: 'data' };
            await api.post('/test', data);

            expect(mockFetch).toHaveBeenCalledWith('/test', expect.objectContaining({
                method: 'POST',
                body: JSON.stringify(data)
            }));
        });

        it('api.put works correctly', async () => {
            const data = { test: 'data' };
            await api.put('/test', data);

            expect(mockFetch).toHaveBeenCalledWith('/test', expect.objectContaining({
                method: 'PUT',
                body: JSON.stringify(data)
            }));
        });

        it('api.patch works correctly', async () => {
            const data = { test: 'data' };
            await api.patch('/test', data);

            expect(mockFetch).toHaveBeenCalledWith('/test', expect.objectContaining({
                method: 'PATCH',
                body: JSON.stringify(data)
            }));
        });

        it('api.delete works correctly', async () => {
            await api.delete('/test');

            expect(mockFetch).toHaveBeenCalledWith('/test', expect.objectContaining({
                method: 'DELETE'
            }));
        });
    });

    describe('helper functions', () => {
        it('handleApiError logs errors', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
            
            handleApiError('Test error', 'Fallback message');
            
            expect(consoleSpy).toHaveBeenCalledWith('API Error:', 'Test error');
            
            consoleSpy.mockRestore();
        });

        it('handleApiError handles undefined errors', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
            
            handleApiError(undefined, 'Fallback message');
            
            expect(consoleSpy).not.toHaveBeenCalled();
            
            consoleSpy.mockRestore();
        });

        it('hasApiData type guard works correctly', () => {
            const successResponse = {
                ok: true,
                data: { test: 'data' },
                status: 200,
                error: undefined
            };

            const errorResponse = {
                ok: false,
                data: undefined,
                status: 404,
                error: 'Not found'
            };

            expect(hasApiData(successResponse)).toBe(true);
            expect(hasApiData(errorResponse)).toBe(false);
        });
    });

    describe('authentication detection', () => {
        it('detects authenticated user', async () => {
            (global as any).window.page.props.auth.user = { id: 1 };

            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Map(),
                json: () => Promise.resolve({})
            });

            const result = await apiRequest('/test', { requireAuth: true });

            expect(result.ok).toBe(true);
            expect(mockFetch).toHaveBeenCalled();
        });

        it('detects unauthenticated user', async () => {
            (global as any).window.page.props.auth.user = null;

            const result = await apiRequest('/test', { requireAuth: true });

            expect(result.ok).toBe(false);
            expect(result.status).toBe(401);
            expect(mockFetch).not.toHaveBeenCalled();
        });

        it('handles missing auth data gracefully', async () => {
            (global as any).window.page = undefined;

            const result = await apiRequest('/test', { requireAuth: true });

            expect(result.ok).toBe(false);
            expect(result.status).toBe(401);
        });
    });
});
