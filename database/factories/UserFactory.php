<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'subscription_plan' => fake()->randomElement(['free', 'premium']),
            'search_count' => fake()->numberBetween(0, 20),
            'daily_reset' => today(),
            'status' => 'active', // Default to active for tests
            'approval_status' => 'approved', // Default to approved for tests
            'login_count' => fake()->numberBetween(0, 100),
            'last_login_at' => fake()->optional()->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Create an admin user.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'email' => '<EMAIL>',
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
            'is_admin' => true,
            'role' => 'admin',
        ]);
    }

    /**
     * Create a suspended user.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'Test suspension',
        ]);
    }

    /**
     * Create a pending approval user.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'approval_status' => 'pending',
        ]);
    }
}
