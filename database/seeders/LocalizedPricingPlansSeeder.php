<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PricingPlan;
use Illuminate\Support\Facades\DB;

class LocalizedPricingPlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // USD to BDT conversion rate (approximate)
        $usdToBdt = 110; // 1 USD = 110 BDT (approximate)

        // Get existing USD plans
        $usdPlans = PricingPlan::where('currency', 'USD')->get();

        foreach ($usdPlans as $usdPlan) {
            // Skip if BDT version already exists
            $existingBdtPlan = PricingPlan::where('name', $usdPlan->name . '_bd')
                ->where('currency', 'BDT')
                ->first();

            if ($existingBdtPlan) {
                $this->command->info("BDT plan already exists for: {$usdPlan->name}");
                continue;
            }

            // Calculate BDT price
            $bdtPrice = $usdPlan->price * $usdToBdt;

            // Create BDT version
            $bdtPlan = PricingPlan::create([
                'name' => $usdPlan->name . '_bd',
                'display_name' => $usdPlan->display_name . ' (Bangladesh)',
                'description' => $usdPlan->description . ' - Localized for Bangladesh',
                'price' => $bdtPrice,
                'currency' => 'BDT',
                'interval' => $usdPlan->interval,
                'features' => $usdPlan->features,
                'search_limit' => $usdPlan->search_limit,
                'model_view_limit' => $usdPlan->model_view_limit,
                'parts_per_model_limit' => $usdPlan->parts_per_model_limit,
                'brand_search_enabled' => $usdPlan->brand_search_enabled,
                'model_search_enabled' => $usdPlan->model_search_enabled,
                'unlimited_model_access' => $usdPlan->unlimited_model_access,
                'is_active' => $usdPlan->is_active,
                'is_public' => $usdPlan->is_public,
                'is_default' => false, // Don't make BDT plans default
                'is_popular' => $usdPlan->is_popular,
                'sort_order' => $usdPlan->sort_order,
                'metadata' => array_merge($usdPlan->metadata ?? [], [
                    'localized_for' => 'BD',
                    'base_plan_id' => $usdPlan->id,
                    'conversion_rate' => $usdToBdt,
                ]),

                // Payment gateway configurations for Bangladesh
                'shurjopay_price_id_monthly' => null, // To be configured later
                'shurjopay_price_id_yearly' => null,
                'shurjopay_product_id' => null,
                'online_payment_enabled' => true,
                'offline_payment_enabled' => true,
                'crypto_payment_enabled' => false, // Disable crypto for BDT plans

                // Fee configuration (can be customized for Bangladesh)
                'shurjopay_fee_percentage' => 2.5,
                'shurjopay_fee_fixed' => 5.00,
                'offline_fee_percentage' => 0.0,
                'offline_fee_fixed' => 0.0,
                'tax_percentage' => 0.0, // No tax for now
                'show_fees_breakdown' => true,
                'tax_inclusive' => false,
            ]);

            $this->command->info("Created BDT plan: {$bdtPlan->name} (৳{$bdtPrice})");
        }

        $this->command->info('Localized pricing plans seeding completed!');
    }
}
