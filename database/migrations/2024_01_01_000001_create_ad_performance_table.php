<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ad_performance', function (Blueprint $table) {
            $table->id();
            $table->string('zone'); // header, sidebar, content, footer, mobile
            $table->string('page'); // page URL or route name
            $table->string('ad_slot_id')->nullable(); // Google AdSense ad slot ID
            $table->enum('user_type', ['free', 'guest', 'premium', 'admin'])->default('free');
            $table->enum('device_type', ['desktop', 'tablet', 'mobile'])->default('desktop');
            $table->integer('impressions')->default(0);
            $table->integer('clicks')->default(0);
            $table->decimal('revenue', 10, 4)->default(0); // Revenue in USD
            $table->decimal('cpm', 8, 4)->default(0); // Cost per mille
            $table->decimal('ctr', 5, 4)->default(0); // Click-through rate (percentage)
            $table->date('date'); // Date for aggregation
            $table->json('metadata')->nullable(); // Additional tracking data
            $table->timestamps();

            $table->index(['zone', 'date']);
            $table->index(['page', 'date']);
            $table->index(['user_type', 'date']);
            $table->index(['device_type', 'date']);
            $table->unique(['zone', 'page', 'user_type', 'device_type', 'date'], 'ad_performance_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_performance');
    }
};
