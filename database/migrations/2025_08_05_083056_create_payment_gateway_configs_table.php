<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_gateway_configs', function (Blueprint $table) {
            $table->id();
            $table->string('gateway_name')->unique(); // paddle, shurjopay, coinbase_commerce, offline
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->boolean('is_enabled')->default(true);
            $table->boolean('is_available_globally')->default(true);
            $table->json('supported_countries')->nullable(); // ['BD'] for shurjopay, ['*'] for others
            $table->json('supported_currencies')->nullable(); // ['BDT'] for shurjopay, ['USD'] for others
            $table->string('primary_currency')->nullable(); // BDT for shurjopay, USD for others
            $table->integer('sort_order')->default(0);
            $table->json('configuration')->nullable(); // Additional gateway-specific config
            $table->json('features')->nullable(); // Gateway features/capabilities
            $table->boolean('requires_api_keys')->default(true);
            $table->boolean('supports_subscriptions')->default(true);
            $table->boolean('supports_one_time_payments')->default(true);
            $table->text('admin_notes')->nullable();
            $table->timestamp('last_configured_at')->nullable();
            $table->string('configured_by')->nullable(); // User who last configured
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_gateway_configs');
    }
};
