<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ad_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('zone'); // header, sidebar, content, footer, mobile
            $table->boolean('enabled')->default(true);
            $table->json('page_types')->nullable(); // ['home', 'search', 'dashboard', etc.]
            $table->json('user_types')->nullable(); // ['free', 'guest']
            $table->json('frequency_rules')->nullable(); // max_ads_per_page, delay_seconds, etc.
            $table->json('targeting_rules')->nullable(); // device_type, geographic, etc.
            $table->string('ad_slot_id')->nullable(); // Google AdSense ad slot ID
            $table->string('ad_format')->default('auto'); // auto, fixed, responsive
            $table->json('ad_sizes')->nullable(); // responsive size configurations
            $table->integer('priority')->default(1); // 1 = highest priority
            $table->timestamps();

            $table->index(['zone', 'enabled']);
            $table->index(['enabled', 'priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_configurations');
    }
};
