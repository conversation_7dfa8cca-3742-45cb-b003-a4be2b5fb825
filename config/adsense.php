<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Google AdSense Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for Google AdSense
    | integration. You can enable/disable AdSense, configure zones,
    | and set frequency limits for ad display.
    |
    */

    'enabled' => env('GOOGLE_ADSENSE_ENABLED', false),

    'client_id' => env('GOOGLE_ADSENSE_CLIENT_ID', ''),

    'auto_ads' => env('GOOGLE_ADSENSE_AUTO_ADS', false),

    'debug' => env('GOOGLE_ADSENSE_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | AdSense Zone Configuration
    |--------------------------------------------------------------------------
    |
    | Configure which zones are enabled for displaying ads.
    | Available zones: header, sidebar, content, footer, mobile
    |
    */

    'zones' => [
        'header' => env('ADSENSE_HEADER_ENABLED', true),
        'sidebar' => env('ADSENSE_SIDEBAR_ENABLED', true),
        'content' => env('ADSENSE_CONTENT_ENABLED', true),
        'footer' => env('ADSENSE_FOOTER_ENABLED', true),
        'mobile' => env('ADSENSE_MOBILE_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | AdSense Frequency Settings
    |--------------------------------------------------------------------------
    |
    | Configure frequency limits and timing for ad display.
    |
    */

    'frequency' => [
        'max_ads_per_page' => env('ADSENSE_MAX_ADS_PER_PAGE', 4),
        'delay_seconds' => env('ADSENSE_DELAY_SECONDS', 3),
        'grace_period_minutes' => env('ADSENSE_GRACE_PERIOD_MINUTES', 0),
    ],

    /*
    |--------------------------------------------------------------------------
    | AdSense Performance Tracking
    |--------------------------------------------------------------------------
    |
    | Configure performance tracking and analytics settings.
    |
    */

    'tracking' => [
        'enabled' => env('ADSENSE_TRACKING_ENABLED', true),
        'sample_rate' => env('ADSENSE_TRACKING_SAMPLE_RATE', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | AdSense Cache Settings
    |--------------------------------------------------------------------------
    |
    | Configure caching for AdSense configuration and performance data.
    |
    */

    'cache' => [
        'config_ttl' => env('ADSENSE_CONFIG_CACHE_TTL', 3600), // 1 hour
        'performance_ttl' => env('ADSENSE_PERFORMANCE_CACHE_TTL', 300), // 5 minutes
    ],
];
