<?php

use App\Http\Controllers\Admin\UserImpersonationController;
use App\Http\Controllers\User\NotificationController as UserNotificationController;
use App\Http\Controllers\Api\LocalizationController;
use App\Http\Controllers\Api\AdTrackingController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Impersonation API endpoints moved to web routes for session compatibility

// Notification API endpoints - These require authentication and return JSON responses
Route::middleware(['auth:web'])->prefix('notifications')->name('api.notifications.')->group(function () {
    Route::get('unread-count', [UserNotificationController::class, 'getUnreadCount'])->name('unread-count');
    Route::get('recent', [UserNotificationController::class, 'getRecent'])->name('recent');
});

// Localization API endpoints
Route::prefix('localization')->name('api.localization.')->group(function () {
    Route::get('data', [LocalizationController::class, 'getLocalizationData'])->name('data');
    Route::get('pricing-plans', [LocalizationController::class, 'getPricingPlans'])->name('pricing-plans');
    Route::get('payment-gateways', [LocalizationController::class, 'getPaymentGateways'])->name('payment-gateways');
    Route::get('country-info', [LocalizationController::class, 'getCountryInfo'])->name('country-info');
    Route::get('stats', [LocalizationController::class, 'getLocalizationStats'])->name('stats');

    // Admin/testing endpoints
    Route::middleware(['auth'])->group(function () {
        Route::post('test-detection', [LocalizationController::class, 'testCountryDetection'])->name('test-detection');
    });
});

// Ad Tracking API endpoints
Route::prefix('ads')->name('api.ads.')->group(function () {
    // Public endpoints (no authentication required for tracking)
    Route::post('track-impression', [AdTrackingController::class, 'trackImpression'])->name('track-impression');
    Route::post('track-click', [AdTrackingController::class, 'trackClick'])->name('track-click');
    Route::get('should-show', [AdTrackingController::class, 'shouldShowAds'])->name('should-show');

    // Configuration endpoints (can be used by authenticated or guest users)
    Route::get('configuration', [AdTrackingController::class, 'getConfiguration'])->name('configuration');
    Route::get('page-configurations', [AdTrackingController::class, 'getPageConfigurations'])->name('page-configurations');
});
