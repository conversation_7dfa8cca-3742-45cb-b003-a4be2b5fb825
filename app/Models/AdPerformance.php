<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdPerformance extends Model
{
    use HasFactory;

    protected $table = 'ad_performance';

    protected $fillable = [
        'zone',
        'page',
        'ad_slot_id',
        'user_type',
        'device_type',
        'impressions',
        'clicks',
        'revenue',
        'cpm',
        'ctr',
        'date',
        'metadata',
    ];

    protected $casts = [
        'impressions' => 'integer',
        'clicks' => 'integer',
        'revenue' => 'decimal:4',
        'cpm' => 'decimal:4',
        'ctr' => 'decimal:4',
        'date' => 'date',
        'metadata' => 'array',
    ];

    /**
     * Get the ad configuration for this performance record.
     */
    public function configuration(): BelongsTo
    {
        return $this->belongsTo(AdConfiguration::class, 'zone', 'zone');
    }

    /**
     * Scope to get performance for a specific date range.
     */
    public function scopeDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('date', [$startDate->toDateString(), $endDate->toDateString()]);
    }

    /**
     * Scope to get performance by zone.
     */
    public function scopeByZone($query, string $zone)
    {
        return $query->where('zone', $zone);
    }

    /**
     * Scope to get performance by page.
     */
    public function scopeByPage($query, string $page)
    {
        return $query->where('page', $page);
    }

    /**
     * Scope to get performance by user type.
     */
    public function scopeByUserType($query, string $userType)
    {
        return $query->where('user_type', $userType);
    }

    /**
     * Scope to get performance by device type.
     */
    public function scopeByDeviceType($query, string $deviceType)
    {
        return $query->where('device_type', $deviceType);
    }

    /**
     * Calculate and update CTR.
     */
    public function updateCtr(): void
    {
        if ($this->impressions > 0) {
            $this->ctr = ($this->clicks / $this->impressions) * 100;
            $this->save();
        }
    }

    /**
     * Calculate and update CPM.
     */
    public function updateCpm(): void
    {
        if ($this->impressions > 0) {
            $this->cpm = ($this->revenue / $this->impressions) * 1000;
            $this->save();
        }
    }

    /**
     * Record an impression.
     */
    public static function recordImpression(
        string $zone,
        string $page,
        string $userType = 'free',
        string $deviceType = 'desktop',
        ?string $adSlotId = null,
        array $metadata = []
    ): void {
        $date = now()->toDateString();

        // Use database upsert to handle race conditions properly
        DB::table('ad_performance')->upsert(
            [
                [
                    'zone' => $zone,
                    'page' => $page,
                    'user_type' => $userType,
                    'device_type' => $deviceType,
                    'date' => $date,
                    'ad_slot_id' => $adSlotId,
                    'impressions' => 1,
                    'clicks' => 0,
                    'revenue' => 0,
                    'metadata' => json_encode($metadata),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ],
            ['zone', 'page', 'user_type', 'device_type', 'date'], // Unique columns
            ['impressions' => DB::raw('impressions + 1'), 'metadata', 'updated_at'] // Columns to update
        );

        // Update CTR for the record
        $record = self::where([
            'zone' => $zone,
            'page' => $page,
            'user_type' => $userType,
            'device_type' => $deviceType,
            'date' => $date,
        ])->first();

        if ($record) {
            $record->updateCtr();
        }
    }

    /**
     * Record a click.
     */
    public static function recordClick(
        string $zone,
        string $page,
        string $userType = 'free',
        string $deviceType = 'desktop',
        ?string $adSlotId = null,
        array $metadata = []
    ): void {
        $date = now()->toDateString();

        // Use database upsert to handle race conditions properly
        DB::table('ad_performance')->upsert(
            [
                [
                    'zone' => $zone,
                    'page' => $page,
                    'user_type' => $userType,
                    'device_type' => $deviceType,
                    'date' => $date,
                    'ad_slot_id' => $adSlotId,
                    'impressions' => 0,
                    'clicks' => 1,
                    'revenue' => 0,
                    'metadata' => json_encode($metadata),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ],
            ['zone', 'page', 'user_type', 'device_type', 'date'], // Unique columns
            ['clicks' => DB::raw('clicks + 1'), 'metadata', 'updated_at'] // Columns to update
        );

        // Update CTR for the record
        $record = self::where([
            'zone' => $zone,
            'page' => $page,
            'user_type' => $userType,
            'device_type' => $deviceType,
            'date' => $date,
        ])->first();

        if ($record) {
            $record->updateCtr();
        }
    }

    /**
     * Update revenue for a specific record.
     */
    public static function updateRevenue(
        string $zone,
        string $page,
        float $revenue,
        string $userType = 'free',
        string $deviceType = 'desktop',
        ?Carbon $date = null
    ): void {
        $record = self::where([
            'zone' => $zone,
            'page' => $page,
            'user_type' => $userType,
            'device_type' => $deviceType,
            'date' => $date ? $date->toDateString() : now()->toDateString(),
        ])->first();

        if ($record) {
            $record->revenue += $revenue;
            $record->updateCpm();
            $record->save();
        }
    }

    /**
     * Get aggregated performance data.
     */
    public static function getAggregatedData(
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        ?string $zone = null,
        ?string $page = null
    ): array {
        $query = self::query();

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        if ($zone) {
            $query->byZone($zone);
        }

        if ($page) {
            $query->byPage($page);
        }

        $data = $query->selectRaw('
            SUM(impressions) as total_impressions,
            SUM(clicks) as total_clicks,
            SUM(revenue) as total_revenue,
            AVG(ctr) as avg_ctr,
            AVG(cpm) as avg_cpm,
            COUNT(DISTINCT date) as days_active
        ')->first();

        return [
            'total_impressions' => $data->total_impressions ?? 0,
            'total_clicks' => $data->total_clicks ?? 0,
            'total_revenue' => round($data->total_revenue ?? 0, 2),
            'avg_ctr' => round($data->avg_ctr ?? 0, 2),
            'avg_cpm' => round($data->avg_cpm ?? 0, 2),
            'days_active' => $data->days_active ?? 0,
        ];
    }

    /**
     * Get performance trends over time.
     */
    public static function getPerformanceTrends(
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        string $groupBy = 'date'
    ): array {
        $query = self::query();

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        return $query->selectRaw("
            {$groupBy},
            SUM(impressions) as impressions,
            SUM(clicks) as clicks,
            SUM(revenue) as revenue,
            AVG(ctr) as ctr,
            AVG(cpm) as cpm
        ")
        ->groupBy($groupBy)
        ->orderBy($groupBy)
        ->get()
        ->toArray();
    }
}
