<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class MetaPixelConfig extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'enabled',
        'pixel_id',
        'access_token',
        'conversions_api_enabled',
        'test_event_code',
        'enabled_events',
        'custom_events',
        'respect_do_not_track',
        'require_consent',
        'consent_settings',
        'debug_mode',
        'automatic_matching',
        'custom_parameters',
        'enable_deduplication',
        'deduplication_method',
        'lazy_load',
        'event_batch_size',
        'event_delay_ms',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'enabled' => 'boolean',
        'conversions_api_enabled' => 'boolean',
        'enabled_events' => 'array',
        'custom_events' => 'array',
        'respect_do_not_track' => 'boolean',
        'require_consent' => 'boolean',
        'consent_settings' => 'array',
        'debug_mode' => 'boolean',
        'automatic_matching' => 'boolean',
        'custom_parameters' => 'array',
        'enable_deduplication' => 'boolean',
        'lazy_load' => 'boolean',
        'event_batch_size' => 'integer',
        'event_delay_ms' => 'integer',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'access_token',
    ];

    /**
     * Default enabled events for Meta Pixel.
     *
     * @var array<string>
     */
    public static $defaultEnabledEvents = [
        'PageView',
        'ViewContent',
        'Search',
        'AddToCart',
        'InitiateCheckout',
        'Purchase',
        'Lead',
        'CompleteRegistration',
    ];

    /**
     * Default consent settings.
     *
     * @var array<string, string>
     */
    public static $defaultConsentSettings = [
        'ad_storage' => 'denied',
        'analytics_storage' => 'denied',
        'ad_user_data' => 'denied',
        'ad_personalization' => 'denied',
        'functionality_storage' => 'granted',
        'security_storage' => 'granted',
    ];

    /**
     * Get the current Meta Pixel configuration.
     * Uses singleton pattern to ensure only one configuration exists.
     */
    public static function current(): self
    {
        return Cache::remember('meta_pixel_config', 3600, function () {
            try {
                return self::first() ?? self::create([
                    'enabled' => false,
                    'conversions_api_enabled' => false,
                    'enabled_events' => self::$defaultEnabledEvents,
                    'consent_settings' => self::$defaultConsentSettings,
                    'respect_do_not_track' => true,
                    'require_consent' => true,
                    'debug_mode' => config('app.debug', false),
                    'automatic_matching' => true,
                    'enable_deduplication' => true,
                    'deduplication_method' => 'event_id',
                    'lazy_load' => false,
                    'event_batch_size' => 1,
                    'event_delay_ms' => 0,
                ]);
            } catch (\Exception $e) {
                // If table doesn't exist (e.g., during tests), return a default instance
                if (str_contains($e->getMessage(), 'no such table') || str_contains($e->getMessage(), 'doesn\'t exist')) {
                    return new self([
                        'enabled' => false,
                        'conversions_api_enabled' => false,
                        'enabled_events' => self::$defaultEnabledEvents,
                        'consent_settings' => self::$defaultConsentSettings,
                        'respect_do_not_track' => true,
                        'require_consent' => true,
                        'debug_mode' => config('app.debug', false),
                        'automatic_matching' => true,
                        'enable_deduplication' => true,
                        'deduplication_method' => 'event_id',
                        'lazy_load' => false,
                        'event_batch_size' => 1,
                        'event_delay_ms' => 0,
                    ]);
                }
                throw $e;
            }
        });
    }

    /**
     * Clear the configuration cache.
     */
    public static function clearCache(): void
    {
        Cache::forget('meta_pixel_config');
    }

    /**
     * Check if Meta Pixel is properly configured.
     */
    public function isConfigured(): bool
    {
        return $this->enabled && !empty($this->pixel_id);
    }

    /**
     * Check if Conversions API is properly configured.
     */
    public function isConversionsApiConfigured(): bool
    {
        return $this->conversions_api_enabled && 
               !empty($this->pixel_id) && 
               !empty($this->access_token);
    }

    /**
     * Check if an event is enabled.
     */
    public function isEventEnabled(string $eventName): bool
    {
        $enabledEvents = $this->enabled_events ?? [];
        return in_array($eventName, $enabledEvents);
    }

    /**
     * Get the masked access token for display purposes.
     */
    public function getMaskedAccessToken(): ?string
    {
        if (empty($this->access_token)) {
            return null;
        }

        $token = $this->access_token;
        if (strlen($token) <= 8) {
            return str_repeat('*', strlen($token));
        }

        return substr($token, 0, 4) . str_repeat('*', strlen($token) - 8) . substr($token, -4);
    }

    /**
     * Get configuration for frontend.
     */
    public function getFrontendConfig(): array
    {
        return [
            'enabled' => $this->enabled,
            'pixel_id' => $this->pixel_id,
            'debug_mode' => $this->debug_mode,
            'respect_do_not_track' => $this->respect_do_not_track,
            'require_consent' => $this->require_consent,
            'consent_settings' => $this->consent_settings ?? self::$defaultConsentSettings,
            'automatic_matching' => $this->automatic_matching,
            'enable_deduplication' => $this->enable_deduplication,
            'lazy_load' => $this->lazy_load,
            'event_delay_ms' => $this->event_delay_ms,
            'enabled_events' => $this->enabled_events ?? [],
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when configuration is updated
        static::saved(function () {
            self::clearCache();
        });

        static::deleted(function () {
            self::clearCache();
        });
    }
}
