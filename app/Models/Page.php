<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;

class Page extends Model
{
    use HasFactory;
    protected $fillable = [
        'title',
        'slug',
        'content',
        'featured_image',
        'meta_description',
        'meta_keywords',
        'layout',
        'is_published',
        'author_id',
        'published_at',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'published_at' => 'datetime',
    ];

    protected $appends = [
        'url',
        'excerpt',
    ];

    /**
     * Get the author of the page.
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Scope to get only published pages.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope to get pages by layout.
     */
    public function scopeByLayout($query, string $layout)
    {
        return $query->where('layout', $layout);
    }

    /**
     * Get reserved slugs that cannot be used for pages.
     */
    public static function getReservedSlugs(): array
    {
        return [
            // Static routes
            'contact',
            'pricing',
            'pages',
            'dashboard',
            'login',
            'register',
            'logout',
            'forgot-password',
            'reset-password',
            'verify-email',
            'placeholder-image.png',

            // Route prefixes
            'guest',
            'api',
            'webhooks',
            'admin',

            // Dynamic single-level routes
            'brands',
            'categories',
            'parts',
            'models',

            // Additional reserved words for safety
            'home',
            'index',
            'search',
            'auth',
            'user',
            'users',
            'profile',
            'settings',
            'config',
            'system',
            'app',
            'application',
            'www',
            'mail',
            'email',
            'ftp',
            'ssh',
            'ssl',
            'tls',
            'http',
            'https',
            'www',
            'cdn',
            'static',
            'assets',
            'public',
            'private',
            'secure',
            'test',
            'testing',
            'dev',
            'development',
            'staging',
            'production',
            'prod',
            'demo',
        ];
    }

    /**
     * Check if a slug is reserved.
     */
    public static function isReservedSlug(string $slug): bool
    {
        return in_array(strtolower($slug), static::getReservedSlugs());
    }

    /**
     * Generate a unique slug from the title.
     */
    public static function generateSlug(string $title, ?int $excludeId = null): string
    {
        // Remove special characters and convert to lowercase
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title), '-'));
        $originalSlug = $slug;
        $counter = 1;

        // Check if the slug is reserved
        if (static::isReservedSlug($slug)) {
            $slug = $originalSlug . '-page';
        }

        while (static::where('slug', $slug)
                    ->when($excludeId, fn($query) => $query->where('id', '!=', $excludeId))
                    ->exists() || static::isReservedSlug($slug)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the page URL.
     */
    public function getUrlAttribute(): string
    {
        return url('/' . $this->slug);
    }

    /**
     * Get the page excerpt from content.
     */
    public function getExcerptAttribute(): string
    {
        if ($this->meta_description) {
            return $this->meta_description;
        }

        $content = strip_tags($this->content ?? '');
        return Str::limit($content, 160);
    }

    /**
     * Get cached page by slug.
     */
    public static function getCachedBySlug(string $slug): ?self
    {
        $cacheKey = "page_slug_{$slug}";
        
        return Cache::remember($cacheKey, 3600, function () use ($slug) {
            return static::published()
                        ->where('slug', $slug)
                        ->with('author')
                        ->first();
        });
    }

    /**
     * Clear page cache.
     */
    public function clearCache(): void
    {
        Cache::forget("page_slug_{$this->slug}");
        Cache::forget('published_pages_list');
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug if not provided
        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = static::generateSlug($page->title);
            }

            // Validate that slug is not reserved
            if (static::isReservedSlug($page->slug)) {
                throw new \InvalidArgumentException("The slug '{$page->slug}' is reserved and cannot be used for pages.");
            }
        });

        // Update slug if title changes
        static::updating(function ($page) {
            if ($page->isDirty('title') && empty($page->getOriginal('slug'))) {
                $page->slug = static::generateSlug($page->title, $page->id);
            }

            // Validate that slug is not reserved
            if ($page->isDirty('slug') && static::isReservedSlug($page->slug)) {
                throw new \InvalidArgumentException("The slug '{$page->slug}' is reserved and cannot be used for pages.");
            }
        });

        // Clear cache when page is updated
        static::saved(function ($page) {
            $page->clearCache();
        });

        static::deleted(function ($page) {
            $page->clearCache();
        });
    }
}
