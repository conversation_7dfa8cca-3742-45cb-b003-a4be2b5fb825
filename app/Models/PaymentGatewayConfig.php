<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class PaymentGatewayConfig extends Model
{
    use HasFactory;

    protected $fillable = [
        'gateway_name',
        'display_name',
        'description',
        'is_enabled',
        'is_available_globally',
        'supported_countries',
        'supported_currencies',
        'primary_currency',
        'sort_order',
        'configuration',
        'features',
        'requires_api_keys',
        'supports_subscriptions',
        'supports_one_time_payments',
        'admin_notes',
        'last_configured_at',
        'configured_by',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'is_available_globally' => 'boolean',
        'supported_countries' => 'array',
        'supported_currencies' => 'array',
        'configuration' => 'array',
        'features' => 'array',
        'requires_api_keys' => 'boolean',
        'supports_subscriptions' => 'boolean',
        'supports_one_time_payments' => 'boolean',
        'last_configured_at' => 'datetime',
    ];

    /**
     * Get enabled payment gateways.
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * Get globally available payment gateways.
     */
    public function scopeGloballyAvailable($query)
    {
        return $query->where('is_available_globally', true);
    }

    /**
     * Get payment gateways ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('display_name');
    }

    /**
     * Check if gateway supports a specific country.
     */
    public function supportsCountry(string $countryCode): bool
    {
        if (!$this->is_enabled || !$this->is_available_globally) {
            return false;
        }

        $supportedCountries = $this->supported_countries ?? [];
        
        // If empty or contains '*', supports all countries
        if (empty($supportedCountries) || in_array('*', $supportedCountries)) {
            return true;
        }

        return in_array(strtoupper($countryCode), array_map('strtoupper', $supportedCountries));
    }

    /**
     * Check if gateway supports a specific currency.
     */
    public function supportsCurrency(string $currency): bool
    {
        $supportedCurrencies = $this->supported_currencies ?? [];
        return in_array(strtoupper($currency), array_map('strtoupper', $supportedCurrencies));
    }

    /**
     * Get available gateways for a specific country and currency.
     */
    public static function getAvailableForCountryAndCurrency(string $countryCode, string $currency): \Illuminate\Database\Eloquent\Collection
    {
        return static::enabled()
            ->globallyAvailable()
            ->ordered()
            ->get()
            ->filter(function ($gateway) use ($countryCode, $currency) {
                return $gateway->supportsCountry($countryCode) && $gateway->supportsCurrency($currency);
            });
    }

    /**
     * Get the primary gateway for a country.
     */
    public static function getPrimaryForCountry(string $countryCode): ?self
    {
        $countryCode = strtoupper($countryCode);
        
        // Bangladesh: SurjoPay is primary
        if ($countryCode === 'BD') {
            return static::where('gateway_name', 'shurjopay')
                ->enabled()
                ->globallyAvailable()
                ->first();
        }
        
        // Other countries: Paddle is primary
        return static::where('gateway_name', 'paddle')
            ->enabled()
            ->globallyAvailable()
            ->first();
    }

    /**
     * Get cached gateway configuration.
     */
    public static function getCachedConfig(): \Illuminate\Database\Eloquent\Collection
    {
        return Cache::remember('payment_gateway_configs', 3600, function () {
            return static::enabled()->globallyAvailable()->ordered()->get();
        });
    }

    /**
     * Clear gateway configuration cache.
     */
    public static function clearCache(): void
    {
        Cache::forget('payment_gateway_configs');
    }

    /**
     * Get gateway configuration for frontend.
     */
    public function toFrontendArray(): array
    {
        return [
            'id' => $this->id,
            'gateway_name' => $this->gateway_name,
            'display_name' => $this->display_name,
            'description' => $this->description,
            'is_enabled' => $this->is_enabled,
            'supported_countries' => $this->supported_countries,
            'supported_currencies' => $this->supported_currencies,
            'primary_currency' => $this->primary_currency,
            'features' => $this->features,
            'supports_subscriptions' => $this->supports_subscriptions,
            'supports_one_time_payments' => $this->supports_one_time_payments,
        ];
    }

    /**
     * Boot method to clear cache when model is saved or deleted.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            static::clearCache();
        });

        static::deleted(function () {
            static::clearCache();
        });
    }

    /**
     * Seed default gateway configurations.
     */
    public static function seedDefaults(): void
    {
        $defaults = [
            [
                'gateway_name' => 'paddle',
                'display_name' => 'Paddle',
                'description' => 'Complete payment solution with global tax compliance',
                'is_enabled' => true,
                'is_available_globally' => true,
                'supported_countries' => ['*'], // All countries except Bangladesh
                'supported_currencies' => ['USD'],
                'primary_currency' => 'USD',
                'sort_order' => 1,
                'features' => ['Subscription Billing', 'Global Tax Compliance', 'Fraud Protection'],
                'requires_api_keys' => true,
                'supports_subscriptions' => true,
                'supports_one_time_payments' => true,
            ],
            [
                'gateway_name' => 'shurjopay',
                'display_name' => 'SurjoPay',
                'description' => 'Bangladesh local payment gateway supporting BDT',
                'is_enabled' => true,
                'is_available_globally' => true,
                'supported_countries' => ['BD'],
                'supported_currencies' => ['BDT'],
                'primary_currency' => 'BDT',
                'sort_order' => 2,
                'features' => ['Local Banking', 'Mobile Banking', 'Card Payments'],
                'requires_api_keys' => true,
                'supports_subscriptions' => true,
                'supports_one_time_payments' => true,
            ],
            [
                'gateway_name' => 'coinbase_commerce',
                'display_name' => 'Coinbase Commerce',
                'description' => 'Cryptocurrency payment gateway',
                'is_enabled' => true,
                'is_available_globally' => true,
                'supported_countries' => ['*'],
                'supported_currencies' => ['USD'],
                'primary_currency' => 'USD',
                'sort_order' => 3,
                'features' => ['Cryptocurrency Payments', 'Auto USDC Settlement', 'Volatility Protection'],
                'requires_api_keys' => true,
                'supports_subscriptions' => false,
                'supports_one_time_payments' => true,
            ],
            [
                'gateway_name' => 'offline',
                'display_name' => 'Offline Payment',
                'description' => 'Manual payment processing for bank transfers and other offline methods',
                'is_enabled' => true,
                'is_available_globally' => true,
                'supported_countries' => ['*'],
                'supported_currencies' => ['BDT', 'USD'],
                'primary_currency' => null,
                'sort_order' => 4,
                'features' => ['Bank Transfer', 'Manual Processing', 'Flexible Payment Terms'],
                'requires_api_keys' => false,
                'supports_subscriptions' => true,
                'supports_one_time_payments' => true,
            ],
        ];

        foreach ($defaults as $config) {
            static::updateOrCreate(
                ['gateway_name' => $config['gateway_name']],
                $config
            );
        }
    }
}
