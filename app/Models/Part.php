<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Part extends Model
{
    use HasFactory, Sluggable;

    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'part_number',
        'manufacturer',
        'description',
        'specifications',
        'images',
        'is_active',
        'sales_button_name',
        'sales_button_url',
    ];

    protected $casts = [
        'specifications' => 'array',
        'images' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Return the sluggable configuration array for this model.
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name'
            ]
        ];
    }

    /**
     * Get the category that owns this part.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the models compatible with this part.
     */
    public function models(): BelongsToMany
    {
        return $this->belongsToMany(MobileModel::class, 'model_parts', 'part_id', 'model_id')
            ->withPivot('compatibility_notes', 'is_verified', 'display_type', 'display_size', 'location')
            ->withTimestamps()
            ->using(\App\Models\ModelPartPivot::class);
    }

    /**
     * Get the user favorites for this part.
     */
    public function favorites(): MorphMany
    {
        return $this->morphMany(UserFavorite::class, 'favoritable');
    }

    /**
     * Scope to get only active parts.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the public URL for this part.
     */
    public function getPublicUrl(): string
    {
        return route('parts.show', $this->slug ?: $this->id);
    }

    /**
     * Get the admin URL for this part.
     */
    public function getAdminUrl(): string
    {
        return route('admin.parts.show', $this->id);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Retrieve the model for a bound value.
     * This allows resolving by both slug and ID for backward compatibility.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        // Try to find by slug first
        $model = $this->where('slug', $value)->first();

        // If not found by slug and value is numeric, try by ID
        if (!$model && is_numeric($value)) {
            $model = $this->where('id', $value)->first();
        }

        return $model;
    }
}
