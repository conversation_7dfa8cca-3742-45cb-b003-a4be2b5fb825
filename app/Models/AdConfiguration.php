<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AdConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'zone',
        'enabled',
        'page_types',
        'user_types',
        'frequency_rules',
        'targeting_rules',
        'ad_slot_id',
        'ad_format',
        'ad_sizes',
        'priority',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'page_types' => 'array',
        'user_types' => 'array',
        'frequency_rules' => 'array',
        'targeting_rules' => 'array',
        'ad_sizes' => 'array',
        'priority' => 'integer',
    ];

    /**
     * Get the ad performance records for this configuration.
     */
    public function performance(): HasMany
    {
        return $this->hasMany(AdPerformance::class, 'zone', 'zone');
    }

    /**
     * Scope to get enabled configurations.
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope to get configurations by zone.
     */
    public function scopeByZone($query, string $zone)
    {
        return $query->where('zone', $zone);
    }

    /**
     * Scope to get configurations by priority.
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'asc');
    }

    /**
     * Check if this configuration applies to a specific page.
     */
    public function appliesToPage(string $page): bool
    {
        if (empty($this->page_types)) {
            return true; // Apply to all pages if no specific pages defined
        }

        return in_array($page, $this->page_types);
    }

    /**
     * Check if this configuration applies to a specific user type.
     */
    public function appliesToUserType(string $userType): bool
    {
        if (empty($this->user_types)) {
            return true; // Apply to all user types if no specific types defined
        }

        return in_array($userType, $this->user_types);
    }

    /**
     * Get the maximum ads per page for this configuration.
     */
    public function getMaxAdsPerPage(): int
    {
        return $this->frequency_rules['max_ads_per_page'] ?? 4;
    }

    /**
     * Get the delay in seconds before showing ads.
     */
    public function getDelaySeconds(): int
    {
        return $this->frequency_rules['delay_seconds'] ?? 3;
    }

    /**
     * Get the grace period in minutes for new users.
     */
    public function getGracePeriodMinutes(): int
    {
        return $this->frequency_rules['grace_period_minutes'] ?? 0;
    }

    /**
     * Get the ad sizes configuration.
     */
    public function getAdSizes(): array
    {
        return $this->ad_sizes ?? [];
    }

    /**
     * Get targeting rules.
     */
    public function getTargetingRules(): array
    {
        return $this->targeting_rules ?? [];
    }

    /**
     * Create default ad configurations.
     */
    public static function createDefaults(): void
    {
        $defaultConfigs = [
            [
                'zone' => 'header',
                'enabled' => true,
                'page_types' => ['home', 'search', 'details'],
                'user_types' => ['free', 'guest'],
                'frequency_rules' => [
                    'max_ads_per_page' => 1,
                    'delay_seconds' => 3,
                    'grace_period_minutes' => 0,
                ],
                'ad_format' => 'responsive',
                'ad_sizes' => [
                    'desktop' => ['width' => 728, 'height' => 90],
                    'mobile' => ['width' => 320, 'height' => 50],
                ],
                'priority' => 1,
            ],
            [
                'zone' => 'sidebar',
                'enabled' => true,
                'page_types' => ['search', 'details', 'dashboard'],
                'user_types' => ['free', 'guest'],
                'frequency_rules' => [
                    'max_ads_per_page' => 1,
                    'delay_seconds' => 0,
                    'grace_period_minutes' => 0,
                ],
                'ad_format' => 'fixed',
                'ad_sizes' => [
                    'desktop' => ['width' => 300, 'height' => 600],
                ],
                'priority' => 2,
            ],
            [
                'zone' => 'content',
                'enabled' => true,
                'page_types' => ['home', 'search', 'details'],
                'user_types' => ['free', 'guest'],
                'frequency_rules' => [
                    'max_ads_per_page' => 2,
                    'delay_seconds' => 5,
                    'grace_period_minutes' => 0,
                ],
                'ad_format' => 'responsive',
                'ad_sizes' => [
                    'desktop' => ['width' => 300, 'height' => 250],
                    'mobile' => ['width' => 300, 'height' => 250],
                ],
                'priority' => 3,
            ],
            [
                'zone' => 'footer',
                'enabled' => true,
                'page_types' => ['home', 'search', 'details'],
                'user_types' => ['free', 'guest'],
                'frequency_rules' => [
                    'max_ads_per_page' => 1,
                    'delay_seconds' => 0,
                    'grace_period_minutes' => 0,
                ],
                'ad_format' => 'responsive',
                'ad_sizes' => [
                    'desktop' => ['width' => 728, 'height' => 90],
                    'mobile' => ['width' => 320, 'height' => 100],
                ],
                'priority' => 4,
            ],
            [
                'zone' => 'mobile',
                'enabled' => true,
                'page_types' => ['home', 'search', 'details', 'dashboard'],
                'user_types' => ['free', 'guest'],
                'frequency_rules' => [
                    'max_ads_per_page' => 2,
                    'delay_seconds' => 2,
                    'grace_period_minutes' => 0,
                ],
                'ad_format' => 'responsive',
                'ad_sizes' => [
                    'mobile' => ['width' => 320, 'height' => 50],
                ],
                'priority' => 5,
            ],
        ];

        foreach ($defaultConfigs as $config) {
            self::firstOrCreate(
                ['zone' => $config['zone']],
                $config
            );
        }
    }
}
