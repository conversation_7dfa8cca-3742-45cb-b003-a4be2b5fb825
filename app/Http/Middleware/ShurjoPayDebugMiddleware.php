<?php

namespace App\Http\Middleware;

use App\Services\ShurjoPayLogger;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ShurjoPayDebugMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only log if ShurjoPay logging and debug are enabled
        if (!ShurjoPayLogger::isLoggingEnabled() || !ShurjoPayLogger::isDebugEnabled()) {
            return $next($request);
        }

        $startTime = microtime(true);

        // Log incoming request
        $this->logRequest($request);

        $response = $next($request);

        // Log response
        $this->logResponse($request, $response, $startTime);

        return $response;
    }

    /**
     * Log incoming request details.
     */
    protected function logRequest(Request $request): void
    {
        $logData = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'headers' => $this->sanitizeHeaders($request->headers->all()),
        ];

        // Add request body for POST requests (sanitized)
        if ($request->isMethod('POST')) {
            $logData['body'] = $this->sanitizeRequestBody($request->all());
        }

        ShurjoPayLogger::debug('ShurjoPay Request', $logData);
    }

    /**
     * Log response details.
     */
    protected function logResponse(Request $request, Response $response, float $startTime): void
    {
        $duration = round((microtime(true) - $startTime) * 1000, 2);

        $logData = [
            'status_code' => $response->getStatusCode(),
            'duration_ms' => $duration,
            'content_type' => $response->headers->get('Content-Type'),
        ];

        // Add response content for debugging (limited size)
        if (ShurjoPayLogger::isDebugEnabled() && $response->getStatusCode() >= 400) {
            $content = $response->getContent();
            if (strlen($content) > 1000) {
                $content = substr($content, 0, 1000) . '... (truncated)';
            }
            $logData['response_content'] = $content;
        }

        if ($response->getStatusCode() >= 400) {
            ShurjoPayLogger::warning('ShurjoPay Response', $logData);
        } else {
            ShurjoPayLogger::debug('ShurjoPay Response', $logData);
        }
    }

    /**
     * Sanitize headers to remove sensitive information.
     */
    protected function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = ['authorization', 'x-csrf-token', 'cookie'];

        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['***REDACTED***'];
            }
        }

        return $headers;
    }

    /**
     * Sanitize request body to remove sensitive information.
     */
    protected function sanitizeRequestBody(array $data): array
    {
        $sensitiveFields = ['password', 'token', 'secret', 'key'];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***REDACTED***';
            }
        }

        return $data;
    }
}
