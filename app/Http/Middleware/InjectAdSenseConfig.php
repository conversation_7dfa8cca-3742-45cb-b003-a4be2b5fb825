<?php

namespace App\Http\Middleware;

use App\Services\AdSenseService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;

class InjectAdSenseConfig
{
    /**
     * Create a new middleware instance.
     */
    public function __construct(
        private AdSenseService $adSenseService
    ) {}

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Share AdSense configuration with all Inertia responses
        Inertia::share([
            'adSettings' => fn () => Cache::remember('adsense_frontend_config', 3600, function () {
                return $this->adSenseService->getFrontendConfig();
            }),
        ]);

        return $next($request);
    }
}
