<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentGatewayConfig;
use App\Services\PaymentGatewayConfigService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class PaymentGatewayConfigController extends Controller
{
    private PaymentGatewayConfigService $gatewayConfigService;

    public function __construct(PaymentGatewayConfigService $gatewayConfigService)
    {
        $this->gatewayConfigService = $gatewayConfigService;
    }

    /**
     * Display a listing of payment gateway configurations.
     */
    public function index(): Response
    {
        $gateways = $this->gatewayConfigService->getAllGatewayConfigs();
        $statistics = $this->gatewayConfigService->getGatewayStatistics();

        return Inertia::render('admin/payment-gateways/Index', [
            'gateways' => $gateways,
            'statistics' => $statistics,
        ]);
    }

    /**
     * Show the form for editing a payment gateway configuration.
     */
    public function edit(PaymentGatewayConfig $paymentGatewayConfig): Response
    {
        return Inertia::render('admin/payment-gateways/Edit', [
            'gateway' => $paymentGatewayConfig,
            'availableCountries' => $this->getAvailableCountries(),
            'availableCurrencies' => $this->getAvailableCurrencies(),
        ]);
    }

    /**
     * Update the specified payment gateway configuration.
     */
    public function update(Request $request, PaymentGatewayConfig $paymentGatewayConfig): RedirectResponse
    {
        $validated = $request->validate([
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_enabled' => 'required|boolean',
            'is_available_globally' => 'required|boolean',
            'supported_countries' => 'nullable|array',
            'supported_countries.*' => 'string|max:3',
            'supported_currencies' => 'nullable|array',
            'supported_currencies.*' => 'string|max:3',
            'primary_currency' => 'nullable|string|max:3',
            'sort_order' => 'required|integer|min:0',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'requires_api_keys' => 'required|boolean',
            'supports_subscriptions' => 'required|boolean',
            'supports_one_time_payments' => 'required|boolean',
            'admin_notes' => 'nullable|string',
        ]);

        // Validate gateway-specific configuration
        $errors = $this->gatewayConfigService->validateGatewayConfig(
            $paymentGatewayConfig->gateway_name,
            $validated
        );

        if (!empty($errors)) {
            return back()->withErrors(['config' => $errors]);
        }

        // Update configuration
        $success = $this->gatewayConfigService->updateGatewayConfig(
            $paymentGatewayConfig->gateway_name,
            array_merge($validated, [
                'last_configured_at' => now(),
                'configured_by' => auth()->user()->name ?? 'System',
            ])
        );

        if ($success) {
            return redirect()->route('admin.payment-gateways.index')
                ->with('success', 'Payment gateway configuration updated successfully.');
        }

        return back()->with('error', 'Failed to update payment gateway configuration.');
    }

    /**
     * Enable a payment gateway.
     */
    public function enable(PaymentGatewayConfig $paymentGatewayConfig): RedirectResponse
    {
        $success = $this->gatewayConfigService->enableGateway($paymentGatewayConfig->gateway_name);

        if ($success) {
            return back()->with('success', "Payment gateway '{$paymentGatewayConfig->display_name}' enabled successfully.");
        }

        return back()->with('error', 'Failed to enable payment gateway.');
    }

    /**
     * Disable a payment gateway.
     */
    public function disable(PaymentGatewayConfig $paymentGatewayConfig): RedirectResponse
    {
        $success = $this->gatewayConfigService->disableGateway($paymentGatewayConfig->gateway_name);

        if ($success) {
            return back()->with('success', "Payment gateway '{$paymentGatewayConfig->display_name}' disabled successfully.");
        }

        return back()->with('error', 'Failed to disable payment gateway.');
    }

    /**
     * Test a payment gateway configuration.
     */
    public function test(PaymentGatewayConfig $paymentGatewayConfig): RedirectResponse
    {
        $isConfigured = $this->gatewayConfigService->isGatewayConfigured($paymentGatewayConfig->gateway_name);

        if ($isConfigured) {
            return back()->with('success', "Payment gateway '{$paymentGatewayConfig->display_name}' is properly configured.");
        }

        return back()->with('error', "Payment gateway '{$paymentGatewayConfig->display_name}' is not properly configured. Please check your API credentials.");
    }

    /**
     * Initialize default gateway configurations.
     */
    public function initializeDefaults(): RedirectResponse
    {
        try {
            $this->gatewayConfigService->initializeDefaults();
            return back()->with('success', 'Default payment gateway configurations initialized successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to initialize default configurations: ' . $e->getMessage());
        }
    }

    /**
     * Get available countries for configuration.
     */
    private function getAvailableCountries(): array
    {
        return [
            'BD' => 'Bangladesh',
            'US' => 'United States',
            'CA' => 'Canada',
            'GB' => 'United Kingdom',
            'AU' => 'Australia',
            'IN' => 'India',
            'DE' => 'Germany',
            'FR' => 'France',
            'JP' => 'Japan',
            'SG' => 'Singapore',
            '*' => 'All Countries',
        ];
    }

    /**
     * Get available currencies for configuration.
     */
    private function getAvailableCurrencies(): array
    {
        return [
            'BDT' => 'Bangladeshi Taka',
            'USD' => 'US Dollar',
            'EUR' => 'Euro',
            'GBP' => 'British Pound',
            'CAD' => 'Canadian Dollar',
            'AUD' => 'Australian Dollar',
            'JPY' => 'Japanese Yen',
            'INR' => 'Indian Rupee',
            'SGD' => 'Singapore Dollar',
        ];
    }

    /**
     * Get gateway configuration status.
     */
    public function status(): Response
    {
        $statistics = $this->gatewayConfigService->getGatewayStatistics();
        $gateways = $this->gatewayConfigService->getAllGatewayConfigs();

        return Inertia::render('admin/payment-gateways/Status', [
            'statistics' => $statistics,
            'gateways' => $gateways,
            'system_status' => [
                'total_gateways' => count($gateways),
                'enabled_gateways' => collect($gateways)->where('is_enabled', true)->count(),
                'configured_gateways' => collect($gateways)->where('configuration_status', 'configured')->count(),
            ],
        ]);
    }
}
