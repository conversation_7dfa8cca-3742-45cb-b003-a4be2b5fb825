<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Page;
use App\Models\Category;
use App\Models\Brand;
use App\Models\MobileModel;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class MenuController extends Controller
{
    /**
     * Display a listing of menus.
     */
    public function index(Request $request): Response
    {
        $query = Menu::with(['items' => function ($query) {
            $query->whereNull('parent_id')->orderBy('order');
        }])->orderBy('created_at', 'desc');

        // Apply location filter
        if ($request->filled('location')) {
            $query->byLocation($request->location);
        }

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $menus = $query->paginate(15)->withQueryString();

        return Inertia::render('admin/Menus/Index', [
            'menus' => $menus,
            'filters' => [
                'search' => $request->search,
                'location' => $request->location,
            ],
            'locations' => Menu::getAvailableLocations(),
        ]);
    }

    /**
     * Show the form for creating a new menu.
     */
    public function create(): Response
    {
        return Inertia::render('admin/Menus/Create', [
            'locations' => Menu::getAvailableLocations(),
        ]);
    }

    /**
     * Store a newly created menu.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'location' => 'required|string|in:' . implode(',', array_keys(Menu::getAvailableLocations())),
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $menu = Menu::create($validated);

        return redirect()->route('admin.menus.show', $menu)
                        ->with('success', 'Menu created successfully.');
    }

    /**
     * Display the specified menu.
     */
    public function show(Menu $menu): Response
    {
        // Load all menu items as a flat array (including inactive ones) for admin interface
        // The frontend will build the tree structure from this flat array
        $menu->load(['items' => function ($query) {
            $query->orderBy('order');
        }]);

        // Get available items for adding to menu
        $availableItems = [
            'pages' => Page::published()->select('id', 'title', 'slug')->get(),
            'categories' => Category::select('id', 'name', 'slug')->get(),
            'brands' => Brand::select('id', 'name', 'slug')->get(),
            'models' => MobileModel::select('id', 'name', 'slug')->get(),
        ];

        return Inertia::render('admin/Menus/Show', [
            'menu' => $menu,
            'availableItems' => $availableItems,
            'itemTypes' => MenuItem::getAvailableTypes(),
            'targetOptions' => MenuItem::getAvailableTargets(),
        ]);
    }

    /**
     * Show the form for editing the specified menu.
     */
    public function edit(Menu $menu): Response
    {
        return Inertia::render('admin/Menus/Edit', [
            'menu' => $menu,
            'locations' => Menu::getAvailableLocations(),
        ]);
    }

    /**
     * Update the specified menu.
     */
    public function update(Request $request, Menu $menu): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'location' => 'required|string|in:' . implode(',', array_keys(Menu::getAvailableLocations())),
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $menu->update($validated);

        return redirect()->route('admin.menus.index')
                        ->with('success', 'Menu updated successfully.');
    }

    /**
     * Remove the specified menu.
     */
    public function destroy(Menu $menu): RedirectResponse
    {
        $menu->delete();

        return redirect()->route('admin.menus.index')
                        ->with('success', 'Menu deleted successfully.');
    }

    /**
     * Update menu items order.
     */
    public function updateOrder(Request $request, Menu $menu)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|integer|exists:menu_items,id',
            'items.*.order' => 'required|integer|min:0',
            'items.*.parent_id' => 'nullable|integer|exists:menu_items,id',
        ]);

        // Validate that parent items belong to the same menu
        $menuItemIds = MenuItem::where('menu_id', $menu->id)->pluck('id')->toArray();

        foreach ($validated['items'] as $itemData) {
            // Ensure the item belongs to this menu
            if (!in_array($itemData['id'], $menuItemIds)) {
                return response()->json([
                    'message' => 'Invalid menu item ID: ' . $itemData['id']
                ], 422);
            }

            // Ensure parent (if specified) belongs to this menu
            if ($itemData['parent_id'] && !in_array($itemData['parent_id'], $menuItemIds)) {
                return response()->json([
                    'message' => 'Invalid parent menu item ID: ' . $itemData['parent_id']
                ], 422);
            }

            // Prevent circular references (item cannot be its own parent)
            if ($itemData['parent_id'] == $itemData['id']) {
                return response()->json([
                    'message' => 'Menu item cannot be its own parent'
                ], 422);
            }
        }

        // Update items in a transaction
        DB::transaction(function () use ($validated, $menu) {
            foreach ($validated['items'] as $itemData) {
                MenuItem::where('id', $itemData['id'])
                       ->where('menu_id', $menu->id)
                       ->update([
                           'order' => $itemData['order'],
                           'parent_id' => $itemData['parent_id'],
                       ]);
            }
        });

        // Clear menu cache
        $menu->clearCache();

        // Return JSON response for AJAX calls, redirect for form submissions
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Menu order updated successfully.',
                'success' => true
            ]);
        }

        return redirect()->back();
    }

    /**
     * Add item to menu.
     */
    public function addItem(Request $request, Menu $menu): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'type' => 'required|string|in:' . implode(',', array_keys(MenuItem::getAvailableTypes())),
            'url' => 'required_if:type,custom|nullable|string|max:500',
            'target' => 'required|string|in:' . implode(',', array_keys(MenuItem::getAvailableTargets())),
            'icon' => 'nullable|string|max:100',
            'css_class' => 'nullable|string|max:255',
            'reference_id' => 'nullable|integer',
            'parent_id' => 'nullable|integer|exists:menu_items,id',
            'is_active' => 'boolean',
        ]);

        // Get the next order number
        $maxOrder = MenuItem::where('menu_id', $menu->id)
                           ->where('parent_id', $validated['parent_id'] ?? null)
                           ->max('order') ?? 0;

        $validated['menu_id'] = $menu->id;
        $validated['order'] = $maxOrder + 1;

        MenuItem::create($validated);

        return redirect()->back();
    }

    /**
     * Update menu item.
     */
    public function updateItem(Request $request, Menu $menu, MenuItem $menuItem): RedirectResponse
    {
        // Ensure the menu item belongs to this menu
        if ($menuItem->menu_id !== $menu->id) {
            abort(404);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'type' => 'required|string|in:' . implode(',', array_keys(MenuItem::getAvailableTypes())),
            'url' => 'required_if:type,custom|nullable|string|max:500',
            'target' => 'required|string|in:' . implode(',', array_keys(MenuItem::getAvailableTargets())),
            'icon' => 'nullable|string|max:100',
            'css_class' => 'nullable|string|max:255',
            'reference_id' => 'nullable|integer',
            'is_active' => 'boolean',
        ]);

        $menuItem->update($validated);

        return redirect()->back();
    }

    /**
     * Remove menu item.
     */
    public function removeItem(Menu $menu, MenuItem $menuItem): RedirectResponse
    {
        // Ensure the menu item belongs to this menu
        if ($menuItem->menu_id !== $menu->id) {
            abort(404);
        }

        $menuItem->delete();

        return redirect()->back();
    }
}
