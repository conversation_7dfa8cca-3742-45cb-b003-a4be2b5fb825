<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdConfiguration;
use App\Models\AdPerformance;
use App\Services\AdSenseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class AdManagementController extends Controller
{
    protected AdSenseService $adSenseService;

    public function __construct(AdSenseService $adSenseService)
    {
        $this->adSenseService = $adSenseService;
    }

    /**
     * Display the ad management dashboard.
     */
    public function index(Request $request): Response
    {
        $startDate = $request->input('start_date') ? 
            Carbon::parse($request->input('start_date')) : 
            now()->subDays(30);
        
        $endDate = $request->input('end_date') ? 
            Carbon::parse($request->input('end_date')) : 
            now();

        // Get ad configurations
        $configurations = AdConfiguration::with('performance')
            ->orderBy('priority')
            ->get()
            ->map(function ($config) {
                return [
                    'id' => $config->id,
                    'zone' => $config->zone,
                    'enabled' => $config->enabled,
                    'page_types' => $config->page_types,
                    'user_types' => $config->user_types,
                    'ad_slot_id' => $config->ad_slot_id,
                    'ad_format' => $config->ad_format,
                    'priority' => $config->priority,
                    'frequency_rules' => $config->frequency_rules,
                    'targeting_rules' => $config->targeting_rules,
                    'created_at' => $config->created_at,
                    'updated_at' => $config->updated_at,
                ];
            });

        // Get analytics data
        $analytics = $this->adSenseService->getAnalytics($startDate, $endDate);
        $zoneComparison = $this->adSenseService->getZoneComparison($startDate, $endDate);

        // Get recent performance data
        $recentPerformance = AdPerformance::with('configuration')
            ->dateRange($startDate, $endDate)
            ->orderBy('date', 'desc')
            ->limit(100)
            ->get()
            ->map(function ($performance) {
                return [
                    'id' => $performance->id,
                    'zone' => $performance->zone,
                    'page' => $performance->page,
                    'user_type' => $performance->user_type,
                    'device_type' => $performance->device_type,
                    'impressions' => $performance->impressions,
                    'clicks' => $performance->clicks,
                    'revenue' => $performance->revenue,
                    'ctr' => $performance->ctr,
                    'cpm' => $performance->cpm,
                    'date' => $performance->date,
                ];
            });

        // Get AdSense configuration
        $adSenseConfig = $this->adSenseService->getAdSenseConfiguration();
        $adSenseStatus = $this->adSenseService->getAdSenseStatus();

        return Inertia::render('admin/AdManagement/index', [
            'configurations' => $configurations,
            'analytics' => $analytics,
            'zoneComparison' => $zoneComparison,
            'recentPerformance' => $recentPerformance,
            'adSenseConfig' => $adSenseConfig,
            'adSenseStatus' => $adSenseStatus,
            'filters' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ],
        ]);
    }

    /**
     * Show the form for creating a new ad configuration.
     */
    public function create(): Response
    {
        return Inertia::render('admin/AdManagement/create', [
            'zones' => ['header', 'sidebar', 'content', 'footer', 'mobile'],
            'pageTypes' => ['home', 'search', 'dashboard', 'details', 'all'],
            'userTypes' => ['free', 'guest', 'premium', 'admin'],
            'adFormats' => ['auto', 'fixed', 'responsive'],
        ]);
    }

    /**
     * Store a newly created ad configuration.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'zone' => 'required|string|in:header,sidebar,content,footer,mobile',
            'enabled' => 'boolean',
            'page_types' => 'nullable|array',
            'page_types.*' => 'string',
            'user_types' => 'nullable|array',
            'user_types.*' => 'string|in:free,guest,premium,admin',
            'ad_slot_id' => 'nullable|string|max:50',
            'ad_format' => 'required|string|in:auto,fixed,responsive',
            'priority' => 'integer|min:1|max:10',
            'frequency_rules' => 'nullable|array',
            'targeting_rules' => 'nullable|array',
            'ad_sizes' => 'nullable|array',
        ]);

        $configuration = AdConfiguration::create($validated);

        $this->adSenseService->clearCache();

        return redirect()->route('admin.ads.index')
            ->with('success', 'Ad configuration created successfully.');
    }

    /**
     * Show the form for editing an ad configuration.
     */
    public function edit(AdConfiguration $adConfiguration): Response
    {
        return Inertia::render('admin/AdManagement/edit', [
            'configuration' => [
                'id' => $adConfiguration->id,
                'zone' => $adConfiguration->zone,
                'enabled' => $adConfiguration->enabled,
                'page_types' => $adConfiguration->page_types,
                'user_types' => $adConfiguration->user_types,
                'ad_slot_id' => $adConfiguration->ad_slot_id,
                'ad_format' => $adConfiguration->ad_format,
                'priority' => $adConfiguration->priority,
                'frequency_rules' => $adConfiguration->frequency_rules,
                'targeting_rules' => $adConfiguration->targeting_rules,
                'ad_sizes' => $adConfiguration->ad_sizes,
            ],
            'zones' => ['header', 'sidebar', 'content', 'footer', 'mobile'],
            'pageTypes' => ['home', 'search', 'dashboard', 'details', 'all'],
            'userTypes' => ['free', 'guest', 'premium', 'admin'],
            'adFormats' => ['auto', 'fixed', 'responsive'],
        ]);
    }

    /**
     * Update an ad configuration.
     */
    public function update(Request $request, AdConfiguration $adConfiguration)
    {
        $validated = $request->validate([
            'zone' => 'required|string|in:header,sidebar,content,footer,mobile',
            'enabled' => 'boolean',
            'page_types' => 'nullable|array',
            'page_types.*' => 'string',
            'user_types' => 'nullable|array',
            'user_types.*' => 'string|in:free,guest,premium,admin',
            'ad_slot_id' => 'nullable|string|max:50',
            'ad_format' => 'required|string|in:auto,fixed,responsive',
            'priority' => 'integer|min:1|max:10',
            'frequency_rules' => 'nullable|array',
            'targeting_rules' => 'nullable|array',
            'ad_sizes' => 'nullable|array',
        ]);

        $adConfiguration->update($validated);

        $this->adSenseService->clearCache();

        return redirect()->route('admin.ads.index')
            ->with('success', 'Ad configuration updated successfully.');
    }

    /**
     * Remove an ad configuration.
     */
    public function destroy(AdConfiguration $adConfiguration)
    {
        $adConfiguration->delete();

        $this->adSenseService->clearCache();

        return redirect()->route('admin.ads.index')
            ->with('success', 'Ad configuration deleted successfully.');
    }

    /**
     * Toggle ad zone enabled/disabled status.
     */
    public function toggle(Request $request, AdConfiguration $adConfiguration)
    {
        $adConfiguration->enabled = !$adConfiguration->enabled;
        $adConfiguration->save();

        $this->adSenseService->clearCache();

        $message = $adConfiguration->enabled ?
            'Ad zone enabled successfully.' :
            'Ad zone disabled successfully.';

        return redirect()->route('admin.ads.index')
            ->with('success', $message);
    }

    /**
     * Get analytics data for a specific zone.
     */
    public function zoneAnalytics(Request $request, string $zone)
    {
        $startDate = $request->input('start_date') ? 
            Carbon::parse($request->input('start_date')) : 
            now()->subDays(30);
        
        $endDate = $request->input('end_date') ? 
            Carbon::parse($request->input('end_date')) : 
            now();

        $analytics = $this->adSenseService->getAnalytics($startDate, $endDate, $zone);

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Initialize default ad configurations.
     */
    public function initializeDefaults()
    {
        $this->adSenseService->initializeDefaults();

        return redirect()->route('admin.ads.index')
            ->with('success', 'Default ad configurations initialized successfully.');
    }

    /**
     * Clear ad configuration cache.
     */
    public function clearCache()
    {
        // Clear all ad-related cache keys
        Cache::forget('ad_configurations');
        Cache::forget('ad_settings');
        Cache::forget('adsense_config');
        Cache::forget('adsense_status');
        Cache::forget('adsense_frontend_config');

        // Also clear the service cache
        $this->adSenseService->clearCache();

        return redirect()->route('admin.ads.index')
            ->with('success', 'All ad-related cache cleared successfully.');
    }

    /**
     * Get AdSense configuration.
     */
    public function getAdSenseConfig()
    {
        $config = $this->adSenseService->getAdSenseConfiguration();
        $status = $this->adSenseService->getAdSenseStatus();

        return response()->json([
            'success' => true,
            'config' => $config,
            'status' => $status,
        ]);
    }

    /**
     * Update AdSense configuration.
     */
    public function updateAdSenseConfig(Request $request)
    {
        $validated = $request->validate([
            'enabled' => 'required|boolean',
            'client_id' => 'required_if:enabled,true|nullable|string|regex:/^ca-pub-\d{16}$/',
            'auto_ads' => 'boolean',
            'debug' => 'boolean',
            'zones' => 'array',
            'zones.header_enabled' => 'boolean',
            'zones.sidebar_enabled' => 'boolean',
            'zones.content_enabled' => 'boolean',
            'zones.footer_enabled' => 'boolean',
            'zones.mobile_enabled' => 'boolean',
            'frequency' => 'array',
            'frequency.max_ads_per_page' => 'integer|min:1|max:10',
            'frequency.delay_seconds' => 'integer|min:0|max:30',
            'frequency.grace_period_minutes' => 'integer|min:0|max:60',
        ]);

        // Validate configuration
        $validation = $this->adSenseService->validateAdSenseConfiguration($validated);

        if (!$validation['valid']) {
            return back()->withErrors($validation['errors']);
        }

        try {
            // Update environment variables
            $this->updateEnvironmentVariables([
                'GOOGLE_ADSENSE_ENABLED' => $validated['enabled'] ? 'true' : 'false',
                'GOOGLE_ADSENSE_CLIENT_ID' => $validated['client_id'] ?? '',
                'GOOGLE_ADSENSE_AUTO_ADS' => ($validated['auto_ads'] ?? false) ? 'true' : 'false',
                'GOOGLE_ADSENSE_DEBUG' => ($validated['debug'] ?? false) ? 'true' : 'false',
                'ADSENSE_HEADER_ENABLED' => ($validated['zones']['header_enabled'] ?? true) ? 'true' : 'false',
                'ADSENSE_SIDEBAR_ENABLED' => ($validated['zones']['sidebar_enabled'] ?? true) ? 'true' : 'false',
                'ADSENSE_CONTENT_ENABLED' => ($validated['zones']['content_enabled'] ?? true) ? 'true' : 'false',
                'ADSENSE_FOOTER_ENABLED' => ($validated['zones']['footer_enabled'] ?? true) ? 'true' : 'false',
                'ADSENSE_MOBILE_ENABLED' => ($validated['zones']['mobile_enabled'] ?? true) ? 'true' : 'false',
                'ADSENSE_MAX_ADS_PER_PAGE' => $validated['frequency']['max_ads_per_page'] ?? 4,
                'ADSENSE_DELAY_SECONDS' => $validated['frequency']['delay_seconds'] ?? 3,
                'ADSENSE_GRACE_PERIOD_MINUTES' => $validated['frequency']['grace_period_minutes'] ?? 0,
            ]);

            // Clear cache
            Cache::forget('adsense_config');
            Cache::forget('adsense_status');
            Cache::forget('adsense_frontend_config');

            return redirect()->route('admin.ads.index')
                ->with('success', 'AdSense configuration updated successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to update AdSense configuration', [
                'error' => $e->getMessage(),
                'config' => $validated,
            ]);

            return back()->withErrors(['general' => 'Failed to update AdSense configuration.']);
        }
    }

    /**
     * Test AdSense configuration.
     */
    public function testAdSenseConfig(Request $request)
    {
        $config = $request->validate([
            'enabled' => 'required|boolean',
            'client_id' => 'required_if:enabled,true|nullable|string',
            'auto_ads' => 'boolean',
            'debug' => 'boolean',
            'zones' => 'array',
            'frequency' => 'array',
        ]);

        $result = $this->adSenseService->testAdSenseConfiguration($config);

        return response()->json($result);
    }

    /**
     * Export performance data.
     */
    public function exportPerformance(Request $request)
    {
        $startDate = $request->input('start_date') ? 
            Carbon::parse($request->input('start_date')) : 
            now()->subDays(30);
        
        $endDate = $request->input('end_date') ? 
            Carbon::parse($request->input('end_date')) : 
            now();

        $performance = AdPerformance::dateRange($startDate, $endDate)
            ->orderBy('date', 'desc')
            ->get();

        $filename = 'ad_performance_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($performance) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Date', 'Zone', 'Page', 'User Type', 'Device Type',
                'Impressions', 'Clicks', 'CTR (%)', 'Revenue ($)', 'CPM ($)'
            ]);

            // CSV data
            foreach ($performance as $record) {
                fputcsv($file, [
                    $record->date,
                    $record->zone,
                    $record->page,
                    $record->user_type,
                    $record->device_type,
                    $record->impressions,
                    $record->clicks,
                    $record->ctr,
                    $record->revenue,
                    $record->cpm,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Update environment variables.
     */
    private function updateEnvironmentVariables(array $variables): void
    {
        $envPath = base_path('.env');

        if (!file_exists($envPath)) {
            throw new \Exception('.env file not found');
        }

        $envContent = file_get_contents($envPath);

        foreach ($variables as $key => $value) {
            $pattern = "/^{$key}=.*$/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envPath, $envContent);
    }
}
