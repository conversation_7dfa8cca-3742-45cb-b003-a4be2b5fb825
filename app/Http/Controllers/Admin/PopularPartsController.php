<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Part;
use App\Models\UserFavorite;
use App\Models\UserSearch;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class PopularPartsController extends Controller
{
    /**
     * Display the popular parts analytics dashboard.
     */
    public function index(Request $request): Response
    {
        $days = $request->get('days', 30);
        $analytics = $this->getPopularPartsAnalytics($days);
        
        return Inertia::render('admin/PopularParts/Index', [
            'analytics' => $analytics,
            'days' => $days,
        ]);
    }

    /**
     * Get comprehensive popular parts analytics.
     */
    private function getPopularPartsAnalytics(int $days): array
    {
        $startDate = now()->subDays($days);
        
        return [
            'overview' => $this->getOverviewStats($startDate),
            'top_parts' => $this->getTopFavoriteParts($startDate),
            'category_breakdown' => $this->getCategoryBreakdown($startDate),
            'trending_parts' => $this->getTrendingParts($startDate),
            'search_correlation' => $this->getSearchCorrelation($startDate),
            'daily_favorites' => $this->getDailyFavoritesData($startDate),
        ];
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats($startDate): array
    {
        // Total parts that have been favorited
        $totalFavoritedParts = UserFavorite::where('favoritable_type', Part::class)
            ->distinct('favoritable_id')
            ->count();

        // Total favorites count
        $totalFavorites = UserFavorite::where('favoritable_type', Part::class)->count();

        // Recent favorites (within the time period)
        $recentFavorites = UserFavorite::where('favoritable_type', Part::class)
            ->where('created_at', '>=', $startDate)
            ->count();

        // Unique users who favorited parts
        $uniqueUsers = UserFavorite::where('favoritable_type', Part::class)
            ->distinct('user_id')
            ->count();

        // Recent unique users
        $recentUniqueUsers = UserFavorite::where('favoritable_type', Part::class)
            ->where('created_at', '>=', $startDate)
            ->distinct('user_id')
            ->count();

        // Average favorites per part
        $avgFavoritesPerPart = $totalFavoritedParts > 0 ? round($totalFavorites / $totalFavoritedParts, 2) : 0;

        return [
            'total_favorited_parts' => $totalFavoritedParts,
            'total_favorites' => $totalFavorites,
            'recent_favorites' => $recentFavorites,
            'unique_users' => $uniqueUsers,
            'recent_unique_users' => $recentUniqueUsers,
            'avg_favorites_per_part' => $avgFavoritesPerPart,
        ];
    }

    /**
     * Get top favorite parts with detailed information.
     */
    private function getTopFavoriteParts($startDate, int $limit = 20): array
    {
        return DB::table('user_favorites')
            ->join('parts', 'user_favorites.favoritable_id', '=', 'parts.id')
            ->join('categories', 'parts.category_id', '=', 'categories.id')
            ->where('user_favorites.favoritable_type', Part::class)
            ->select([
                'parts.id',
                'parts.name',
                'parts.slug',
                'parts.part_number',
                'parts.manufacturer',
                'categories.name as category_name',
                DB::raw('COUNT(user_favorites.id) as total_favorites'),
                DB::raw('COUNT(DISTINCT user_favorites.user_id) as unique_users'),
                DB::raw("COUNT(CASE WHEN user_favorites.created_at >= '{$startDate}' THEN 1 END) as recent_favorites")
            ])
            ->groupBy([
                'parts.id', 'parts.name', 'parts.slug', 'parts.part_number',
                'parts.manufacturer', 'categories.name'
            ])
            ->orderByDesc('total_favorites')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get category breakdown of favorite parts.
     */
    private function getCategoryBreakdown($startDate): array
    {
        return DB::table('user_favorites')
            ->join('parts', 'user_favorites.favoritable_id', '=', 'parts.id')
            ->join('categories', 'parts.category_id', '=', 'categories.id')
            ->where('user_favorites.favoritable_type', Part::class)
            ->select([
                'categories.id',
                'categories.name',
                DB::raw('COUNT(user_favorites.id) as total_favorites'),
                DB::raw('COUNT(DISTINCT parts.id) as unique_parts'),
                DB::raw('COUNT(DISTINCT user_favorites.user_id) as unique_users'),
                DB::raw("COUNT(CASE WHEN user_favorites.created_at >= '{$startDate}' THEN 1 END) as recent_favorites")
            ])
            ->groupBy('categories.id', 'categories.name')
            ->orderByDesc('total_favorites')
            ->get()
            ->toArray();
    }

    /**
     * Get trending parts (parts gaining favorites recently).
     */
    private function getTrendingParts($startDate, int $limit = 10): array
    {
        // Get parts with significant recent activity
        $recentFavorites = DB::table('user_favorites')
            ->join('parts', 'user_favorites.favoritable_id', '=', 'parts.id')
            ->join('categories', 'parts.category_id', '=', 'categories.id')
            ->where('user_favorites.favoritable_type', Part::class)
            ->where('user_favorites.created_at', '>=', $startDate)
            ->select([
                'parts.id',
                'parts.name',
                'parts.slug',
                'parts.manufacturer',
                'categories.name as category_name',
                DB::raw('COUNT(user_favorites.id) as recent_favorites'),
                DB::raw('COUNT(DISTINCT user_favorites.user_id) as recent_unique_users')
            ])
            ->groupBy([
                'parts.id', 'parts.name', 'parts.slug', 
                'parts.manufacturer', 'categories.name'
            ])
            ->having('recent_favorites', '>=', 2) // Only parts with at least 2 recent favorites
            ->orderByDesc('recent_favorites')
            ->limit($limit)
            ->get()
            ->toArray();

        return $recentFavorites;
    }

    /**
     * Get search correlation data for favorite parts.
     */
    private function getSearchCorrelation($startDate): array
    {
        // Get search frequency for top favorite parts
        $topParts = $this->getTopFavoriteParts($startDate, 10);
        $partNames = array_column($topParts, 'name');
        
        if (empty($partNames)) {
            return [];
        }

        $searchData = [];
        foreach ($partNames as $partName) {
            $searchCount = UserSearch::where('search_query', 'LIKE', "%{$partName}%")
                ->where('created_at', '>=', $startDate)
                ->count();
            
            $searchData[] = [
                'part_name' => $partName,
                'search_count' => $searchCount,
            ];
        }

        return $searchData;
    }

    /**
     * Get daily favorites data for charts.
     */
    private function getDailyFavoritesData($startDate): array
    {
        return DB::table('user_favorites')
            ->where('favoritable_type', Part::class)
            ->where('created_at', '>=', $startDate)
            ->select([
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as favorites_count'),
                DB::raw('COUNT(DISTINCT user_id) as unique_users'),
                DB::raw('COUNT(DISTINCT favoritable_id) as unique_parts')
            ])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Export popular parts analytics data.
     */
    public function export(Request $request)
    {
        $days = $request->get('days', 30);
        $format = $request->get('format', 'csv');
        
        $analytics = $this->getPopularPartsAnalytics($days);
        
        if ($format === 'json') {
            return response()->json($analytics);
        }
        
        // CSV export
        $filename = "popular_parts_analytics_" . now()->format('Y-m-d') . ".csv";
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];
        
        $callback = function() use ($analytics) {
            $file = fopen('php://output', 'w');
            
            // Write top parts data
            fputcsv($file, [
                'Part Name', 'Category', 'Manufacturer', 'Part Number', 
                'Total Favorites', 'Unique Users', 'Recent Favorites'
            ]);
            
            foreach ($analytics['top_parts'] as $part) {
                fputcsv($file, [
                    $part->name,
                    $part->category_name,
                    $part->manufacturer ?? 'N/A',
                    $part->part_number ?? 'N/A',
                    $part->total_favorites,
                    $part->unique_users,
                    $part->recent_favorites,
                ]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }
}
