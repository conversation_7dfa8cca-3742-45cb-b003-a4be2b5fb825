<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CountryDetectionService;
use App\Services\PaymentGatewayConfigService;
use App\Services\LocalizedPricingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LocalizationController extends Controller
{
    private CountryDetectionService $countryDetectionService;
    private PaymentGatewayConfigService $gatewayConfigService;
    private LocalizedPricingService $localizedPricingService;

    public function __construct(
        CountryDetectionService $countryDetectionService,
        PaymentGatewayConfigService $gatewayConfigService,
        LocalizedPricingService $localizedPricingService
    ) {
        $this->countryDetectionService = $countryDetectionService;
        $this->gatewayConfigService = $gatewayConfigService;
        $this->localizedPricingService = $localizedPricingService;
    }

    /**
     * Get localization data for the current user.
     */
    public function getLocalizationData(Request $request): JsonResponse
    {
        try {
            // Detect country from request
            $countryData = $this->countryDetectionService->getCountryFromRequest($request);
            
            // Get available payment gateways for the country
            $gatewayData = $this->gatewayConfigService->getAvailableGatewaysForCountry($countryData['country_code']);
            
            // Get localized pricing plans
            $pricingData = $this->localizedPricingService->getPlanRecommendationsForCountry($countryData['country_code']);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'country' => $countryData,
                    'payment_gateways' => $gatewayData,
                    'pricing' => $pricingData,
                    'localization_settings' => [
                        'auto_select_gateway' => true,
                        'show_currency_conversion' => false,
                        'preferred_language' => 'en',
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get localization data',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred',
            ], 500);
        }
    }

    /**
     * Get country-specific pricing plans.
     */
    public function getPricingPlans(Request $request): JsonResponse
    {
        try {
            $countryCode = $request->get('country');
            
            if (!$countryCode) {
                $countryData = $this->countryDetectionService->getCountryFromRequest($request);
                $countryCode = $countryData['country_code'];
            }
            
            $plans = $this->localizedPricingService->getFormattedPlansForCountry($countryCode);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'plans' => $plans,
                    'country_code' => $countryCode,
                    'currency' => $this->countryDetectionService->getLocalizedCurrency($countryCode),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get pricing plans',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred',
            ], 500);
        }
    }

    /**
     * Get available payment gateways for a country.
     */
    public function getPaymentGateways(Request $request): JsonResponse
    {
        try {
            $countryCode = $request->get('country');
            
            if (!$countryCode) {
                $countryData = $this->countryDetectionService->getCountryFromRequest($request);
                $countryCode = $countryData['country_code'];
            }
            
            $gateways = $this->gatewayConfigService->getAvailableGatewaysForCountry($countryCode);
            
            return response()->json([
                'success' => true,
                'data' => $gateways,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get payment gateways',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred',
            ], 500);
        }
    }

    /**
     * Get country detection information.
     */
    public function getCountryInfo(Request $request): JsonResponse
    {
        try {
            $countryData = $this->countryDetectionService->getCountryFromRequest($request);
            
            return response()->json([
                'success' => true,
                'data' => $countryData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to detect country',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred',
            ], 500);
        }
    }

    /**
     * Test country detection with a specific IP.
     */
    public function testCountryDetection(Request $request): JsonResponse
    {
        $request->validate([
            'ip' => 'required|ip',
        ]);
        
        try {
            $testResult = $this->countryDetectionService->testDetection($request->ip);
            
            return response()->json([
                'success' => true,
                'data' => $testResult,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to test country detection',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred',
            ], 500);
        }
    }

    /**
     * Get localization statistics.
     */
    public function getLocalizationStats(): JsonResponse
    {
        try {
            $countryStats = $this->countryDetectionService->getDetectionStats();
            $gatewayStats = $this->gatewayConfigService->getGatewayStatistics();
            $pricingStats = $this->localizedPricingService->getPricingStatistics();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'country_detection' => $countryStats,
                    'payment_gateways' => $gatewayStats,
                    'pricing_plans' => $pricingStats,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get localization statistics',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred',
            ], 500);
        }
    }
}
