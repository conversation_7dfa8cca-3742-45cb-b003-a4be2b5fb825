<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AdSenseService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class AdTrackingController extends Controller
{
    protected AdSenseService $adSenseService;

    public function __construct(AdSenseService $adSenseService)
    {
        $this->adSenseService = $adSenseService;
    }

    /**
     * Track an ad impression.
     */
    public function trackImpression(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'zone' => 'required|string|in:header,sidebar,content,footer,mobile',
            'page' => 'required|string|max:255',
            'ad_slot' => 'nullable|string|max:50',
            'user_type' => 'nullable|string|in:free,premium,admin,guest',
            'timestamp' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid data provided',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $this->adSenseService->recordImpression(
                $request->input('zone'),
                $request->input('page'),
                $request,
                $request->input('ad_slot')
            );

            return response()->json([
                'success' => true,
                'message' => 'Impression tracked successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to track impression',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Track an ad click.
     */
    public function trackClick(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'zone' => 'required|string|in:header,sidebar,content,footer,mobile',
            'page' => 'required|string|max:255',
            'ad_slot' => 'nullable|string|max:50',
            'user_type' => 'nullable|string|in:free,premium,admin,guest',
            'timestamp' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid data provided',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $this->adSenseService->recordClick(
                $request->input('zone'),
                $request->input('page'),
                $request,
                $request->input('ad_slot')
            );

            return response()->json([
                'success' => true,
                'message' => 'Click tracked successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to track click',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get ad configuration for a specific zone and page.
     */
    public function getConfiguration(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'zone' => 'required|string|in:header,sidebar,content,footer,mobile',
            'page' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid data provided',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $config = $this->adSenseService->getAdConfiguration(
                $request->input('zone'),
                $request->input('page'),
                $request->user()
            );

            if (!$config) {
                return response()->json([
                    'success' => false,
                    'message' => 'No ad configuration found for this zone and page',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'zone' => $config->zone,
                    'enabled' => $config->enabled,
                    'ad_slot_id' => $config->ad_slot_id,
                    'ad_format' => $config->ad_format,
                    'ad_sizes' => $config->getAdSizes(),
                    'frequency_rules' => $config->frequency_rules,
                    'targeting_rules' => $config->getTargetingRules(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get ad configuration',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all ad configurations for a page.
     */
    public function getPageConfigurations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid data provided',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $configs = $this->adSenseService->getPageAdConfigurations(
                $request->input('page'),
                $request->user()
            );

            $formattedConfigs = [];
            foreach ($configs as $zone => $config) {
                $formattedConfigs[$zone] = [
                    'enabled' => $config->enabled,
                    'ad_slot_id' => $config->ad_slot_id,
                    'ad_format' => $config->ad_format,
                    'ad_sizes' => $config->getAdSizes(),
                    'frequency_rules' => $config->frequency_rules,
                    'max_ads_per_page' => $config->getMaxAdsPerPage(),
                    'delay_seconds' => $config->getDelaySeconds(),
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'page' => $request->input('page'),
                    'should_show_ads' => $this->adSenseService->shouldShowAds($request->user()),
                    'zones' => $formattedConfigs,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get page ad configurations',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check if ads should be shown to the current user.
     */
    public function shouldShowAds(Request $request): JsonResponse
    {
        try {
            $shouldShow = $this->adSenseService->shouldShowAds($request->user());

            return response()->json([
                'success' => true,
                'data' => [
                    'should_show_ads' => $shouldShow,
                    'user_type' => $request->user() ? 
                        ($request->user()->isAdmin() ? 'admin' : 
                         ($request->user()->isPremium() ? 'premium' : 'free')) : 'guest',
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check ad eligibility',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
