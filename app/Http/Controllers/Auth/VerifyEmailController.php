<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\RedirectResponse;

class VerifyEmailController extends Controller
{
    /**
     * Mark the authenticated user's email address as verified.
     */
    public function __invoke(EmailVerificationRequest $request): RedirectResponse
    {
        $user = $request->user();
        
        if ($user->hasVerifiedEmail()) {
            // Check if user can access admin dashboard
            if ($user->canAccessAdminDashboard()) {
                return redirect()->intended(route('admin.dashboard', absolute: false).'?verified=1');
            }
            return redirect()->intended(route('dashboard', absolute: false).'?verified=1');
        }

        if ($user->markEmailAsVerified()) {
            /** @var \Illuminate\Contracts\Auth\MustVerifyEmail $user */
            $user = $request->user();

            // Update user status to active after email verification
            if ($user->status === 'pending') {
                $user->update(['status' => 'active']);
            }

            event(new Verified($user));
        }

        // Check if user can access admin dashboard
        if ($user->canAccessAdminDashboard()) {
            return redirect()->intended(route('admin.dashboard', absolute: false).'?verified=1');
        }
        return redirect()->intended(route('dashboard', absolute: false).'?verified=1');
    }
}
