<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\UserNotification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class NotificationController extends Controller
{
    /**
     * Display a listing of the user's notifications.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        
        // Get filter parameters
        $type = $request->get('type', 'all');
        $status = $request->get('status', 'all');
        $perPage = $request->get('per_page', 15);
        
        // Build query
        $query = $user->notifications()
            ->with('sentBy:id,name,email')
            ->latest();
        
        // Apply filters
        if ($type !== 'all') {
            $query->where('type', $type);
        }
        
        if ($status === 'unread') {
            $query->unread();
        } elseif ($status === 'read') {
            $query->read();
        }
        
        $notifications = $query->paginate($perPage);

        // Format the notifications data to match the expected structure
        $notificationsData = [
            'data' => $notifications->items(),
            'meta' => [
                'total' => $notifications->total(),
                'per_page' => $notifications->perPage(),
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'from' => $notifications->firstItem(),
                'to' => $notifications->lastItem(),
            ],
            'links' => $notifications->linkCollection()->toArray(),
        ];

        // Get statistics
        $stats = [
            'total' => $user->notifications()->count(),
            'unread' => $user->notifications()->unread()->count(),
            'read' => $user->notifications()->read()->count(),
            'recent' => $user->notifications()->recent(7)->count(),
        ];

        return Inertia::render('user/notifications/Index', [
            'notifications' => $notificationsData,
            'stats' => $stats,
            'filters' => [
                'type' => $type,
                'status' => $status,
                'per_page' => $perPage,
            ],
            'notification_types' => [
                'all' => 'All Types',
                'info' => 'Information',
                'warning' => 'Warning',
                'success' => 'Success',
                'error' => 'Error',
                'announcement' => 'Announcement',
            ],
        ]);
    }

    /**
     * Display the specified notification.
     */
    public function show(Request $request, UserNotification $notification): Response
    {
        // Note: The route model binding with resolveRouteBinding already ensures
        // that the notification belongs to the authenticated user, so we don't
        // need the additional check here. If the notification doesn't belong
        // to the user, Laravel will return a 404 automatically.

        // Mark as read if not already read
        if (!$notification->isRead()) {
            $notification->markAsRead();
        }

        $notification->load('sentBy:id,name,email');

        return Inertia::render('user/notifications/Show', [
            'notification' => $notification,
        ]);
    }

    /**
     * Mark a notification as read.
     */
    public function markAsRead(Request $request, UserNotification $notification): RedirectResponse
    {
        // Note: Route model binding ensures the notification belongs to the authenticated user
        try {
            $notification->markAsRead();
            return redirect()->back()->with('success', 'Notification marked as read.');
        } catch (\Exception $e) {
            \Log::error('Failed to mark notification as read', [
                'notification_id' => $notification->id,
                'user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);
            return redirect()->back()->with('error', 'Failed to mark notification as read. Please try again.');
        }
    }

    /**
     * Mark a notification as unread.
     */
    public function markAsUnread(Request $request, UserNotification $notification): RedirectResponse
    {
        // Note: Route model binding ensures the notification belongs to the authenticated user
        try {
            $notification->markAsUnread();
            return redirect()->back()->with('success', 'Notification marked as unread.');
        } catch (\Exception $e) {
            \Log::error('Failed to mark notification as unread', [
                'notification_id' => $notification->id,
                'user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);
            return redirect()->back()->with('error', 'Failed to mark notification as unread. Please try again.');
        }
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(Request $request): RedirectResponse
    {
        $user = $request->user();

        try {
            $count = $user->notifications()
                ->unread()
                ->update(['read_at' => now()]);

            $message = $count > 0
                ? "Marked {$count} notifications as read."
                : "No unread notifications to mark as read.";

            return redirect()->back()->with('success', $message);
        } catch (\Exception $e) {
            \Log::error('Failed to mark all notifications as read', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return redirect()->back()->with('error', 'Failed to mark all notifications as read. Please try again.');
        }
    }

    /**
     * Get unread notification count for the user.
     */
    public function getUnreadCount(Request $request): JsonResponse
    {
        $user = $request->user();
        $count = $user->notifications()->unread()->count();

        return response()->json([
            'count' => $count,
        ]);
    }

    /**
     * Get recent notifications for dashboard.
     */
    public function getRecent(Request $request): JsonResponse
    {
        $user = $request->user();

        $notifications = $user->notifications()
            ->with('sentBy:id,name')
            ->latest()
            ->take(5)
            ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
}
