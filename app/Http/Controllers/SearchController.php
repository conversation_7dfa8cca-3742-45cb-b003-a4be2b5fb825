<?php

namespace App\Http\Controllers;

use App\Events\PartViewed;
use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SearchConfiguration;
use App\Models\User;
use App\Services\SearchService;
use App\Services\CopyProtectionService;
use App\Services\CompatibilityColumnService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class SearchController extends Controller
{
    public function __construct(
        private SearchService $searchService,
        private CopyProtectionService $copyProtectionService,
        private CompatibilityColumnService $compatibilityColumnService
    ) {
        //
    }

    /**
     * Show the search page.
     */
    public function index()
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Redirect guests to home page for search
        if (!$user) {
            return redirect()->route('home');
        }

        $filters = $this->searchService->getAvailableFilters();

        return Inertia::render('search/index', [
            'filters' => $filters,
        ]);
    }

    /**
     * Perform search.
     */
    public function search(Request $request)
    {
        // Clean up invalid parameters before processing
        $cleanedParams = [];
        foreach ($request->all() as $key => $value) {
            // Only keep valid parameters with valid values
            if (!in_array($value, ['null', null, '', 'all', false], true) && $value !== '') {
                $cleanedParams[$key] = $value;
            }
        }

        // Create a new request with cleaned parameters
        $cleanedRequest = $request->duplicate($cleanedParams);

        $user = $request->user();
        $results = $this->searchService->searchParts($cleanedRequest, $user);

        if (isset($results['error'])) {
            if ($request->header('X-Inertia')) {
                // For Inertia requests, redirect to subscription plans with error message
                return redirect()->route('subscription.plans')->with([
                    'error' => $results['error'],
                    'message' => $results['message'] ?? 'You have reached your daily search limit.',
                ]);
            }
            return response()->json($results, 429);
        }

        if ($request->wantsJson()) {
            return response()->json($results);
        }

        return Inertia::render('search/results', $results);
    }

    /**
     * Get search suggestions.
     */
    public function suggestions(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $user = $request->user();
        $categoryId = $request->get('category_id');
        $brandId = $request->get('brand_id');

        // Get suggestions with user context for limit checking
        $suggestions = $this->searchService->getSuggestions($query, 10, $categoryId, $brandId, $user);

        // Handle error responses (e.g., search limit exceeded)
        if (isset($suggestions['error'])) {
            return response()->json($suggestions, 429);
        }

        return response()->json($suggestions);
    }

    /**
     * Search brands for compatibility selection.
     */
    public function searchBrands(Request $request)
    {
        $query = $request->get('q', '');
        $limit = min((int) $request->get('limit', 20), 50); // Max 50 results

        $brandsQuery = \App\Models\Brand::active()->orderBy('name');

        if (!empty($query)) {
            $brandsQuery->where('name', 'LIKE', '%' . $query . '%');
        }

        $brands = $brandsQuery->limit($limit)->get()->map(function ($brand) {
            return [
                'value' => (string) $brand->id,
                'label' => $brand->name,
                'image' => $brand->logo_url,
                'data' => [
                    'id' => $brand->id,
                    'name' => $brand->name,
                    'logo_url' => $brand->logo_url,
                ]
            ];
        });

        return response()->json($brands);
    }

    /**
     * Search models for compatibility selection.
     */
    public function searchModels(Request $request)
    {
        $query = $request->get('q', '');
        $brandId = $request->get('brand_id');
        $excludeCompatible = $request->get('exclude_compatible');
        $partId = $request->get('part_id');
        $limit = min((int) $request->get('limit', 20), 50); // Max 50 results

        $modelsQuery = \App\Models\MobileModel::with('brand')->active()->orderBy('name');

        if (!empty($query)) {
            $modelsQuery->where(function ($q) use ($query) {
                $q->where('name', 'LIKE', '%' . $query . '%')
                  ->orWhere('model_number', 'LIKE', '%' . $query . '%')
                  ->orWhereHas('brand', function ($brandQuery) use ($query) {
                      $brandQuery->where('name', 'LIKE', '%' . $query . '%');
                  });
            });
        }

        if ($brandId) {
            $modelsQuery->where('brand_id', $brandId);
        }

        // Exclude models that are already compatible with the part
        if ($excludeCompatible && $partId) {
            $modelsQuery->whereDoesntHave('parts', function ($q) use ($partId) {
                $q->where('parts.id', $partId);
            });
        }

        $models = $modelsQuery->limit($limit)->get()->map(function ($model) {
            $description = [];
            if ($model->model_number) {
                $description[] = $model->model_number;
            }
            if ($model->release_year) {
                $description[] = (string) $model->release_year;
            }

            return [
                'value' => (string) $model->id,
                'label' => $model->name,
                'description' => implode(' • ', $description),
                'image' => $model->brand->logo_url,
                'data' => [
                    'id' => $model->id,
                    'name' => $model->name,
                    'model_number' => $model->model_number,
                    'release_year' => $model->release_year,
                    'brand' => $model->brand,
                ]
            ];
        });

        return response()->json($models);
    }

    /**
     * Show brand details.
     */
    public function showBrand(Brand $brand)
    {
        /** @var User|null $user */
        $user = Auth::user();
        $brand->load(['models' => function ($query) {
            $query->active()->with('parts');
        }]);

        // Log brand view activity
        if ($user) {
            $user->logActivity(
                'brand_viewed',
                "User viewed brand: {$brand->name}",
                [
                    'brand_id' => $brand->id,
                    'brand_name' => $brand->name,
                    'models_count' => $brand->models->count(),
                ]
            );
        }

        return Inertia::render('search/brand-details', [
            'brand' => $brand,
        ]);
    }

    /**
     * Show category details.
     */
    public function showCategory(Category $category)
    {
        /** @var User|null $user */
        $user = Auth::user();
        $category->load(['parts' => function ($query) {
            $query->active()->with(['models.brand']);
        }, 'children']);

        // Log category view activity
        if ($user) {
            $user->logActivity(
                'category_viewed',
                "User viewed category: {$category->name}",
                [
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'parts_count' => $category->parts->count(),
                    'children_count' => $category->children->count(),
                ]
            );
        }

        return Inertia::render('search/category-details', [
            'category' => $category,
        ]);
    }

    /**
     * Show mobile model details for admin users.
     *
     * This route is only accessible to admin and content manager users.
     * Regular users should use the public model view route.
     */
    public function showModel(MobileModel $model)
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Ensure only admin/content managers can access this route
        if (!$user || (!$user->isAdmin() && !$user->canManageContent())) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $model->load(['brand', 'parts' => function ($query) {
            $query->active()->with('category');
        }]);

        // Log model view activity for admin users
        $user->logActivity(
            'admin_model_viewed',
            "Admin viewed model: {$model->name}",
            [
                'model_id' => $model->id,
                'model_name' => $model->name,
                'brand_name' => $model->brand->name ?? null,
                'release_year' => $model->release_year,
                'parts_count' => $model->parts->count(),
            ]
        );

        return Inertia::render('search/model-details', [
            'model' => $model,
        ]);
    }

    /**
     * Show part details.
     */
    public function showPart(Part $part)
    {
        /** @var User|null $user */
        $user = Auth::user();
        $part->load(['category', 'models.brand']);
        $relatedParts = $this->searchService->getRelatedParts($part);

        // Get compatibility column configuration for public view
        $compatibilityColumns = $this->compatibilityColumnService->getVisibleColumns(false);

        // Get copy protection configuration for the current user (JavaScript format)
        $copyProtectionConfig = $this->copyProtectionService->getJavaScriptConfig($user);

        // Get guest search configuration for blur effect
        $guestSearchConfig = null;
        if (!$user) {
            // Apply blur effect to compatible models for guest users
            $enablePartialResults = SearchConfiguration::get('enable_partial_results', true);
            $maxVisibleResults = SearchConfiguration::get('guest_max_visible_results', 5);
            $blurIntensity = SearchConfiguration::get('blur_intensity', 'medium');
            $showSignupCta = SearchConfiguration::get('show_signup_cta', true);

            $guestSearchConfig = [
                'enable_partial_results' => $enablePartialResults,
                'max_visible_results' => $maxVisibleResults,
                'blur_intensity' => $blurIntensity,
                'show_signup_cta' => $showSignupCta,
            ];

            // Apply blur logic to compatible models if enabled
            if ($enablePartialResults && $part->models) {
                $models = $part->models->map(function ($model, $index) use ($maxVisibleResults) {
                    $modelArray = $model->toArray();
                    $modelArray['is_blurred'] = $index >= $maxVisibleResults;
                    return $modelArray;
                });

                // Update the part's models with blur information
                $part->setRelation('models', $models);
            }
        }

        // Log part view activity
        if ($user) {
            $user->logActivity(
                'part_viewed',
                "User viewed part: {$part->name}",
                [
                    'part_id' => $part->id,
                    'part_name' => $part->name,
                    'part_number' => $part->part_number,
                    'category' => $part->category->name ?? null,
                    'manufacturer' => $part->manufacturer,
                ]
            );

            // Dispatch part viewed event for plugins
            PartViewed::dispatch($part, $user);
        }

        $data = [
            'part' => $part,
            'relatedParts' => $relatedParts,
            'copyProtectionConfig' => $copyProtectionConfig,
            'compatibilityColumns' => $compatibilityColumns,
        ];

        // Only add guest search config for non-authenticated users
        if (!$user && $guestSearchConfig) {
            $data['guestSearchConfig'] = $guestSearchConfig;
        }

        return Inertia::render('search/part-details', $data);
    }

    /**
     * Get filters data for AJAX requests.
     */
    public function filters()
    {
        return response()->json($this->searchService->getAvailableFilters());
    }

    /**
     * Show category-specific search page.
     */
    public function searchByCategory(Category $category, Request $request)
    {
        /** @var User|null $user */
        $user = $request->user();

        // If no search query provided, show the search interface
        if (!$request->has('q') || empty($request->get('q'))) {
            $filters = $this->searchService->getAvailableFilters();

            return Inertia::render('search/category-search', [
                'category' => $category,
                'filters' => $filters,
            ]);
        }

        // Clean up invalid parameters before processing
        $cleanedParams = [];
        foreach ($request->all() as $key => $value) {
            // Only keep valid parameters with valid values
            if (!in_array($value, ['null', null, '', 'all', false], true) && $value !== '') {
                $cleanedParams[$key] = $value;
            }
        }

        // Create a new request with cleaned parameters
        $cleanedRequest = $request->duplicate($cleanedParams);

        // Perform the search with category filter
        $cleanedRequest->merge(['category_id' => $category->id]);

        $results = $this->searchService->searchParts($cleanedRequest, $user);

        if (isset($results['error'])) {
            return response()->json($results, 429);
        }

        if ($request->wantsJson()) {
            return response()->json($results);
        }

        return Inertia::render('search/category-search', array_merge($results, [
            'category' => $category,
        ]));
    }

    /**
     * Show brand-specific search page.
     */
    public function searchByBrand(Brand $brand, Request $request)
    {
        /** @var User|null $user */
        $user = $request->user();

        // If no search query provided, show the search interface
        if (!$request->has('q') || empty($request->get('q'))) {
            $filters = $this->searchService->getAvailableFilters();

            return Inertia::render('search/brand-search', [
                'brand' => $brand,
                'filters' => $filters,
            ]);
        }

        // Clean up invalid parameters before processing
        $cleanedParams = [];
        foreach ($request->all() as $key => $value) {
            // Only keep valid parameters with valid values
            if (!in_array($value, ['null', null, '', 'all', false], true) && $value !== '') {
                $cleanedParams[$key] = $value;
            }
        }

        // Create a new request with cleaned parameters
        $cleanedRequest = $request->duplicate($cleanedParams);

        // Perform the search with brand filter
        $cleanedRequest->merge(['brand_id' => $brand->id]);
        $results = $this->searchService->searchParts($cleanedRequest, $user);

        if (isset($results['error'])) {
            if ($request->header('X-Inertia')) {
                // For Inertia requests, redirect to subscription plans with error message
                return redirect()->route('subscription.plans')->with([
                    'error' => $results['error'],
                    'message' => $results['message'] ?? 'You have reached your daily search limit.',
                ]);
            }
            return response()->json($results, 429);
        }

        if ($request->wantsJson()) {
            return response()->json($results);
        }

        return Inertia::render('search/brand-search', array_merge($results, [
            'brand' => $brand,
        ]));
    }

    /**
     * Show model search page for registered users.
     */
    public function searchModel(Request $request)
    {
        /** @var User|null $user */
        $user = $request->user();

        // Get subscription status and limits
        $isSubscribed = false;
        $hasUnlimitedAccess = false;
        $maxVisibleResults = 10; // Default for non-subscribers

        if ($user) {
            $isSubscribed = $user->hasActiveSubscription();
            $hasUnlimitedAccess = $user->isAdmin() || $isSubscribed;

            // Log model search activity for authenticated users
            $user->logActivity(
                'model_search_accessed',
                "User accessed model search page",
                [
                    'is_subscribed' => $isSubscribed,
                    'search_query' => $request->get('q', ''),
                ]
            );
        }

        // Get admin-configured limits for non-subscribers
        if (!$hasUnlimitedAccess) {
            $maxVisibleResults = \App\Models\SearchConfiguration::get('guest_max_visible_results', 10);
        }

        // Get available filters
        $brands = \App\Models\Brand::active()->orderBy('name')->get();
        $releaseYears = \App\Models\MobileModel::whereNotNull('release_year')
            ->distinct()
            ->orderBy('release_year', 'desc')
            ->pluck('release_year');

        // If no search query provided, show the search interface
        if (!$request->has('q') || empty($request->get('q'))) {
            return Inertia::render('search/model-search', [
                'brands' => $brands,
                'releaseYears' => $releaseYears,
                'isSubscribed' => $isSubscribed,
                'hasUnlimitedAccess' => $hasUnlimitedAccess,
                'maxVisibleResults' => $maxVisibleResults,
            ]);
        }

        // Perform model search
        $query = \App\Models\MobileModel::with(['brand', 'parts.category'])
            ->active();

        $searchTerm = $request->get('q');
        if ($searchTerm) {
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('model_number', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('brand', function ($brandQuery) use ($searchTerm) {
                      $brandQuery->where('name', 'LIKE', "%{$searchTerm}%");
                  });
            });
        }

        // Apply brand filter
        if ($brandId = $request->get('brand_id')) {
            $query->where('brand_id', $brandId);
        }

        // Apply release year filter
        if ($releaseYear = $request->get('release_year')) {
            $query->where('release_year', $releaseYear);
        }

        // Set pagination parameters
        $perPage = 16; // Maximum 16 results per page as requested
        $currentPage = $request->get('page', 1);

        // For non-subscribers, we need to handle pagination differently
        if (!$hasUnlimitedAccess) {
            // Get total count for display purposes
            $totalModelsCount = $query->count();

            // Calculate how many results the user can see across all pages
            $maxTotalResults = $maxVisibleResults;
            $maxPages = ceil($maxTotalResults / $perPage);

            // If current page exceeds what user can access, redirect to last accessible page
            if ($currentPage > $maxPages && $maxPages > 0) {
                return redirect()->route('search.model', array_merge($request->all(), ['page' => $maxPages]));
            }

            // Calculate offset for this page within the allowed results
            $offset = ($currentPage - 1) * $perPage;

            // Get models for this page (limited by subscription)
            $visibleModels = $query->skip($offset)->take($perPage)->get();

            // Calculate hidden models count
            $hiddenModelsCount = max(0, $totalModelsCount - $maxTotalResults);

            // Create pagination data manually for limited results
            $paginationData = [
                'data' => $visibleModels,
                'current_page' => $currentPage,
                'last_page' => $maxPages,
                'per_page' => $perPage,
                'total' => min($totalModelsCount, $maxTotalResults),
                'from' => $offset + 1,
                'to' => min($offset + $visibleModels->count(), $maxTotalResults),
            ];
        } else {
            // For subscribers, use standard pagination
            $paginatedModels = $query->paginate($perPage, ['*'], 'page', $currentPage);
            $totalModelsCount = $paginatedModels->total();
            $hiddenModelsCount = 0;

            $paginationData = [
                'data' => $paginatedModels->items(),
                'current_page' => $paginatedModels->currentPage(),
                'last_page' => $paginatedModels->lastPage(),
                'per_page' => $paginatedModels->perPage(),
                'total' => $paginatedModels->total(),
                'from' => $paginatedModels->firstItem() ?: 0,
                'to' => $paginatedModels->lastItem() ?: 0,
            ];
        }

        return Inertia::render('search/model-search', [
            'models' => $paginationData,
            'totalModelsCount' => $totalModelsCount,
            'hiddenModelsCount' => $hiddenModelsCount,
            'brands' => $brands,
            'releaseYears' => $releaseYears,
            'isSubscribed' => $isSubscribed,
            'hasUnlimitedAccess' => $hasUnlimitedAccess,
            'maxVisibleResults' => $maxVisibleResults,
            'searchQuery' => $searchTerm,
            'filters' => [
                'brand_id' => $request->get('brand_id'),
                'release_year' => $request->get('release_year'),
            ],
        ]);
    }

    /**
     * Show categories listing page for selection.
     */
    public function listCategories()
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Redirect guests to home page
        if (!$user) {
            return redirect()->route('home');
        }

        $categories = Category::active()
            ->withCount('parts')
            ->orderBy('name')
            ->get();

        return Inertia::render('search/categories-list', [
            'categories' => $categories,
        ]);
    }

    /**
     * Show brands listing page for selection.
     */
    public function listBrands()
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Redirect guests to home page
        if (!$user) {
            return redirect()->route('home');
        }

        $brands = Brand::active()
            ->withCount('models')
            ->orderBy('name')
            ->get();

        return Inertia::render('search/brands-list', [
            'brands' => $brands,
        ]);
    }
}
