<?php

namespace App\Services;

use App\Models\PaymentGatewayConfig;
use Illuminate\Support\Facades\Log;

class PaymentGatewayConfigService
{
    private CountryDetectionService $countryDetectionService;

    public function __construct(CountryDetectionService $countryDetectionService)
    {
        $this->countryDetectionService = $countryDetectionService;
    }

    /**
     * Get available payment gateways for a country.
     */
    public function getAvailableGatewaysForCountry(string $countryCode): array
    {
        $countryCode = strtoupper($countryCode);
        $currency = $this->countryDetectionService->getLocalizedCurrency($countryCode);
        
        $gateways = PaymentGatewayConfig::getAvailableForCountryAndCurrency($countryCode, $currency);
        
        return [
            'country_code' => $countryCode,
            'currency' => $currency,
            'primary_gateway' => $this->getPrimaryGateway($countryCode),
            'available_gateways' => $gateways->map->toFrontendArray()->toArray(),
            'gateway_preferences' => $this->getGatewayPreferences($countryCode),
        ];
    }

    /**
     * Get primary payment gateway for a country.
     */
    public function getPrimaryGateway(string $countryCode): ?array
    {
        $primary = PaymentGatewayConfig::getPrimaryForCountry($countryCode);
        return $primary ? $primary->toFrontendArray() : null;
    }

    /**
     * Get gateway preferences for a country.
     */
    public function getGatewayPreferences(string $countryCode): array
    {
        $countryCode = strtoupper($countryCode);
        
        if ($countryCode === 'BD') {
            return [
                'primary' => 'shurjopay',
                'secondary' => ['offline'],
                'currency' => 'BDT',
                'recommended_order' => ['shurjopay', 'offline'],
            ];
        }
        
        return [
            'primary' => 'paddle',
            'secondary' => ['coinbase_commerce', 'offline'],
            'currency' => 'USD',
            'recommended_order' => ['paddle', 'coinbase_commerce', 'offline'],
        ];
    }

    /**
     * Enable a payment gateway.
     */
    public function enableGateway(string $gatewayName): bool
    {
        try {
            $gateway = PaymentGatewayConfig::where('gateway_name', $gatewayName)->first();
            
            if (!$gateway) {
                Log::warning('Attempted to enable non-existent gateway', ['gateway' => $gatewayName]);
                return false;
            }
            
            $gateway->update(['is_enabled' => true]);
            
            Log::info('Payment gateway enabled', [
                'gateway' => $gatewayName,
                'display_name' => $gateway->display_name,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to enable payment gateway', [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Disable a payment gateway.
     */
    public function disableGateway(string $gatewayName): bool
    {
        try {
            $gateway = PaymentGatewayConfig::where('gateway_name', $gatewayName)->first();
            
            if (!$gateway) {
                Log::warning('Attempted to disable non-existent gateway', ['gateway' => $gatewayName]);
                return false;
            }
            
            $gateway->update(['is_enabled' => false]);
            
            Log::info('Payment gateway disabled', [
                'gateway' => $gatewayName,
                'display_name' => $gateway->display_name,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to disable payment gateway', [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Update gateway configuration.
     */
    public function updateGatewayConfig(string $gatewayName, array $config): bool
    {
        try {
            $gateway = PaymentGatewayConfig::where('gateway_name', $gatewayName)->first();
            
            if (!$gateway) {
                Log::warning('Attempted to update non-existent gateway', ['gateway' => $gatewayName]);
                return false;
            }
            
            $gateway->update($config);
            
            Log::info('Payment gateway configuration updated', [
                'gateway' => $gatewayName,
                'updated_fields' => array_keys($config),
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update payment gateway configuration', [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get all gateway configurations for admin.
     */
    public function getAllGatewayConfigs(): array
    {
        $gateways = PaymentGatewayConfig::ordered()->get();
        
        return $gateways->map(function ($gateway) {
            return array_merge($gateway->toArray(), [
                'status' => $gateway->is_enabled ? 'enabled' : 'disabled',
                'configuration_status' => $this->getConfigurationStatus($gateway),
            ]);
        })->toArray();
    }

    /**
     * Get configuration status for a gateway.
     */
    private function getConfigurationStatus(PaymentGatewayConfig $gateway): string
    {
        if (!$gateway->requires_api_keys) {
            return 'configured'; // Offline payments don't need API keys
        }
        
        // Check if the gateway has proper configuration
        switch ($gateway->gateway_name) {
            case 'paddle':
                return config('paddle.vendor_id') && config('paddle.api_key') ? 'configured' : 'not_configured';
            case 'shurjopay':
                return config('shurjopay.username') && config('shurjopay.password') ? 'configured' : 'not_configured';
            case 'coinbase_commerce':
                return config('coinbase_commerce.api_key') ? 'configured' : 'not_configured';
            default:
                return 'unknown';
        }
    }

    /**
     * Check if a gateway is properly configured.
     */
    public function isGatewayConfigured(string $gatewayName): bool
    {
        $gateway = PaymentGatewayConfig::where('gateway_name', $gatewayName)->first();
        
        if (!$gateway || !$gateway->is_enabled) {
            return false;
        }
        
        return $this->getConfigurationStatus($gateway) === 'configured';
    }

    /**
     * Get gateway statistics.
     */
    public function getGatewayStatistics(): array
    {
        $total = PaymentGatewayConfig::count();
        $enabled = PaymentGatewayConfig::enabled()->count();
        $configured = PaymentGatewayConfig::get()->filter(function ($gateway) {
            return $this->getConfigurationStatus($gateway) === 'configured';
        })->count();
        
        return [
            'total_gateways' => $total,
            'enabled_gateways' => $enabled,
            'configured_gateways' => $configured,
            'disabled_gateways' => $total - $enabled,
            'configuration_completion' => $total > 0 ? round(($configured / $total) * 100, 1) : 0,
        ];
    }

    /**
     * Initialize default gateway configurations.
     */
    public function initializeDefaults(): void
    {
        PaymentGatewayConfig::seedDefaults();
        Log::info('Payment gateway default configurations initialized');
    }

    /**
     * Validate gateway configuration.
     */
    public function validateGatewayConfig(string $gatewayName, array $config): array
    {
        $errors = [];
        
        // Common validations
        if (empty($config['display_name'])) {
            $errors[] = 'Display name is required';
        }
        
        if (!isset($config['is_enabled']) || !is_bool($config['is_enabled'])) {
            $errors[] = 'Enabled status must be a boolean';
        }
        
        if (!empty($config['supported_currencies']) && !is_array($config['supported_currencies'])) {
            $errors[] = 'Supported currencies must be an array';
        }
        
        if (!empty($config['supported_countries']) && !is_array($config['supported_countries'])) {
            $errors[] = 'Supported countries must be an array';
        }
        
        // Gateway-specific validations
        switch ($gatewayName) {
            case 'shurjopay':
                if (!empty($config['supported_currencies']) && !in_array('BDT', $config['supported_currencies'])) {
                    $errors[] = 'SurjoPay must support BDT currency';
                }
                break;
            case 'paddle':
            case 'coinbase_commerce':
                if (!empty($config['supported_currencies']) && !in_array('USD', $config['supported_currencies'])) {
                    $errors[] = 'This gateway must support USD currency';
                }
                break;
        }
        
        return $errors;
    }
}
