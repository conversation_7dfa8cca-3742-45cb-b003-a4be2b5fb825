<?php

namespace App\Services;

use App\Models\PricingPlan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LocalizedPricingService
{
    private CountryDetectionService $countryDetectionService;

    public function __construct(CountryDetectionService $countryDetectionService)
    {
        $this->countryDetectionService = $countryDetectionService;
    }

    /**
     * Get localized pricing plans for a country.
     */
    public function getLocalizedPlansForCountry(string $countryCode): \Illuminate\Database\Eloquent\Collection
    {
        $countryCode = strtoupper($countryCode);
        $currency = $this->countryDetectionService->getLocalizedCurrency($countryCode);
        
        // Cache key for localized plans
        $cacheKey = "localized_plans_{$countryCode}_{$currency}";
        
        return Cache::remember($cacheKey, 3600, function () use ($countryCode, $currency) {
            return $this->fetchLocalizedPlans($countryCode, $currency);
        });
    }

    /**
     * Fetch localized pricing plans from database.
     */
    private function fetchLocalizedPlans(string $countryCode, string $currency): \Illuminate\Database\Eloquent\Collection
    {
        if ($countryCode === 'BD') {
            // For Bangladesh, get BDT plans first, fallback to USD if needed
            $plans = PricingPlan::where('currency', 'BDT')
                ->active()
                ->public()
                ->ordered()
                ->get();
                
            // If no BDT plans found, fallback to USD plans
            if ($plans->isEmpty()) {
                Log::warning('No BDT plans found for Bangladesh, falling back to USD plans');
                $plans = PricingPlan::where('currency', 'USD')
                    ->active()
                    ->public()
                    ->ordered()
                    ->get();
            }
        } else {
            // For other countries, get USD plans
            $plans = PricingPlan::where('currency', 'USD')
                ->active()
                ->public()
                ->ordered()
                ->get();
        }
        
        return $plans;
    }

    /**
     * Get a specific localized plan by name and country.
     */
    public function getLocalizedPlan(string $planName, string $countryCode): ?PricingPlan
    {
        $countryCode = strtoupper($countryCode);
        
        if ($countryCode === 'BD') {
            // Try to get BDT version first
            $bdtPlan = PricingPlan::where('name', $planName . '_bd')
                ->where('currency', 'BDT')
                ->active()
                ->first();
                
            if ($bdtPlan) {
                return $bdtPlan;
            }
            
            // Fallback to USD plan
            Log::info('BDT plan not found, falling back to USD plan', [
                'requested_plan' => $planName,
                'country' => $countryCode
            ]);
        }
        
        // Get USD plan for other countries or as fallback
        return PricingPlan::where('name', $planName)
            ->where('currency', 'USD')
            ->active()
            ->first();
    }

    /**
     * Get pricing plans with localized formatting.
     */
    public function getFormattedPlansForCountry(string $countryCode): array
    {
        $plans = $this->getLocalizedPlansForCountry($countryCode);
        $currency = $this->countryDetectionService->getLocalizedCurrency($countryCode);
        
        return $plans->map(function ($plan) use ($currency, $countryCode) {
            return $this->formatPlanForCountry($plan, $countryCode);
        })->toArray();
    }

    /**
     * Format a pricing plan for a specific country.
     */
    public function formatPlanForCountry(PricingPlan $plan, string $countryCode): array
    {
        $countryCode = strtoupper($countryCode);
        $currency = $plan->currency;
        
        // Format price based on currency
        $formattedPrice = $this->formatPrice($plan->price, $currency);
        
        // Get currency symbol
        $currencySymbol = $this->getCurrencySymbol($currency);
        
        return array_merge($plan->toArray(), [
            'formatted_price' => $formattedPrice,
            'currency_symbol' => $currencySymbol,
            'localized_for_country' => $countryCode,
            'is_localized' => $this->isPlanLocalized($plan, $countryCode),
            'original_plan_id' => $this->getOriginalPlanId($plan),
        ]);
    }

    /**
     * Format price based on currency.
     */
    private function formatPrice(float $price, string $currency): string
    {
        switch ($currency) {
            case 'BDT':
                return '৳' . number_format($price, 0);
            case 'USD':
                return '$' . number_format($price, 2);
            default:
                return $currency . ' ' . number_format($price, 2);
        }
    }

    /**
     * Get currency symbol.
     */
    private function getCurrencySymbol(string $currency): string
    {
        $symbols = [
            'BDT' => '৳',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
        ];
        
        return $symbols[$currency] ?? $currency;
    }

    /**
     * Check if a plan is localized for a country.
     */
    private function isPlanLocalized(PricingPlan $plan, string $countryCode): bool
    {
        if ($countryCode === 'BD') {
            return $plan->currency === 'BDT';
        }
        
        return $plan->currency === 'USD';
    }

    /**
     * Get original plan ID for localized plans.
     */
    private function getOriginalPlanId(PricingPlan $plan): ?int
    {
        $metadata = $plan->metadata ?? [];
        return $metadata['base_plan_id'] ?? null;
    }

    /**
     * Get plan recommendations for a country.
     */
    public function getPlanRecommendationsForCountry(string $countryCode): array
    {
        $plans = $this->getLocalizedPlansForCountry($countryCode);
        $currency = $this->countryDetectionService->getLocalizedCurrency($countryCode);
        
        // Find popular and recommended plans
        $popularPlan = $plans->where('is_popular', true)->first();
        $freePlan = $plans->where('price', 0)->first();
        $premiumPlans = $plans->where('price', '>', 0)->sortBy('price');
        
        return [
            'currency' => $currency,
            'country_code' => $countryCode,
            'popular_plan' => $popularPlan ? $this->formatPlanForCountry($popularPlan, $countryCode) : null,
            'free_plan' => $freePlan ? $this->formatPlanForCountry($freePlan, $countryCode) : null,
            'premium_plans' => $premiumPlans->map(function ($plan) use ($countryCode) {
                return $this->formatPlanForCountry($plan, $countryCode);
            })->values()->toArray(),
            'total_plans' => $plans->count(),
        ];
    }

    /**
     * Clear localized pricing cache.
     */
    public function clearCache(string $countryCode = null): void
    {
        if ($countryCode) {
            $currency = $this->countryDetectionService->getLocalizedCurrency($countryCode);
            $cacheKey = "localized_plans_{$countryCode}_{$currency}";
            Cache::forget($cacheKey);
        } else {
            // Clear all localized pricing cache
            $countries = ['BD', 'US', 'CA', 'GB', 'AU']; // Add more as needed
            foreach ($countries as $country) {
                $currency = $this->countryDetectionService->getLocalizedCurrency($country);
                $cacheKey = "localized_plans_{$country}_{$currency}";
                Cache::forget($cacheKey);
            }
        }
        
        Log::info('Localized pricing cache cleared', ['country' => $countryCode ?? 'all']);
    }

    /**
     * Get pricing statistics by country.
     */
    public function getPricingStatistics(): array
    {
        $bdtPlans = PricingPlan::where('currency', 'BDT')->count();
        $usdPlans = PricingPlan::where('currency', 'USD')->count();
        $activePlans = PricingPlan::active()->count();
        $publicPlans = PricingPlan::public()->count();
        
        return [
            'total_plans' => PricingPlan::count(),
            'bdt_plans' => $bdtPlans,
            'usd_plans' => $usdPlans,
            'active_plans' => $activePlans,
            'public_plans' => $publicPlans,
            'localization_coverage' => [
                'bangladesh' => $bdtPlans > 0,
                'international' => $usdPlans > 0,
            ],
        ];
    }

    /**
     * Sync localized plans with base plans.
     */
    public function syncLocalizedPlans(): array
    {
        $results = [
            'synced' => 0,
            'created' => 0,
            'errors' => [],
        ];
        
        try {
            // This could be enhanced to automatically sync pricing changes
            // between base USD plans and localized BDT plans
            Log::info('Localized plans sync completed', $results);
        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            Log::error('Failed to sync localized plans', ['error' => $e->getMessage()]);
        }
        
        return $results;
    }
}
