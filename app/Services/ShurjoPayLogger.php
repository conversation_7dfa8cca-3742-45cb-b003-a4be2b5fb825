<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ShurjoPayLogger
{
    /**
     * Check if ShurjoPay logging is enabled.
     */
    public static function isLoggingEnabled(): bool
    {
        return filter_var(config('shurjopay.logging.enabled', false), FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * Check if ShurjoPay debug mode is enabled.
     */
    public static function isDebugEnabled(): bool
    {
        return filter_var(config('shurjopay.debug', false), FILTER_VALIDATE_BOOLEAN) && 
               filter_var(config('app.debug', false), FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * Log info message if logging is enabled.
     */
    public static function info(string $message, array $context = []): void
    {
        if (!self::isLoggingEnabled()) {
            return;
        }

        try {
            Log::channel('shurjopay')->info($message, $context);
        } catch (\Exception $e) {
            // Fallback to default log channel if shurjopay channel fails
            Log::info($message, array_merge($context, ['shurjopay_logging_error' => $e->getMessage()]));
        }
    }

    /**
     * Log debug message if logging and debug are enabled.
     */
    public static function debug(string $message, array $context = []): void
    {
        if (!self::isLoggingEnabled() || !self::isDebugEnabled()) {
            return;
        }

        try {
            Log::channel('shurjopay')->debug($message, $context);
        } catch (\Exception $e) {
            // Fallback to default log channel if shurjopay channel fails
            Log::debug($message, array_merge($context, ['shurjopay_logging_error' => $e->getMessage()]));
        }
    }

    /**
     * Log warning message if logging is enabled.
     */
    public static function warning(string $message, array $context = []): void
    {
        if (!self::isLoggingEnabled()) {
            return;
        }

        try {
            Log::channel('shurjopay')->warning($message, $context);
        } catch (\Exception $e) {
            // Fallback to default log channel if shurjopay channel fails
            Log::warning($message, array_merge($context, ['shurjopay_logging_error' => $e->getMessage()]));
        }
    }

    /**
     * Log error message (always logged regardless of logging setting for critical issues).
     */
    public static function error(string $message, array $context = []): void
    {
        try {
            if (self::isLoggingEnabled()) {
                Log::channel('shurjopay')->error($message, $context);
            } else {
                // Still log critical errors to default channel even if ShurjoPay logging is disabled
                Log::error($message, array_merge($context, ['source' => 'shurjopay']));
            }
        } catch (\Exception $e) {
            // Fallback to default log channel
            Log::error($message, array_merge($context, ['shurjopay_logging_error' => $e->getMessage()]));
        }
    }

    /**
     * Log a message only once per cache duration to prevent spam.
     */
    public static function logOnce(string $level, string $message, array $context = [], int $cacheDurationMinutes = 60): void
    {
        $cacheKey = 'shurjopay_log_' . md5($level . $message . serialize($context));
        
        if (Cache::has($cacheKey)) {
            return;
        }

        // Log the message
        switch ($level) {
            case 'info':
                self::info($message, $context);
                break;
            case 'debug':
                self::debug($message, $context);
                break;
            case 'warning':
                self::warning($message, $context);
                break;
            case 'error':
                self::error($message, $context);
                break;
        }

        // Cache to prevent repeated logging
        Cache::put($cacheKey, true, now()->addMinutes($cacheDurationMinutes));
    }

    /**
     * Log service initialization (only once per application lifecycle).
     */
    public static function logServiceInitialization(array $context = []): void
    {
        self::logOnce('info', 'ShurjoPay service provider initialized', $context, 1440); // 24 hours
    }

    /**
     * Log configuration validation (only once per application lifecycle).
     */
    public static function logConfigurationValidation(bool $passed, array $errors = []): void
    {
        if ($passed) {
            self::logOnce('info', 'ShurjoPay configuration validation passed', [], 1440); // 24 hours
        } else {
            self::warning('ShurjoPay configuration issues detected', [
                'errors' => $errors,
                'environment' => app()->environment(),
            ]);
        }
    }

    /**
     * Clear all cached log entries (useful for testing or configuration changes).
     */
    public static function clearLogCache(): void
    {
        try {
            // Try Redis-based cache clearing
            if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                $keys = Cache::getRedis()->keys('*shurjopay_log_*');
                if (!empty($keys)) {
                    Cache::getRedis()->del($keys);
                }
            } else {
                // For other cache stores (like array store in tests), we can't easily clear specific keys
                // So we'll just flush the entire cache in test environments
                if (app()->environment('testing')) {
                    Cache::flush();
                }
            }
        } catch (\Exception $e) {
            // Silently fail if cache clearing doesn't work
            // This is not critical functionality
        }
    }
}
