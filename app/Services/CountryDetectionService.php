<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON><PERSON>\Location\Facades\Location;
use <PERSON><PERSON><PERSON>\Location\Position;

class CountryDetectionService
{
    private IpSearchTrackingService $ipTrackingService;

    public function __construct(IpSearchTrackingService $ipTrackingService)
    {
        $this->ipTrackingService = $ipTrackingService;
    }

    /**
     * Get country information for the current request.
     */
    public function getCountryFromRequest(Request $request): array
    {
        $ip = $this->ipTrackingService->getClientIp($request);
        return $this->getCountryFromIp($ip);
    }

    /**
     * Get country information from IP address with caching.
     */
    public function getCountryFromIp(string $ip): array
    {
        // Generate cache key
        $cacheKey = "country_detection_" . hash('sha256', $ip);
        
        // Try to get from cache first
        $cached = Cache::get($cacheKey);
        if ($cached !== null) {
            return $cached;
        }

        // Detect country from IP
        $countryData = $this->detectCountryFromIp($ip);
        
        // Cache for 24 hours
        Cache::put($cacheKey, $countryData, now()->addHours(24));
        
        return $countryData;
    }

    /**
     * Detect country from IP address using location service.
     */
    private function detectCountryFromIp(string $ip): array
    {
        try {
            // Use the location service to get position
            $position = Location::get($ip);
            
            if ($position && $position->countryCode) {
                $countryData = [
                    'country_code' => strtoupper($position->countryCode),
                    'country_name' => $position->countryName,
                    'region' => $position->regionName,
                    'city' => $position->cityName,
                    'timezone' => $position->timezone,
                    'currency' => $this->getCurrencyForCountry($position->countryCode),
                    'is_bangladesh' => strtoupper($position->countryCode) === 'BD',
                    'detected_at' => now()->toISOString(),
                    'ip_hash' => substr(hash('sha256', $ip), 0, 8),
                ];

                Log::info('Country detected successfully', [
                    'country_code' => $countryData['country_code'],
                    'country_name' => $countryData['country_name'],
                    'currency' => $countryData['currency'],
                    'ip_hash' => $countryData['ip_hash'],
                ]);

                return $countryData;
            }
        } catch (\Exception $e) {
            Log::warning('Country detection failed', [
                'error' => $e->getMessage(),
                'ip_hash' => substr(hash('sha256', $ip), 0, 8),
            ]);
        }

        // Fallback to default (assume non-Bangladesh)
        return $this->getDefaultCountryData();
    }

    /**
     * Get default country data when detection fails.
     */
    private function getDefaultCountryData(): array
    {
        return [
            'country_code' => 'US',
            'country_name' => 'United States',
            'region' => null,
            'city' => null,
            'timezone' => null,
            'currency' => 'USD',
            'is_bangladesh' => false,
            'detected_at' => now()->toISOString(),
            'ip_hash' => 'fallback',
            'is_fallback' => true,
        ];
    }

    /**
     * Get currency code for a country.
     */
    private function getCurrencyForCountry(string $countryCode): string
    {
        $countryCode = strtoupper($countryCode);
        
        // Bangladesh uses BDT
        if ($countryCode === 'BD') {
            return 'BDT';
        }
        
        // For all other countries, use USD as default
        // In a production system, you might want a more comprehensive mapping
        $currencyMap = [
            'US' => 'USD',
            'CA' => 'USD', // Use USD for Canada for simplicity
            'GB' => 'USD', // Use USD for UK for simplicity
            'AU' => 'USD', // Use USD for Australia for simplicity
            'IN' => 'USD', // Use USD for India for simplicity
            // Add more mappings as needed
        ];

        return $currencyMap[$countryCode] ?? 'USD';
    }

    /**
     * Get appropriate payment gateways for a country.
     */
    public function getPaymentGatewaysForCountry(string $countryCode): array
    {
        $countryCode = strtoupper($countryCode);
        
        if ($countryCode === 'BD') {
            // Bangladesh: SurjoPay for online, offline for backup
            return [
                'primary' => 'shurjopay',
                'secondary' => ['offline'],
                'currency' => 'BDT',
                'available_gateways' => ['shurjopay', 'offline'],
            ];
        }
        
        // Other countries: Paddle and Coinbase Commerce for online, offline for backup
        return [
            'primary' => 'paddle',
            'secondary' => ['coinbase_commerce', 'offline'],
            'currency' => 'USD',
            'available_gateways' => ['paddle', 'coinbase_commerce', 'offline'],
        ];
    }

    /**
     * Check if a country should use localized pricing.
     */
    public function shouldUseLocalizedPricing(string $countryCode): bool
    {
        return strtoupper($countryCode) === 'BD';
    }

    /**
     * Get localized currency for a country.
     */
    public function getLocalizedCurrency(string $countryCode): string
    {
        return $this->getCurrencyForCountry($countryCode);
    }

    /**
     * Clear country detection cache for an IP.
     */
    public function clearCache(string $ip): void
    {
        $cacheKey = "country_detection_" . hash('sha256', $ip);
        Cache::forget($cacheKey);
    }

    /**
     * Clear all country detection cache.
     */
    public function clearAllCache(): void
    {
        // This is a simplified approach. In production, you might want to use cache tags
        Log::info('Country detection cache cleared');
    }

    /**
     * Get country detection statistics.
     */
    public function getDetectionStats(): array
    {
        // This could be enhanced to track detection success rates, etc.
        return [
            'service_status' => 'active',
            'cache_enabled' => true,
            'fallback_enabled' => true,
            'supported_countries' => ['BD' => 'Bangladesh', 'Others' => 'International'],
            'supported_currencies' => ['BDT', 'USD'],
        ];
    }

    /**
     * Test country detection with a specific IP.
     */
    public function testDetection(string $ip): array
    {
        $startTime = microtime(true);
        $result = $this->detectCountryFromIp($ip);
        $endTime = microtime(true);
        
        return [
            'result' => $result,
            'detection_time_ms' => round(($endTime - $startTime) * 1000, 2),
            'cache_used' => false, // This was a direct detection
        ];
    }
}
