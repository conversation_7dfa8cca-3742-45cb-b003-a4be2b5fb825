<?php

namespace App\Services;

use App\Models\AdConfiguration;
use App\Models\AdPerformance;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AdSenseService
{
    /**
     * Check if ads should be shown to a user.
     */
    public function shouldShowAds(?User $user): bool
    {
        // Don't show ads to admin users
        if ($user && $user->isAdmin()) {
            return false;
        }

        // Don't show ads to premium users
        if ($user && $user->isPremium()) {
            return false;
        }

        // Show ads to free users and guests
        return true;
    }

    /**
     * Get ad configuration for a specific zone and page.
     */
    public function getAdConfiguration(
        string $zone,
        string $page,
        ?User $user = null
    ): ?AdConfiguration {
        $userType = $this->getUserType($user);
        
        $cacheKey = "ad_config_{$zone}_{$page}_{$userType}";
        
        return Cache::remember($cacheKey, 300, function () use ($zone, $page, $userType) {
            return AdConfiguration::enabled()
                ->byZone($zone)
                ->byPriority()
                ->get()
                ->first(function ($config) use ($page, $userType) {
                    return $config->appliesToPage($page) && $config->appliesToUserType($userType);
                });
        });
    }

    /**
     * Get all ad configurations for a page.
     */
    public function getPageAdConfigurations(string $page, ?User $user = null): array
    {
        if (!$this->shouldShowAds($user)) {
            return [];
        }

        $userType = $this->getUserType($user);
        $cacheKey = "page_ads_{$page}_{$userType}";

        return Cache::remember($cacheKey, 300, function () use ($page, $userType) {
            $configs = [];
            $zones = ['header', 'sidebar', 'content', 'footer', 'mobile'];

            foreach ($zones as $zone) {
                $config = AdConfiguration::enabled()
                    ->byZone($zone)
                    ->byPriority()
                    ->get()
                    ->first(function ($config) use ($page, $userType) {
                        return $config->appliesToPage($page) && $config->appliesToUserType($userType);
                    });

                if ($config) {
                    $configs[$zone] = $config;
                }
            }

            return $configs;
        });
    }

    /**
     * Record an ad impression.
     */
    public function recordImpression(
        string $zone,
        string $page,
        Request $request,
        ?string $adSlotId = null
    ): void {
        try {
            $userType = $this->getUserTypeFromRequest($request);
            $deviceType = $this->getDeviceTypeFromRequest($request);
            
            $metadata = [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referrer' => $request->header('referer'),
                'timestamp' => now()->toISOString(),
            ];

            AdPerformance::recordImpression(
                $zone,
                $page,
                $userType,
                $deviceType,
                $adSlotId,
                $metadata
            );

            Log::info('Ad impression recorded', [
                'zone' => $zone,
                'page' => $page,
                'user_type' => $userType,
                'device_type' => $deviceType,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to record ad impression', [
                'zone' => $zone,
                'page' => $page,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Record an ad click.
     */
    public function recordClick(
        string $zone,
        string $page,
        Request $request,
        ?string $adSlotId = null
    ): void {
        try {
            $userType = $this->getUserTypeFromRequest($request);
            $deviceType = $this->getDeviceTypeFromRequest($request);
            
            $metadata = [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referrer' => $request->header('referer'),
                'timestamp' => now()->toISOString(),
            ];

            AdPerformance::recordClick(
                $zone,
                $page,
                $userType,
                $deviceType,
                $adSlotId,
                $metadata
            );

            Log::info('Ad click recorded', [
                'zone' => $zone,
                'page' => $page,
                'user_type' => $userType,
                'device_type' => $deviceType,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to record ad click', [
                'zone' => $zone,
                'page' => $page,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get ad performance analytics.
     */
    public function getAnalytics(
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        ?string $zone = null,
        ?string $page = null
    ): array {
        $startDate = $startDate ?? now()->subDays(30);
        $endDate = $endDate ?? now();

        $aggregated = AdPerformance::getAggregatedData($startDate, $endDate, $zone, $page);
        $trends = AdPerformance::getPerformanceTrends($startDate, $endDate);

        // Calculate days more accurately - if we're looking at "last 30 days", it should show 30
        $daysDiff = $startDate->startOfDay()->diffInDays($endDate->startOfDay());
        $totalDays = $daysDiff === 0 ? 1 : $daysDiff;

        return [
            'summary' => $aggregated,
            'trends' => $trends,
            'period' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'days' => $totalDays,
            ],
        ];
    }

    /**
     * Get zone-wise performance comparison.
     */
    public function getZoneComparison(?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $startDate = $startDate ?? now()->subDays(30);
        $endDate = $endDate ?? now();

        $zones = ['header', 'sidebar', 'content', 'footer', 'mobile'];
        $comparison = [];

        foreach ($zones as $zone) {
            $comparison[$zone] = AdPerformance::getAggregatedData($startDate, $endDate, $zone);
        }

        return $comparison;
    }

    /**
     * Clear ad configuration cache.
     */
    public function clearCache(): void
    {
        Cache::forget('ad_config_*');
        Cache::forget('page_ads_*');
        Cache::forget('ad_configurations');
        Cache::forget('ad_settings');
        Cache::forget('adsense_config');
        Cache::forget('adsense_status');
        Log::info('Ad configuration cache cleared');
    }

    /**
     * Get user type from user object.
     */
    private function getUserType(?User $user): string
    {
        if (!$user) {
            return 'guest';
        }

        if ($user->isAdmin()) {
            return 'admin';
        }

        if ($user->isPremium()) {
            return 'premium';
        }

        return 'free';
    }

    /**
     * Get user type from request.
     */
    private function getUserTypeFromRequest(Request $request): string
    {
        $user = $request->user();
        return $this->getUserType($user);
    }

    /**
     * Get device type from request.
     */
    private function getDeviceTypeFromRequest(Request $request): string
    {
        $userAgent = $request->userAgent();

        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }

        return 'desktop';
    }

    /**
     * Initialize default ad configurations.
     */
    public function initializeDefaults(): void
    {
        AdConfiguration::createDefaults();
        $this->clearCache();
        Log::info('Default ad configurations initialized');
    }

    /**
     * Update ad configuration.
     */
    public function updateConfiguration(string $zone, array $data): AdConfiguration
    {
        $config = AdConfiguration::firstOrCreate(['zone' => $zone]);
        $config->update($data);
        $this->clearCache();
        
        Log::info('Ad configuration updated', ['zone' => $zone, 'data' => $data]);
        
        return $config;
    }

    /**
     * Enable/disable ads for a specific zone.
     */
    public function toggleZone(string $zone, bool $enabled): bool
    {
        $config = AdConfiguration::firstOrCreate(['zone' => $zone]);
        $config->enabled = $enabled;
        $config->save();

        $this->clearCache();

        Log::info('Ad zone toggled', ['zone' => $zone, 'enabled' => $enabled]);

        return true;
    }

    /**
     * Get current AdSense configuration.
     */
    public function getAdSenseConfiguration(): array
    {
        return [
            'enabled' => config('adsense.enabled', false),
            'client_id' => config('adsense.client_id', ''),
            'auto_ads' => config('adsense.auto_ads', false),
            'debug' => config('adsense.debug', false),
            'zones' => [
                'header_enabled' => config('adsense.zones.header', true),
                'sidebar_enabled' => config('adsense.zones.sidebar', true),
                'content_enabled' => config('adsense.zones.content', true),
                'footer_enabled' => config('adsense.zones.footer', true),
                'mobile_enabled' => config('adsense.zones.mobile', true),
            ],
            'frequency' => [
                'max_ads_per_page' => config('adsense.frequency.max_ads_per_page', 4),
                'delay_seconds' => config('adsense.frequency.delay_seconds', 3),
                'grace_period_minutes' => config('adsense.frequency.grace_period_minutes', 0),
            ],
        ];
    }

    /**
     * Validate AdSense configuration.
     */
    public function validateAdSenseConfiguration(array $config): array
    {
        $errors = [];

        // Validate client ID format
        if (!empty($config['client_id'])) {
            if (!preg_match('/^ca-pub-\d{16}$/', $config['client_id'])) {
                $errors['client_id'] = 'Client ID must be in format: ca-pub-xxxxxxxxxxxxxxxx';
            }
        } elseif ($config['enabled'] ?? false) {
            $errors['client_id'] = 'Client ID is required when AdSense is enabled';
        }

        // Validate frequency settings
        if (isset($config['frequency'])) {
            $frequency = $config['frequency'];

            if (isset($frequency['max_ads_per_page'])) {
                if ($frequency['max_ads_per_page'] < 1 || $frequency['max_ads_per_page'] > 10) {
                    $errors['frequency.max_ads_per_page'] = 'Max ads per page must be between 1 and 10';
                }
            }

            if (isset($frequency['delay_seconds'])) {
                if ($frequency['delay_seconds'] < 0 || $frequency['delay_seconds'] > 30) {
                    $errors['frequency.delay_seconds'] = 'Delay seconds must be between 0 and 30';
                }
            }

            if (isset($frequency['grace_period_minutes'])) {
                if ($frequency['grace_period_minutes'] < 0 || $frequency['grace_period_minutes'] > 60) {
                    $errors['frequency.grace_period_minutes'] = 'Grace period must be between 0 and 60 minutes';
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Get AdSense configuration status.
     */
    public function getAdSenseStatus(): array
    {
        $config = $this->getAdSenseConfiguration();
        $validation = $this->validateAdSenseConfiguration($config);

        return [
            'configured' => !empty($config['client_id']),
            'enabled' => $config['enabled'],
            'client_id_valid' => empty($validation['errors']['client_id'] ?? null),
            'auto_ads_enabled' => $config['auto_ads'],
            'debug_mode' => $config['debug'],
            'zones_enabled' => array_filter($config['zones']),
            'status' => $validation['valid'] ? 'healthy' : 'error',
            'last_updated' => now()->toISOString(),
        ];
    }

    /**
     * Get AdSense configuration formatted for frontend consumption.
     */
    public function getFrontendConfig(): array
    {
        $config = $this->getAdSenseConfiguration();

        return [
            'enabled' => $config['enabled'],
            'zones' => [
                'header' => $config['zones']['header_enabled'],
                'sidebar' => $config['zones']['sidebar_enabled'],
                'content' => $config['zones']['content_enabled'],
                'footer' => $config['zones']['footer_enabled'],
                'mobile' => $config['zones']['mobile_enabled'],
            ],
            'frequency' => [
                'maxAdsPerPage' => $config['frequency']['max_ads_per_page'],
                'delaySeconds' => $config['frequency']['delay_seconds'],
                'gracePeriodMinutes' => $config['frequency']['grace_period_minutes'],
            ],
        ];
    }

    /**
     * Test AdSense configuration.
     */
    public function testAdSenseConfiguration(array $config): array
    {
        $validation = $this->validateAdSenseConfiguration($config);

        if (!$validation['valid']) {
            return [
                'success' => false,
                'message' => 'Configuration validation failed',
                'errors' => $validation['errors'],
            ];
        }

        // Test client ID format and basic connectivity
        $clientId = $config['client_id'] ?? '';
        if (empty($clientId)) {
            return [
                'success' => false,
                'message' => 'Client ID is required for testing',
            ];
        }

        // Basic format validation
        if (!preg_match('/^ca-pub-\d{16}$/', $clientId)) {
            return [
                'success' => false,
                'message' => 'Invalid client ID format',
            ];
        }

        return [
            'success' => true,
            'message' => 'AdSense configuration is valid',
            'client_id' => $clientId,
            'zones_configured' => count(array_filter($config['zones'] ?? [])),
        ];
    }

}
