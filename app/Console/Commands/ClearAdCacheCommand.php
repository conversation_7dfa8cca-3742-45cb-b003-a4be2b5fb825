<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearAdCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ads:clear-cache {--all : Clear all cache, not just ad-related}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all ad-related cache entries';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Clearing ad-related cache entries...');

        // Ad-related cache keys
        $adCacheKeys = [
            'adsense_config',
            'adsense_status', 
            'adsense_frontend_config',
        ];

        $clearedCount = 0;

        foreach ($adCacheKeys as $key) {
            if (Cache::forget($key)) {
                $this->line("✓ Cleared cache key: {$key}");
                $clearedCount++;
            } else {
                $this->line("- Cache key not found: {$key}");
            }
        }

        if ($this->option('all')) {
            $this->info('Clearing all application cache...');
            $this->call('cache:clear');
            $this->call('config:clear');
            $this->call('route:clear');
            $this->call('view:clear');
        }

        $this->info("Successfully cleared {$clearedCount} ad-related cache entries.");
        
        return self::SUCCESS;
    }
}
