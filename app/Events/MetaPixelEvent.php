<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MetaPixelEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $eventName;
    public array $eventData;
    public ?User $user;
    public ?string $eventId;
    public bool $sendToConversionsApi;
    public \DateTime $timestamp;
    public string $actionSource;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $eventName,
        array $eventData = [],
        ?User $user = null,
        ?string $eventId = null,
        bool $sendToConversionsApi = true,
        string $actionSource = 'website'
    ) {
        $this->eventName = $eventName;
        $this->eventData = $eventData;
        $this->user = $user;
        $this->eventId = $eventId ?: $this->generateEventId();
        $this->sendToConversionsApi = $sendToConversionsApi;
        $this->timestamp = new \DateTime();
        $this->actionSource = $actionSource;
    }

    /**
     * Generate a unique event ID.
     */
    private function generateEventId(): string
    {
        return uniqid('meta_pixel_', true) . '_' . time();
    }
}
