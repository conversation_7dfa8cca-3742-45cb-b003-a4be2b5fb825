# Mobile App Quick Start Guide

## Prerequisites

Before starting development, ensure you have the following installed:

### Required Software
- **Node.js** (v18 or higher)
- **npm** or **yarn**
- **Expo CLI** (`npm install -g @expo/cli`)
- **Git**
- **VS Code** (recommended) with React Native extensions

### Development Environment
- **iOS Development:** Xcode (macOS only)
- **Android Development:** Android Studio
- **Physical Devices:** iOS and Android devices for testing

### Accounts Needed
- **Expo Account** (free)
- **Apple Developer Account** (for iOS deployment)
- **Google Play Console Account** (for Android deployment)

## Project Setup

### 1. Initialize Project
```bash
# Create new Expo project
npx create-expo-app mobile-parts-app --template blank-typescript

# Navigate to project directory
cd mobile-parts-app

# Install additional dependencies
npm install @react-navigation/native @react-navigation/bottom-tabs @react-navigation/stack
npm install @tanstack/react-query react-native-async-storage
npm install react-native-keychain expo-secure-store
npm install expo-notifications expo-camera expo-barcode-scanner
npm install react-native-google-mobile-ads
```

### 2. Project Structure Setup
```bash
# Create folder structure
mkdir -p src/{components,screens,services,hooks,utils,types,constants,assets}
mkdir -p src/components/{ui,forms,cards,modals}
mkdir -p src/screens/{auth,search,parts,user,subscription}
mkdir -p src/services/{api,auth,storage,notifications}
mkdir -p src/assets/{images,icons,fonts}

# Create navigation folder
mkdir -p src/navigation
```

### 3. Configuration Files

#### TypeScript Configuration (tsconfig.json)
```json
{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/screens/*": ["src/screens/*"],
      "@/services/*": ["src/services/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"]
    }
  }
}
```

#### Metro Configuration (metro.config.js)
```javascript
const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

config.resolver.alias = {
  '@': './src',
};

module.exports = config;
```

### 4. Environment Configuration

#### Create .env file
```bash
# API Configuration
API_BASE_URL=https://api.mobileparts.com/api/mobile
API_TIMEOUT=10000

# AdMob Configuration
ADMOB_APP_ID_IOS=ca-app-pub-xxxxx~xxxxx
ADMOB_APP_ID_ANDROID=ca-app-pub-xxxxx~xxxxx
ADMOB_BANNER_ID_IOS=ca-app-pub-xxxxx/xxxxx
ADMOB_BANNER_ID_ANDROID=ca-app-pub-xxxxx/xxxxx

# App Configuration
APP_NAME=Mobile Parts Database
APP_VERSION=1.0.0
```

## Core Implementation

### 1. API Client Setup

#### Create API Client (src/services/api/client.ts)
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = process.env.API_BASE_URL || 'https://api.mobileparts.com/api/mobile';

class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.timeout = 10000;
  }

  private async getAuthToken(): Promise<string | null> {
    return await AsyncStorage.getItem('auth_token');
  }

  private async getHeaders(): Promise<Record<string, string>> {
    const token = await this.getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = await this.getHeaders();

    const config: RequestInit = {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    };

    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }

    return data;
  }

  async get(endpoint: string): Promise<any> {
    return this.request(endpoint, { method: 'GET' });
  }

  async post(endpoint: string, body: any): Promise<any> {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });
  }

  async put(endpoint: string, body: any): Promise<any> {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(body),
    });
  }

  async delete(endpoint: string): Promise<any> {
    return this.request(endpoint, { method: 'DELETE' });
  }
}

export const apiClient = new ApiClient();
```

### 2. Authentication Service

#### Create Auth Service (src/services/auth/authService.ts)
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { apiClient } from '../api/client';

export interface LoginCredentials {
  email: string;
  password: string;
  deviceName: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  subscription_plan: string;
  search_count: number;
}

class AuthService {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USER_KEY = 'user_data';

  async login(credentials: LoginCredentials): Promise<{ user: User; token: string }> {
    const response = await apiClient.post('/auth/login', credentials);
    
    if (response.success) {
      await this.storeToken(response.data.token);
      await this.storeUser(response.data.user);
      return response.data;
    }
    
    throw new Error(response.message || 'Login failed');
  }

  async register(userData: any): Promise<{ user: User; token: string }> {
    const response = await apiClient.post('/auth/register', userData);
    
    if (response.success) {
      await this.storeToken(response.data.token);
      await this.storeUser(response.data.user);
      return response.data;
    }
    
    throw new Error(response.message || 'Registration failed');
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout', {});
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      await this.clearAuthData();
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await apiClient.get('/auth/user');
      if (response.success) {
        await this.storeUser(response.data);
        return response.data;
      }
    } catch (error) {
      console.error('Failed to get current user:', error);
      await this.clearAuthData();
    }
    return null;
  }

  async getStoredToken(): Promise<string | null> {
    return await SecureStore.getItemAsync(this.TOKEN_KEY);
  }

  async getStoredUser(): Promise<User | null> {
    const userData = await AsyncStorage.getItem(this.USER_KEY);
    return userData ? JSON.parse(userData) : null;
  }

  private async storeToken(token: string): Promise<void> {
    await SecureStore.setItemAsync(this.TOKEN_KEY, token);
  }

  private async storeUser(user: User): Promise<void> {
    await AsyncStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  private async clearAuthData(): Promise<void> {
    await SecureStore.deleteItemAsync(this.TOKEN_KEY);
    await AsyncStorage.removeItem(this.USER_KEY);
  }
}

export const authService = new AuthService();
```

### 3. React Query Setup

#### Create Query Client (src/services/queryClient.ts)
```typescript
import { QueryClient } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createAsyncStoragePersister } from '@tanstack/query-async-storage-persister';
import { persistQueryClient } from '@tanstack/react-query-persist-client-core';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});

const asyncStoragePersister = createAsyncStoragePersister({
  storage: AsyncStorage,
});

persistQueryClient({
  queryClient,
  persister: asyncStoragePersister,
});
```

### 4. Authentication Hook

#### Create useAuth Hook (src/hooks/useAuth.ts)
```typescript
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authService, LoginCredentials, User } from '../services/auth/authService';

export const useAuth = () => {
  const queryClient = useQueryClient();

  const { data: user, isLoading } = useQuery({
    queryKey: ['auth', 'user'],
    queryFn: async () => {
      const storedUser = await authService.getStoredUser();
      if (storedUser) {
        return storedUser;
      }
      return await authService.getCurrentUser();
    },
    enabled: true,
  });

  const loginMutation = useMutation({
    mutationFn: (credentials: LoginCredentials) => authService.login(credentials),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    },
  });

  const logoutMutation = useMutation({
    mutationFn: () => authService.logout(),
    onSuccess: () => {
      queryClient.clear();
    },
  });

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    login: loginMutation.mutateAsync,
    logout: logoutMutation.mutateAsync,
    isLoggingIn: loginMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
  };
};
```

## Development Workflow

### 1. Start Development Server
```bash
# Start Expo development server
npx expo start

# For iOS simulator
npx expo start --ios

# For Android emulator
npx expo start --android
```

### 2. Testing on Physical Devices
1. Install Expo Go app on your device
2. Scan QR code from terminal
3. App will load on your device

### 3. Building for Production
```bash
# Install EAS CLI
npm install -g eas-cli

# Login to Expo
eas login

# Configure build
eas build:configure

# Build for both platforms
eas build --platform all
```

## Next Steps

1. **Implement Authentication Screens:** Create login, register, and password reset screens
2. **Add Navigation:** Set up React Navigation with tab and stack navigators
3. **Create Search Interface:** Implement search functionality with filters
4. **Add Part Details:** Create detailed part information screens
5. **Implement Favorites:** Add ability to save favorite parts and models
6. **Add Subscription Management:** Integrate in-app purchases
7. **Implement Push Notifications:** Set up notification handling
8. **Add Offline Support:** Implement caching and offline functionality

## Useful Commands

```bash
# Install new dependency
npx expo install package-name

# Clear cache
npx expo start --clear

# Check for issues
npx expo doctor

# Update Expo SDK
npx expo install --fix

# Generate app icons
npx expo prebuild

# Run tests
npm test

# Type checking
npx tsc --noEmit
```

## Resources

- [Expo Documentation](https://docs.expo.dev/)
- [React Navigation](https://reactnavigation.org/)
- [React Query Documentation](https://tanstack.com/query/latest)
- [React Native Documentation](https://reactnative.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

This quick start guide provides the foundation for building the mobile parts database app. Follow the comprehensive documentation for detailed implementation of each feature.
