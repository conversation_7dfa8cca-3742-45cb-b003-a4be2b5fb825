# Mobile App API Documentation

## Overview

This document provides detailed API specifications for the mobile application backend integration. The mobile app will communicate with the Laravel backend through dedicated API endpoints designed specifically for mobile clients.

## Authentication

### Token-Based Authentication
The mobile app uses Laravel Sanctum for API authentication with token-based authentication.

**Base URL:** `https://api.mobileparts.com/api/mobile`

**Authentication Header:**
```
Authorization: Bearer {token}
```

## API Endpoints

### Authentication Endpoints

#### POST /auth/login
Authenticate user and return access token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "device_name": "iPhone 15 Pro"
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "subscription_plan": "free",
      "search_count": 5,
      "daily_reset": "2024-01-15"
    },
    "token": "1|abc123def456..."
  }
}
```

**Response (Error - 401):**
```json
{
  "success": false,
  "message": "Invalid credentials"
}
```

#### POST /auth/register
Register new user account.

**Request:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "device_name": "iPhone 15 Pro"
}
```

**Response (Success - 201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "subscription_plan": "free",
      "search_count": 0,
      "daily_reset": "2024-01-15"
    },
    "token": "1|abc123def456..."
  }
}
```

#### POST /auth/logout
Logout user and revoke current token.

**Headers:** `Authorization: Bearer {token}`

**Response (Success - 200):**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

#### GET /auth/user
Get current authenticated user information.

**Headers:** `Authorization: Bearer {token}`

**Response (Success - 200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "subscription_plan": "premium",
    "search_count": 15,
    "daily_reset": "2024-01-15",
    "subscription_ends_at": "2024-02-15T00:00:00Z"
  }
}
```

### Search Endpoints

#### GET /search/parts
Search for parts with filters and pagination.

**Headers:** `Authorization: Bearer {token}`

**Query Parameters:**
- `q` (required): Search query string
- `type` (optional): Search type (`all`, `category`, `model`, `part_name`)
- `category_id` (optional): Filter by category ID
- `brand_id` (optional): Filter by brand ID
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 20, max: 50)

**Example Request:**
```
GET /search/parts?q=LCD&type=part_name&category_id=1&page=1&per_page=20
```

**Response (Success - 200):**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "name": "LCD Screen Assembly",
        "slug": "lcd-screen-assembly",
        "part_number": "LCD001",
        "manufacturer": "Samsung",
        "description": "High-quality LCD screen replacement",
        "category": {
          "id": 1,
          "name": "Display",
          "slug": "display"
        },
        "images": [
          "https://api.mobileparts.com/storage/parts/lcd001-1.jpg",
          "https://api.mobileparts.com/storage/parts/lcd001-2.jpg"
        ],
        "compatible_models": [
          {
            "id": 1,
            "name": "iPhone 15",
            "brand": "Apple"
          }
        ],
        "is_favorite": false
      }
    ],
    "current_page": 1,
    "last_page": 5,
    "per_page": 20,
    "total": 95,
    "from": 1,
    "to": 20
  },
  "remaining_searches": 15
}
```

**Response (Rate Limited - 429):**
```json
{
  "success": false,
  "message": "Daily search limit exceeded",
  "remaining_searches": 0
}
```

### Parts Endpoints

#### GET /parts/{id}
Get detailed information about a specific part.

**Headers:** `Authorization: Bearer {token}`

**Response (Success - 200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "LCD Screen Assembly",
    "slug": "lcd-screen-assembly",
    "part_number": "LCD001",
    "manufacturer": "Samsung",
    "description": "High-quality LCD screen replacement for iPhone 15",
    "specifications": {
      "size": "6.1 inches",
      "resolution": "2556 x 1179",
      "technology": "Super Retina XDR OLED",
      "color_support": "True Tone, Wide color (P3)"
    },
    "category": {
      "id": 1,
      "name": "Display",
      "slug": "display"
    },
    "images": [
      "https://api.mobileparts.com/storage/parts/lcd001-1.jpg",
      "https://api.mobileparts.com/storage/parts/lcd001-2.jpg",
      "https://api.mobileparts.com/storage/parts/lcd001-3.jpg"
    ],
    "compatible_models": [
      {
        "id": 1,
        "name": "iPhone 15",
        "brand": "Apple",
        "model_number": "A2846",
        "compatibility_notes": "Direct replacement, no modifications needed"
      },
      {
        "id": 2,
        "name": "iPhone 15 Plus",
        "brand": "Apple",
        "model_number": "A2847",
        "compatibility_notes": "Compatible with minor adjustments"
      }
    ],
    "related_parts": [
      {
        "id": 2,
        "name": "Touch Screen Digitizer",
        "part_number": "DIG001",
        "category": "Display"
      }
    ],
    "is_favorite": true
  }
}
```

### Brands & Categories Endpoints

#### GET /brands
Get list of all active brands.

**Headers:** `Authorization: Bearer {token}`

**Response (Success - 200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Apple",
      "slug": "apple",
      "logo_url": "https://api.mobileparts.com/storage/brands/apple-logo.png",
      "country": "USA",
      "models_count": 25
    },
    {
      "id": 2,
      "name": "Samsung",
      "slug": "samsung",
      "logo_url": "https://api.mobileparts.com/storage/brands/samsung-logo.png",
      "country": "South Korea",
      "models_count": 45
    }
  ]
}
```

#### GET /brands/{id}/models
Get models for a specific brand.

**Headers:** `Authorization: Bearer {token}`

**Response (Success - 200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "iPhone 15",
      "slug": "iphone-15",
      "model_number": "A2846",
      "release_year": 2023,
      "specifications": {
        "display": "6.1-inch Super Retina XDR",
        "processor": "A17 Pro",
        "storage": ["128GB", "256GB", "512GB", "1TB"]
      },
      "images": [
        "https://api.mobileparts.com/storage/models/iphone15-1.jpg"
      ],
      "parts_count": 15
    }
  ]
}
```

#### GET /categories
Get list of all active categories.

**Headers:** `Authorization: Bearer {token}`

**Response (Success - 200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Display",
      "slug": "display",
      "description": "LCD screens, OLED displays, touch digitizers",
      "parts_count": 150,
      "subcategories": [
        {
          "id": 11,
          "name": "LCD Screens",
          "slug": "lcd-screens",
          "parts_count": 75
        }
      ]
    }
  ]
}
```

### User Management Endpoints

#### GET /user/profile
Get user profile information.

**Headers:** `Authorization: Bearer {token}`

**Response (Success - 200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": "123 Main St",
    "city": "New York",
    "country": "USA",
    "subscription_plan": "premium",
    "subscription_status": "active",
    "subscription_ends_at": "2024-02-15T00:00:00Z",
    "search_count": 15,
    "daily_reset": "2024-01-15",
    "created_at": "2023-12-01T00:00:00Z"
  }
}
```

#### GET /user/favorites
Get user's favorite parts and models.

**Headers:** `Authorization: Bearer {token}`

**Query Parameters:**
- `type` (optional): Filter by type (`parts`, `models`)
- `page` (optional): Page number
- `per_page` (optional): Items per page

**Response (Success - 200):**
```json
{
  "success": true,
  "data": {
    "parts": [
      {
        "id": 1,
        "name": "LCD Screen Assembly",
        "part_number": "LCD001",
        "category": "Display",
        "added_at": "2024-01-10T00:00:00Z"
      }
    ],
    "models": [
      {
        "id": 1,
        "name": "iPhone 15",
        "brand": "Apple",
        "added_at": "2024-01-12T00:00:00Z"
      }
    ]
  }
}
```

#### POST /user/favorites/{type}/{id}
Add item to favorites.

**Headers:** `Authorization: Bearer {token}`

**Parameters:**
- `type`: `parts` or `models`
- `id`: Item ID

**Response (Success - 200):**
```json
{
  "success": true,
  "message": "Added to favorites"
}
```

#### DELETE /user/favorites/{type}/{id}
Remove item from favorites.

**Headers:** `Authorization: Bearer {token}`

**Response (Success - 200):**
```json
{
  "success": true,
  "message": "Removed from favorites"
}
```
