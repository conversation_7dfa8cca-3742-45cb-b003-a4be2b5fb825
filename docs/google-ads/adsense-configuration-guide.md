# Google AdSense Configuration Guide

This guide explains how to configure and manage Google AdSense settings through the admin interface.

## Overview

The Google AdSense configuration tab provides a comprehensive interface for managing your AdSense settings, including:

- Global AdSense settings (Client ID, Auto Ads, Debug mode)
- Zone-specific configuration (Header, Sidebar, Content, Footer, Mobile)
- Frequency settings (Max ads per page, delay, grace period)
- Real-time status monitoring and validation
- Configuration testing functionality

## Accessing AdSense Configuration

1. Navigate to **Admin Dashboard** → **System Configuration** → **Ad Management**
2. Click on the **AdSense Configuration** tab
3. The interface displays current status and configuration options

## Configuration Sections

### 1. AdSense Status

The status section displays:
- **Configured**: Whether AdSense is properly set up
- **Enabled**: Current enabled/disabled state
- **Client ID Valid**: Validation status of your AdSense Client ID
- **Auto Ads**: Whether Auto Ads feature is enabled

Status indicators:
- ✅ Green checkmark: Configuration is healthy
- ⚠️ Red alert: Configuration has issues requiring attention

### 2. Global Settings

#### Enable AdSense
Toggle to enable or disable Google AdSense for your entire site.

#### AdSense Client ID
Your Google AdSense publisher ID in the format: `ca-pub-xxxxxxxxxxxxxxxx`

**How to find your Client ID:**
1. Log in to your Google AdSense account
2. Go to **Account** → **Account Information**
3. Copy your Publisher ID

#### Auto Ads
Enable Google's automatic ad placement feature. When enabled, Google will automatically place ads on your site based on their algorithms.

#### Debug Mode
Enable debug mode for development and testing purposes. This should be disabled in production.

### 3. Zone Configuration

Configure which zones are enabled for displaying ads:

- **Header Zone**: Display ads in the header area
- **Sidebar Zone**: Display ads in the sidebar area  
- **Content Zone**: Display ads within content areas
- **Footer Zone**: Display ads in the footer area
- **Mobile Zone**: Display ads on mobile devices

### 4. Frequency Settings

Control ad frequency and timing:

- **Max Ads Per Page**: Maximum number of ads per page (1-10)
- **Delay (seconds)**: Delay before showing ads (0-30 seconds)
- **Grace Period (minutes)**: Grace period for new users (0-60 minutes)

## Environment Variables

The following environment variables are managed through the interface:

```env
# Global AdSense Settings
GOOGLE_ADSENSE_ENABLED=false
GOOGLE_ADSENSE_CLIENT_ID=ca-pub-0000000000000000
GOOGLE_ADSENSE_AUTO_ADS=false
GOOGLE_ADSENSE_DEBUG=false

# Zone Configuration
ADSENSE_HEADER_ENABLED=true
ADSENSE_SIDEBAR_ENABLED=true
ADSENSE_CONTENT_ENABLED=true
ADSENSE_FOOTER_ENABLED=true
ADSENSE_MOBILE_ENABLED=true

# Frequency Settings
ADSENSE_MAX_ADS_PER_PAGE=4
ADSENSE_DELAY_SECONDS=3
ADSENSE_GRACE_PERIOD_MINUTES=0
```

## Testing Configuration

Use the **Test Configuration** button to validate your settings:

1. Click **Test Configuration**
2. The system will validate:
   - Client ID format
   - Configuration completeness
   - Zone settings
3. Results are displayed with success/error messages

## Best Practices

### Client ID Validation
- Ensure your Client ID follows the exact format: `ca-pub-xxxxxxxxxxxxxxxx`
- The ID must be 16 digits after the `ca-pub-` prefix
- Copy the ID directly from your AdSense account to avoid typos

### Zone Configuration
- Start with fewer zones enabled and gradually increase based on performance
- Monitor user experience to ensure ads don't negatively impact site usability
- Consider mobile-specific settings for better mobile user experience

### Frequency Settings
- Start with conservative settings (max 3-4 ads per page)
- Use grace periods for new users to improve first-time experience
- Monitor performance metrics to optimize settings

### Auto Ads
- Test Auto Ads thoroughly before enabling in production
- Monitor page load times and user experience
- Consider disabling Auto Ads if you prefer manual ad placement control

## Troubleshooting

### Common Issues

**Invalid Client ID Error**
- Verify the Client ID format: `ca-pub-xxxxxxxxxxxxxxxx`
- Ensure no extra spaces or characters
- Copy directly from AdSense account

**Ads Not Displaying**
- Check if AdSense is enabled globally
- Verify zone-specific settings are enabled
- Ensure user type is eligible for ads (not admin/premium)
- Check browser console for JavaScript errors

**Configuration Not Saving**
- Verify admin permissions
- Check server logs for validation errors
- Ensure all required fields are filled

### Status Indicators

- **Healthy**: All configurations are valid and working
- **Error**: Configuration issues need attention

## API Endpoints

The following API endpoints are available for programmatic access:

- `GET /admin/ads/adsense-config` - Get current configuration
- `POST /admin/ads/adsense-config` - Update configuration
- `POST /admin/ads/adsense-config/test` - Test configuration

## Security Considerations

- AdSense configuration requires admin privileges
- All configuration changes are logged
- Rate limiting is applied to prevent abuse
- 2FA verification may be required for sensitive operations

## Related Documentation

- [Ad Management Implementation Guide](./IMPLEMENTATION_GUIDE.md)
- [Page-wise Ad Placement Guide](./page-wise-ad-placement-guide.md)
- [Ads Implementation Strategy](./ads-implementation-strategy.md)
