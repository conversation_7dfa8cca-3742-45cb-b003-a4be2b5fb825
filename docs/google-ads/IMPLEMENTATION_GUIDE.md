# Google AdSense Implementation Guide

## Overview

This guide provides detailed instructions for setting up and managing the Google AdSense integration in the Mobile Parts Database application. The implementation shows ads only to free users while providing an ad-free experience for premium subscribers and administrators.

## Quick Start

### 1. Environment Configuration

Add the following variables to your `.env` file:

```bash
# Google AdSense Configuration
GOOGLE_ADSENSE_ENABLED=true
GOOGLE_ADSENSE_CLIENT_ID=ca-pub-YOUR_PUBLISHER_ID
GOOGLE_ADSENSE_AUTO_ADS=false
GOOGLE_ADSENSE_DEBUG=false

# AdSense Zone Configuration
ADSENSE_HEADER_ENABLED=true
ADSENSE_SIDEBAR_ENABLED=true
ADSENSE_CONTENT_ENABLED=true
ADSENSE_FOOTER_ENABLED=true
ADSENSE_MOBILE_ENABLED=true

# AdSense Frequency Settings
ADSENSE_MAX_ADS_PER_PAGE=4
ADSENSE_DELAY_SECONDS=3
ADSENSE_GRACE_PERIOD_MINUTES=0

# Vite Environment Variable (for frontend)
VITE_ADSENSE_CLIENT_ID="${GOOGLE_ADSENSE_CLIENT_ID}"
```

### 2. Database Setup

Run the migrations to create the necessary tables:

```bash
php artisan migrate
```

### 3. Initialize Default Configurations

```bash
php artisan tinker
```

```php
use App\Services\AdSenseService;
$adSenseService = app(AdSenseService::class);
$adSenseService->initializeDefaults();
```

Or use the admin interface at `/admin/ads` and click "Initialize Defaults".

## Architecture

### Backend Components

#### Models
- **AdConfiguration**: Manages ad zone configurations
- **AdPerformance**: Tracks ad performance metrics

#### Services
- **AdSenseService**: Core service for ad management and analytics

#### Controllers
- **AdTrackingController**: API endpoints for ad tracking
- **AdManagementController**: Admin interface for ad management

### Frontend Components

#### Core Components
- **AdContainer**: Base component for rendering individual ads
- **AdScript**: Loads Google AdSense scripts
- **AdWrapper**: Handles user-based ad display logic

#### Layout Components
- **AdLayoutWrapper**: Wraps pages with ad zones
- **HeaderAdZone**, **SidebarAdZone**, etc.: Predefined ad placements

#### Hooks
- **useAds**: Determines if ads should be shown to current user
- **useAdTracking**: Tracks ad impressions and clicks

## Usage

### Basic Ad Placement

```tsx
import { HeaderAdZone, ContentAdSeparator, FooterAdZone } from '@/components/ads';

function MyPage() {
    return (
        <div>
            <HeaderAdZone page="/my-page" />
            
            <main>
                {/* Your content */}
                <ContentAdSeparator page="/my-page" />
                {/* More content */}
            </main>
            
            <FooterAdZone page="/my-page" />
        </div>
    );
}
```

### Advanced Layout with Responsive Ads

```tsx
import { AdLayoutWrapper, ResponsiveAdGrid } from '@/components/ads';

function MyPage() {
    return (
        <AdLayoutWrapper page="/my-page">
            <ResponsiveAdGrid page="/my-page" showSidebarAd={true}>
                {/* Your main content */}
            </ResponsiveAdGrid>
        </AdLayoutWrapper>
    );
}
```

### Conditional Ad Display

```tsx
import { ConditionalAd } from '@/components/ads';

function MyComponent() {
    return (
        <div>
            <ConditionalAd 
                zone="content" 
                page="/my-page"
                showPlaceholder={true}
                placeholderMessage="Upgrade to Premium for ad-free experience"
            />
        </div>
    );
}
```

## Admin Management

### Accessing the Admin Interface

Navigate to `/admin/ads` to access the ad management dashboard.

### Features Available

1. **Configuration Management**
   - Create, edit, and delete ad configurations
   - Enable/disable ad zones
   - Set targeting rules and frequency limits

2. **Performance Analytics**
   - View impressions, clicks, and revenue
   - Zone-wise performance comparison
   - Export performance data

3. **Cache Management**
   - Clear ad configuration cache
   - Initialize default configurations

### API Endpoints

#### Public Endpoints

```bash
# Track ad impression
POST /api/ads/track-impression
{
    "zone": "header",
    "page": "/home",
    "ad_slot": "1234567890"
}

# Track ad click
POST /api/ads/track-click
{
    "zone": "header", 
    "page": "/home",
    "ad_slot": "1234567890"
}

# Check if ads should be shown
GET /api/ads/should-show

# Get ad configuration
GET /api/ads/configuration?zone=header&page=/home

# Get all page configurations
GET /api/ads/page-configurations?page=/home
```

## User Targeting Logic

### Who Sees Ads
- ✅ **Free Users**: Users without premium subscription
- ✅ **Guest Users**: Non-authenticated visitors

### Who Doesn't See Ads
- ❌ **Premium Users**: Active premium subscribers
- ❌ **Admin Users**: Users with admin role

### Implementation

The targeting logic is implemented in the `useAds` hook:

```tsx
const { shouldShowAds, userType } = useAds();

if (shouldShowAds) {
    // Show ads
} else {
    // Show placeholder or nothing
}
```

## Performance Tracking

### Metrics Collected
- **Impressions**: Number of times ads are displayed
- **Clicks**: Number of ad clicks
- **CTR**: Click-through rate (clicks/impressions * 100)
- **Revenue**: Ad revenue (when available from AdSense)
- **CPM**: Cost per mille (revenue/impressions * 1000)

### Data Aggregation
- Daily aggregation by zone, page, user type, and device type
- Automatic CTR and CPM calculation
- Historical trend analysis

## Testing

### Running Tests

```bash
# Backend tests
php artisan test --filter=AdSense

# Frontend tests
npm test -- --testPathPattern=ads
```

### Test Coverage
- ✅ AdSense service functionality
- ✅ API endpoint validation
- ✅ User targeting logic
- ✅ React hook behavior
- ✅ Performance tracking

## Troubleshooting

### Common Issues

1. **Ads not showing**
   - Check if user is premium/admin
   - Verify environment variables
   - Check ad configuration in admin panel

2. **AdSense script not loading**
   - Verify `VITE_ADSENSE_CLIENT_ID` is set
   - Check browser console for errors
   - Ensure ad blocker is disabled for testing

3. **Performance tracking not working**
   - Check API endpoints are accessible
   - Verify CSRF token is included
   - Check database permissions

### Debug Mode

Enable debug mode in development:

```bash
GOOGLE_ADSENSE_DEBUG=true
```

This will log additional information to the browser console.

## Security Considerations

- All ad tracking endpoints include CSRF protection
- Rate limiting applied to admin configuration changes
- Input validation on all API endpoints
- User type verification for ad display logic

## Performance Optimization

- Configuration caching with Redis
- Lazy loading of ad scripts
- Responsive ad sizing
- Minimal impact on page load times

## Future Enhancements

- A/B testing for ad placements
- Advanced targeting based on user behavior
- Integration with Google Ad Manager
- Mobile app AdMob integration
- Real-time revenue reporting
