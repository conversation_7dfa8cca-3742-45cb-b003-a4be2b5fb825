# Page-Wise Ad Placement Guide

## Mobile Parts Database - Detailed Ad Implementation

### Overview

This document provides specific ad placement recommendations for each page type in the Mobile Parts Database application, optimized for user experience and revenue generation.

### Ad Zone Reference

- **Zone A**: Header Banner (728x90 desktop, 320x50 mobile)
- **Zone B**: Sidebar (160x600 or 300x600 desktop only)
- **Zone C**: In-Content (300x250 medium rectangle)
- **Zone D**: Footer Banner (728x90 desktop, 320x100 mobile)
- **Zone E**: Mobile-Specific (320x50, 300x250, 320x100)

### Page-Specific Implementation

#### 1. Home Page (`/`)

**Current Layout Analysis**

- Hero section with search interface
- Feature cards grid
- Pricing plans section
- Footer

**Recommended Ad Placement**

```
[Navigation Bar]
[Zone A: Header Banner] ← High visibility, above fold
[Hero Section with Search]
[Zone C: Rectangle Ad] ← Between hero and features
[Feature Cards Grid]
[Zone B: Sidebar] ← Desktop only, alongside content
[Pricing Plans Section]
[Zone D: Footer Banner] ← Before footer
[Footer]
```

**Mobile Layout**

```
[Navigation]
[Zone E: Mobile Banner]
[Hero Section]
[Zone E: Mobile Rectangle] ← After hero
[Feature Cards]
[Pricing Plans]
[Zone E: Mobile Banner] ← Before footer
[Footer]
```

**Implementation Notes**

- Delay Zone A by 3 seconds to allow search interface interaction
- Zone C should blend with feature cards design
- No ads for first-time visitors (grace period)

#### 2. Search Results (`/search`)

**Current Layout Analysis**

- Search filters sidebar
- Results grid/list
- Pagination
- Search statistics

**Recommended Ad Placement**

```
[Navigation + Search Bar]
[Zone A: Header Banner]
[Filters Sidebar] | [Results Grid + Zone B Sidebar]
                  | [Zone C: In-Results Ad every 5-7 items]
                  | [Pagination]
[Zone D: Footer Banner]
```

**Mobile Layout**

```
[Navigation]
[Zone E: Mobile Banner]
[Search Filters (collapsible)]
[Results List]
  - Result 1
  - Result 2
  - Result 3
  - [Zone E: Native Ad]
  - Result 4
  - Result 5
  - Result 6
  - [Zone E: Native Ad]
[Zone E: Mobile Banner]
```

**Implementation Notes**

- In-results ads should match result card styling
- Limit to maximum 3 in-content ads per page
- No ads if search returns fewer than 10 results

#### 3. Model Details (`/models/{model}`)

**Current Layout Analysis**

- Model header with image and basic info
- Specifications grid
- Compatible parts list
- Related models section

**Recommended Ad Placement**

```
[Navigation + Breadcrumbs]
[Zone A: Header Banner]
[Model Header Section]
[Zone C: Rectangle] ← Between header and specs
[Specifications Grid] | [Zone B: Sidebar]
[Compatible Parts List]
[Zone C: Rectangle] ← Between sections
[Related Models]
[Zone D: Footer Banner]
```

**Mobile Layout**

```
[Navigation]
[Zone E: Mobile Banner]
[Model Header]
[Zone E: Rectangle] ← After header
[Specifications]
[Zone E: Rectangle] ← Between sections
[Compatible Parts]
[Related Models]
[Zone E: Mobile Banner]
```

**Implementation Notes**

- Zone C ads should not interrupt specifications reading flow
- Sidebar ads can show related device accessories
- Consider contextual targeting based on device brand

#### 4. Part Details (`/parts/{part}`)

**Current Layout Analysis**

- Part header with images
- Technical specifications
- Compatibility table
- Related parts

**Recommended Ad Placement**

```
[Navigation + Breadcrumbs]
[Zone A: Header Banner]
[Part Header with Images]
[Zone C: Rectangle] ← After images, before specs
[Technical Specifications] | [Zone B: Sidebar]
[Compatibility Table]
[Zone C: Rectangle] ← Between sections
[Related Parts]
[Zone D: Footer Banner]
```

**Mobile Layout**

```
[Navigation]
[Zone E: Mobile Banner]
[Part Header + Images]
[Zone E: Rectangle] ← After images
[Technical Specs]
[Compatibility Table]
[Zone E: Rectangle] ← Between sections
[Related Parts]
[Zone E: Mobile Banner]
```

**Implementation Notes**

- High-value page for contextual ads (repair tools, accessories)
- Sidebar can show compatible parts from other brands
- Native ads in related parts section

#### 5. Dashboard (`/dashboard`)

**Current Layout Analysis**

- Statistics cards
- Recent activity feed
- Quick actions
- Search history

**Recommended Ad Placement**

```
[Sidebar Navigation] | [Main Content Area]
                     | [Zone A: Header Banner]
                     | [Statistics Cards]
                     | [Zone C: Rectangle] ← Between stats and activity
                     | [Activity Feed + Zone B: Sidebar]
                     | [Quick Actions]
                     | [Zone D: Footer Banner]
```

**Mobile Layout**

```
[Navigation]
[Zone E: Mobile Banner]
[Statistics Cards]
[Zone E: Rectangle] ← Between sections
[Activity Feed]
[Quick Actions]
[Zone E: Mobile Banner]
```

**Implementation Notes**

- Reduced ad frequency for engaged users
- Contextual ads based on search history
- Promote premium features subtly

#### 6. Brand Pages (`/brands/{brand}`)

**Current Layout Analysis**

- Brand header with logo
- Model listings grid
- Brand information
- Popular models section

**Recommended Ad Placement**

```
[Navigation + Breadcrumbs]
[Zone A: Header Banner]
[Brand Header Section]
[Zone C: Rectangle] ← After brand info
[Model Listings Grid] | [Zone B: Sidebar]
[Popular Models]
[Zone D: Footer Banner]
```

**Mobile Layout**

```
[Navigation]
[Zone E: Mobile Banner]
[Brand Header]
[Zone E: Rectangle] ← After header
[Model Listings]
[Popular Models]
[Zone E: Mobile Banner]
```

**Implementation Notes**

- Brand-specific contextual targeting
- Sidebar can show competitor comparisons
- Native ads in model listings

#### 7. Category Pages (`/categories/{category}`)

**Current Layout Analysis**

- Category header
- Subcategory navigation
- Parts/models grid
- Filters sidebar

**Recommended Ad Placement**

```
[Navigation + Breadcrumbs]
[Zone A: Header Banner]
[Category Header]
[Subcategory Navigation]
[Filters Sidebar] | [Content Grid + Zone B: Sidebar]
                  | [Zone C: In-Grid Ads every 8-10 items]
[Zone D: Footer Banner]
```

**Mobile Layout**

```
[Navigation]
[Zone E: Mobile Banner]
[Category Header]
[Subcategory Nav]
[Filters (collapsible)]
[Content Grid]
  - Item 1-3
  - [Zone E: Native Ad]
  - Item 4-6
  - [Zone E: Native Ad]
[Zone E: Mobile Banner]
```

**Implementation Notes**

- Category-specific ad targeting
- Native ads should match grid item styling
- Limit in-grid ads to maintain browsing flow

### Ad Frequency Rules

#### Global Rules

- Maximum 4 ads per page
- Minimum 500px spacing between ads
- No ads in first 30 seconds of session
- Reduced frequency for returning users

#### Page-Specific Rules

- **Home Page**: Maximum 3 ads, delayed loading
- **Search Results**: Maximum 4 ads, native integration
- **Detail Pages**: Maximum 4 ads, contextual placement
- **Dashboard**: Maximum 3 ads, user-behavior based

### Responsive Design Considerations

#### Desktop (1200px+)

- Full sidebar utilization
- Leaderboard banners
- Multiple ad zones active

#### Tablet (768px - 1199px)

- Sidebar ads hidden
- Mobile banner formats
- Reduced ad frequency

#### Mobile (< 768px)

- Mobile-optimized formats only
- Single-column layout
- Touch-friendly spacing

### Performance Optimization

#### Loading Strategy

- Lazy loading for below-fold ads
- Progressive enhancement
- Fallback for ad blockers
- Minimal impact on page speed

#### User Experience

- Native ad styling
- Clear ad labeling
- Non-intrusive placement
- Easy reporting mechanism

### A/B Testing Framework

#### Test Scenarios

1. **Ad Density**: 2 vs 3 vs 4 ads per page
2. **Placement**: Above vs below content sections
3. **Format**: Banner vs native vs video
4. **Timing**: Immediate vs delayed loading

#### Success Metrics

- Revenue per page view
- User engagement time
- Bounce rate impact
- Premium conversion rate

### Implementation Priority

#### Phase 1 (Week 1-2)

1. Home page ads
2. Search results ads
3. Basic admin controls

#### Phase 2 (Week 3-4)

1. Detail page ads
2. Dashboard ads
3. Performance tracking

#### Phase 3 (Week 5-6)

1. Brand/category page ads
2. Advanced targeting
3. A/B testing framework

This page-wise guide ensures optimal ad placement while maintaining the professional user experience across all sections of the Mobile Parts Database.
